%  Function    ：GenDataset_lbls
%  Description : 合并标注文件，按前缀均衡分配样本，划分为训练集、验证集和测试集
%  Parameter   : div_percentage       -- 数据集划分比例
%                folder_names         -- 各类型数据文件名

clear;
close all;
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量
%从base抽取数据总数
nSelCount = myconfig.nSelCount;
%训练集，验证集，测试集比例
div_percentage = myconfig.div_percentage; 
% 定义多个可替换的文件夹名
folder_names = myconfig.folder_names; % 可根据需要添加或修改文件夹名
folder_outputDS=myconfig.folder_outputDS;

% ================== 读取类别定义 ==================
fname_class = myconfig.fpath_classdef;
if ~exist(fname_class, "file")
    error("类别文件不存在: %s", fname_class);
end
Table_cls = readtable(fname_class, 'Format', '%d %s %d %f');
Table_cls.Properties.VariableNames = ["clsid", "clsname", "bw", "Tms"];
%%
for i = 1:length(folder_names)
    folder_name = folder_names{i};
    % ================== 初始化部分 ==================
    signalDB_dir= fullfile(folder_outputDS, folder_name);
   

    % 定义输出文件路径
    output_dir =fullfile(folder_outputDS, folder_name);
    sOut_train = fullfile(output_dir, 'Train_ds_gen.txt');    % 训练集
    sOut_val = fullfile(output_dir, 'Val_ds_gen.txt');        % 验证集
    sOut_test = fullfile(output_dir, 'Test_ds_gen.txt');      % 测试集

    % 确保输出目录存在
    if ~exist(output_dir, 'dir')
        mkdir(output_dir);
    end

    % ================== 初始化写入标志 ==================
    isFirstWrite = true;  % 文件首次写入标志

    % ================== 处理每个类别 ==================
    for iRow = 1:height(Table_cls)
        clsname = char(Table_cls.clsname(iRow));
        fname_txt = fullfile(signalDB_dir, clsname, 'wfile_list.txt');

        % 跳过不存在的文件
        if ~exist(fname_txt, "file")
            fprintf("!! 警告: 文件不存在 [%s]\n", fname_txt);
            continue;
        end

        % 读取当前类别数据
        curflines = readlines(fname_txt);
        nSamples = length(curflines)-1;
        fprintf("处理类别 ID=%d: %s (总样本=%d)\n", iRow, clsname, nSamples);

        % ===== 核心逻辑: 按前缀分组均衡分配 =====
        % --- 按前缀分组 ---
        prefixGroups = containers.Map();
        for j = 1:length(curflines)
            line = curflines{j};
            match = regexp(line, '(CJ[0-9]_[0-9]+[MK])_', 'tokens');
            if ~isempty(match)
                prefix = match{1}{1};
                if ~isKey(prefixGroups, prefix)
                    prefixGroups(prefix) = [];
                end
                prefixGroups(prefix) = [prefixGroups(prefix); j];
            else %手动添加的记录
            sExtra = "CJ_BYHAND";
            if ~isKey(prefixGroups, sExtra)
                prefixGroups(sExtra) = [];
            end
            prefixGroups(sExtra) = [prefixGroups(sExtra); j];
            end
        end
        prefixes = keys(prefixGroups);
        nPrefixes = length(prefixes);
        fprintf("检测到前缀: %s (共%d个前缀)\n", strjoin(prefixes, ', '), nPrefixes);

        % 根据文件夹名决定样本分配方式
        if strcmp(folder_name, 'base')
            if nPrefixes == 0
                warning("类别 %s 未检测到有效前缀，跳过处理", clsname);
                continue;
            end
            
            nPrefixes = 1;%change to one group mode
            totalindices = 1:nSamples;%exclude null line

            % --- 动态调整样本分配策略 ---
            if nSamples >= nSelCount
                % 总样本数足够，按原逻辑计算
                fprintf("总样本数足够，按目标数 %d 分配\n", nSelCount);
                samplesPerPrefix_train = int32(nSelCount * div_percentage(1) / nPrefixes);
                samplesPerPrefix_val = int32(nSelCount * div_percentage(2) / nPrefixes);
                samplesPerPrefix_test = int32(nSelCount * div_percentage(3) / nPrefixes);
            else
                % 总样本数不足，按实际样本数重新计算
                fprintf("总样本数不足，按实际样本数 %d 分配\n", nSamples);
                total_train = int32(nSamples * div_percentage(1));
                total_val = int32(nSamples * div_percentage(2));
                total_test = nSamples - total_train - total_val;  % 确保总和为nSamples
                
                samplesPerPrefix_train = int32(total_train / nPrefixes);
                samplesPerPrefix_val = int32(total_val / nPrefixes);
                samplesPerPrefix_test = int32(total_test / nPrefixes);
            end

            % --- 收集训练/验证/测试数据 ---
            [trainLines, valLines, testLines] = deal({});

            for k = 1:nPrefixes
                if nPrefixes==1
                    indices = totalindices';
                    nAvailable = length(indices);
                else
                    prefix = prefixes{k};
                    indices = prefixGroups(prefix);
                    nAvailable = length(indices);
                end

                % 训练集抽取
                nTrain = min(samplesPerPrefix_train, nAvailable);
                randIdx = indices(randperm(nAvailable));
                trainLines = [trainLines; curflines(randIdx(1:nTrain))];

                % 验证集抽取
                nValAvailable = nAvailable - nTrain;
                nVal = min(samplesPerPrefix_val, nValAvailable);
                if nVal > 0
                    valLines = [valLines; curflines(randIdx(nTrain+1:nTrain+nVal))];
                end

                % 测试集抽取
                nTestAvailable = nAvailable - nTrain - nVal;
                nTest = min(samplesPerPrefix_test, nTestAvailable);
                if nTest > 0
                    testLines = [testLines; curflines(randIdx(nTrain+nVal+1:nTrain+nVal+nTest))];
                end
            end

            % ===== 写入文件 =====
            writeMode = 'append';
            if isFirstWrite
                writeMode = 'overwrite';  % 首次写入使用覆盖模式
                fprintf('>> 创建新文件: %s\n', sOut_train);
            end

            % 写入训练集
            writelines(trainLines, sOut_train, 'WriteMode', writeMode);
            % 写入验证集
            writelines(valLines, sOut_val, 'WriteMode', writeMode);
            % 写入测试集
            writelines(testLines, sOut_test, 'WriteMode', writeMode);

        else
            % 如果不是 base 文件夹，所有样本作为训练集
            trainLines = curflines;
            writeMode = 'append';
            if isFirstWrite
                writeMode = 'overwrite';  % 首次写入使用覆盖模式
                fprintf('>> 创建新文件: %s\n', sOut_train);
            end
            writelines(trainLines, sOut_train, 'WriteMode', writeMode);
        end

        % 重置首次写入标志（仅在第一次写入后重置）
        if isFirstWrite
            isFirstWrite = false;
        end
    end
fprintf('\n===== 处理完成 =====\n');
fprintf('训练集: %s\n', sOut_train);
fprintf('验证集: %s\n', sOut_val);
fprintf('测试集: %s\n', sOut_test);
end