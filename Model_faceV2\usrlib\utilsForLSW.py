'''
这个文件用于随机生成LSW-pair,
针对0415的数据集：LSW总共留出了259条数据
'''

import random
import os

def get_lsw_pair_path(lsw_txt_path, seed_num, number_of_pair, same_class_ratio=0.7):
    """
    从LSW的txt文件中随机生成指定数量的LSW-pair，可以控制同类配对的比例
    
    Args:
        lsw_txt_path (str): LSW数据集的txt文件路径
        seed_num (int): 随机种子
        number_of_pair (int): 需要生成的pair数量
        same_class_ratio (float): 同类配对的比例，默认0.7
    
    Returns:
        str: 生成的pair文件路径
    """
    # 设置随机种子
    random.seed(seed_num)
    
    # 读取所有数据并按类别分组
    class_data = {}
    with open(lsw_txt_path, 'r') as f:
        for line in f:
            class_id = line.strip().split(';')[0]
            if class_id not in class_data:
                class_data[class_id] = []
            class_data[class_id].append(line.strip())
    
    # 计算同类和不同类的pair数量
    same_class_pairs_num = int(number_of_pair * same_class_ratio)
    diff_class_pairs_num = number_of_pair - same_class_pairs_num
    
    pairs = []
    
    # 生成同类pair
    for class_id, samples in class_data.items():
        if len(samples) >= 2:  # 确保该类别有足够的样本生成pair
            num_pairs = min(same_class_pairs_num // len(class_data), len(samples) // 2)
            for _ in range(num_pairs):
                pair_samples = random.sample(samples, 2)
                pair = f"{pair_samples[0]};{pair_samples[1]};0"  # 0表示同类
                pairs.append(pair)
    
    # 生成不同类pair
    class_ids = list(class_data.keys())
    for _ in range(diff_class_pairs_num):
        # 随机选择两个不同的类别
        class1, class2 = random.sample(class_ids, 2)
        sample1 = random.choice(class_data[class1])
        sample2 = random.choice(class_data[class2])
        pair = f"{sample1};{sample2};1"  # 1表示不同类
        pairs.append(pair)

    # 生成输出文件路径
    output_path = os.path.join(os.path.dirname(lsw_txt_path), f'lsw_pair_path-{seed_num}.txt')
    
    # 写入pair数据
    with open(output_path, 'w') as f:
        for pair in pairs:
            f.write(f"{pair}\n")
    
    return output_path


if __name__ == "__main__":
    lsw_txt_path = "/mnt/disk/lzg_doc/mydataset/2025_04_15/LSW_ds_gen19_list.txt"
    seed_num = 199
    number_of_pair = 150
    pair_path = get_lsw_pair_path(lsw_txt_path, seed_num, number_of_pair, same_class_ratio=0.5)
    print(f"生成的pair文件路径: {pair_path}")
