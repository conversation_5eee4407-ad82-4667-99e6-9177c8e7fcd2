%  Function    ：InitMyParams
%  Description : 初始化路径等
%
%  Author      : Liu<PERSON>guo
%  Date        : 2025-04-23

myconfig.folder_labeleddata    = "D:\label_data\2025-05-26";               %标签数据路径
myconfig.fpath_labeldef        = "D:\label_data\2025-05-26\label_def.txt";%label参数文件定义
myconfig.fpath_classdef        = "D:\label_data\2025-05-26\class_def.txt"; %类别定义文件
myconfig.folder_outputDS       = "D:\2025-05-23";               %输出数据集路径

myconfig.gennoised_datanum     = 10;                %从原始数据列表Train_records中抽取前n条数据作为噪声增强
myconfig.genaugment_datanum    = 3;                 %从原始数据列表Train_records中另外按每种距离抽取n条数据作为噪声增强

myconfig.noised_arrays         = 3:3:12;            %噪声增强信噪比变化范围
myconfig.dpos_arrays           = -3600:800:2000;    %位置增强修改起始位置可以向后或向前移动

myconfig.nSelCount             = 500;               %从base抽取数据总数
myconfig.div_percentage        = [0.8, 0.16, 0.04]; %训练集，验证集，测试集比例
myconfig.folder_names          = {'base', 'base_noised', 'base_augment', 'noised', 'augment'}; % 可根据需要添加或修改文件
myconfig.CaptureParseOut       = "outdataset\test\sam\fchanscan-S1.hdf5"; % cmdRuncapture 输出文件路径
myconfig.lswfileOut            = "./output/lsw_pair.txt";                 % cmdGenLSW_pair 输出文件路径