function [startpos_new] = findAccuractPos(x, startpos, offset)
%  Function    ：findAccuractPos
%  Description : 根据输入信号确定合适的有用信号起始位置
%  Parameter   : x        -- 输入信号
%                startpos -- 原起始位置
%                offset   -- 可能的偏移量
%
%  Return      :
%                startpos_new   -- 新的信号起始位置
%
%  Author      : Liuzhiguo
%  Date        : 2025-02-06
%
    %先通过谱熵/能量确定大概范围 startpos_new+-frame_step
    [startpos_range1, frame_step1] = findAccuractPosBySpectralEntropy(x, startpos, offset);
    [startpos_range, frame_step] = findAccuractPosBySpectralEntropy(x, startpos_range1, 2*frame_step1);%二次搜索
    %能量法
    %[startpos_range, frame_step] = findAccuractPosBySTEnergy(x, startpos, offset);
    %再通过阈值法确定更精确的位置
    [startpos_new] = findAccuractPosByThreshold(x, startpos_range, frame_step*2);
end

function [startpos_new, frame_step] = findAccuractPosBySpectralEntropy(x, startpos, offset)
%通过时域方法判断起始位置(谱熵范围法)
    if startpos-offset > 0 
        startpos_new = startpos-offset;
    else
        startpos_new = 1;
    end
    x_clip = x(startpos_new : startpos+offset-1);
    frame_length = floor(length(x_clip)/4);
    frame_step = floor(length(x_clip)/8);
    num_frames = floor((length(x_clip) - frame_length) / frame_step) + 1;
    spectral_entropy = zeros(num_frames, 1);

    % 分帧计算谱熵
    for i = 1:num_frames
        start_index = (i - 1) * frame_step + 1;
        end_index = start_index + frame_length - 1;
        frame = x_clip(start_index:end_index);
        spectrum = abs(fft(frame));
        spectrum = spectrum / sum(spectrum);
        spectral_entropy(i) = -sum(spectrum .* log2(spectrum + eps));
    end
    % % 设置谱熵阈值(此方法极其不准确)
    % entropy_threshold = 0.08 * max(spectral_entropy);
    % % 寻找信号的开始帧和结束帧
    % start_frame = find(spectral_entropy > entropy_threshold, 1, 'first');

    %求阶梯差最大值
    [~, index]=max(abs(diff(spectral_entropy)));
    start_frame = index + 1;%从第一个信号开始，差值从第2个信号开始，所以补偿1

    % 计算开始位置，偏差为frame_step
    start_index = (start_frame - 1) * frame_step + 1;
    % 计算开始位置
    startpos_new = startpos_new+start_index-1;
end

function [startpos_new, frame_step] = findAccuractPosBySTEnergy(x, startpos, offset)
%通过时域方法判断起始位置(短时能量法)
    if startpos-offset > 0 
        startpos_new = startpos-offset;
    else
        startpos_new = 1;
    end
    x_clip = x(startpos_new : startpos+offset-1);
    frame_length = floor(length(x_clip)/4);
    frame_step = floor(length(x_clip)/8);
    num_frames = floor((length(x_clip) - frame_length) / frame_step) + 1;
    st_energy = zeros(num_frames, 1);

    % 分帧计算短时能量
    for i = 1:num_frames
        start_index = (i - 1) * frame_step + 1;
        end_index = start_index + frame_length - 1;
        frame = x_clip(start_index:end_index);
        st_energy(i) = sum(abs(frame).^2)/frame_length;
    end
    %求阶梯差最大值
    [~, index]=max(abs(diff(st_energy)));
    start_frame = index + 1;%从第一个信号开始，差值从第2个信号开始，所以补偿1

    % 计算开始位置，偏差为frame_step
    start_index = (start_frame - 1) * frame_step + 1;
    % 计算开始位置
    startpos_new = startpos_new+start_index-1;
end

function [startpos_new] = findAccuractPosByThreshold(x, startpos, offset)
%通过时域方法判断起始位置（阈值）
    if startpos-offset > 0 
        startpos_new = startpos-offset;
    else
        startpos_new = 1;
    end
    x_abs = abs(x(startpos_new : startpos+offset-1));
    % % 设置阈值(偶尔会有较大偏差)
    % %threshold = 0.08 * max(x_abs); % 阈值可以根据实际情况调整
    % threshold = 0.08 * max(x_abs);
    % % 寻找信号的开始点和结束点
    % start_index = find(x_abs > threshold, 1, 'first');

    [~, start_index]=max(abs(diff(x_abs)));
    % 检查是否找到开始点和结束点
    if isempty(start_index)
        start_index = 1;
    end
    startpos_new = startpos_new+start_index;%*向前移动，防止漏掉起始点 -offset/8
end