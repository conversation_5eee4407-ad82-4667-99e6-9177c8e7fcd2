import math

def IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs):
    """
    函数    ：IsValidNarrowBW
    描述    : 依据窄带信号分布范围是否在宽带信号分布范围以内，判断是否为有效的窄带信号
    参数    : 
             wb_fc       -- 宽带信号中心频率
             wb_bw       -- 宽带信号带宽
             wb_fs       -- 宽带信号采样率 
             nb_fc       -- 窄带信号中心频率
             nb_bw       -- 窄带信号带宽 
             nb_fs       -- 窄带信号采样率 
    返回值  : 1:有效 -1:无效
    作者    : Liuzhiguo
    日期    : 2025-04-28
    """
    ret = 1
    if wb_bw < wb_fs:
        # 1. 计算宽带信号上下边界
        wb_bw_lb = wb_fc - wb_bw / 2
        wb_bw_ub = wb_fc + wb_bw / 2
        wb_fs_lb = wb_fc - wb_fs / 2
        wb_fs_ub = wb_fc + wb_fs / 2
    else:
        ret = -1
        return ret

    # 2. 计算窄带信号上下边界
    scale_factor = 1.2
    nb_bw_lb = nb_fc - math.floor((nb_bw * scale_factor) / 2)
    nb_bw_ub = nb_fc + math.floor((nb_bw * scale_factor) / 2)
    nb_fs_lb = nb_fc - math.floor(nb_fs / 2)
    nb_fs_ub = nb_fc + math.floor(nb_fs / 2)

    # 检查下边界 bw
    if nb_bw_lb < wb_bw_lb:
        ret = -1
        print(f"Error: nb_bw_lb={nb_bw_lb:.2f} < wb_bw_lb={wb_bw_lb:.2f}")
        return ret
    # 检查上边界 bw
    if nb_bw_ub > wb_bw_ub:
        ret = -1
        print(f"Error: nb_bw_ub={nb_bw_ub:.2f} > wb_bw_ub={wb_bw_ub:.2f}")
        return ret
    # 检查下边界 fs
    if nb_fs_lb < wb_fs_lb:
        ret = -1
        print(f"Error: nb_fs_lb={nb_fs_lb:.2f} < wb_fs_lb={wb_fs_lb:.2f}")
        return ret
    # 检查上边界 fs
    if nb_fs_ub > wb_fs_ub:
        ret = -1
        print(f"Error: nb_fs_ub={nb_fs_ub:.2f} > wb_fs_ub={wb_fs_ub:.2f}")
        return ret

    return ret
    