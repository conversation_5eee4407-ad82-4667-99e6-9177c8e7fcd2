# 小数据集测试工具(Testcase on Tinydataset)

## 功能描述
`testcase_on_tinydataset.py` 是一个专门用于信号分类预测测试的脚本，支持多种文件格式和测试模式。主要功能包括：

1. **多格式文件处理**：支持读取和处理多种格式的信号文件（.dat、.bvsp、.hdfv）
2. **三种运行模式**：
   - **TEST_MODE**：标准预测模式，处理.dat和.bvsp文件
   - **CLEAN_MODE**：数据集清洗模式，专门用于识别标注错误的.bvsp样本
   - **HDFV_MODE**：专门处理.hdfv格式文件的预测模式
3. **灵活的测试方式**：支持单文件测试和批量文件夹测试
4. **详细的结果报告**：自动生成预测结果CSV文件、错误日志和问题文件列表

## 输出文件结构
```
output/
├── dataset_clean_results/          # 清洗模式输出目录
│   ├── dataset_clean_results_MMDD_HHMM.csv    # 预测结果CSV文件
│   ├── dataset_clean_results_MMDD_HHMM.log    # 详细错误日志
│   └── dataset_clean_results_list_MMDD_HHMM.txt    # 问题文件列表
└── predict_results/                # 预测模式输出目录
    └── predict_results_MMDD_HHMM.csv    # 预测结果CSV文件
```

## 脚本配置参数

### 运行模式配置
```python
# 运行模式选择
class RunType:
    TEST_MODE = 0   # 标准预测模式（处理DAT和BVSP文件）
    CLEAN_MODE = 1  # 数据集清洗模式（仅处理BVSP文件）
    HDFV_MODE = 2   # HDFV文件处理模式

use_run_type = RunType.TEST_MODE  # 设置当前运行模式
```

### 数据集配置
```python
test_single_ds = False    # False: 批量测试, True: 单文件测试
dataset_name = 'fchanscan-S1.hdf5'  # 临时处理文件名
dataset_path = "数据集根目录路径"        # 批量测试的根目录
dataset_path_single = "单文件路径"       # 单文件测试路径
```

### 模型配置
```python
modeltype = 0  # 模型类型：0-分类模型，1-匹配模型
model_path = "模型文件路径"  # 深度学习模型路径

# 预测阈值配置（根据实际需求调整）
threshold_classify = np.ones(cls_count, dtype=float)*0.7  # 分类模型阈值
threshold_match = np.ones(cls_count, dtype=float)*0.8     # 匹配模型阈值
```

## 使用方法

### 1. 数据集清洗模式（CLEAN_MODE）
**用途**：识别训练数据集中标注错误的样本，生成清洗报告

**配置示例**：
```python
use_run_type = RunType.CLEAN_MODE
test_single_ds = False
dataset_path = R"C:\path\to\labeled\dataset"
```

**输出文件**：
- **CSV文件**：包含所有文件的预测结果详情
- **LOG文件**：仅记录预测错误的详细信息（真实类别≠预测类别）
- **LIST文件**：仅包含预测错误的文件路径列表，便于后续处理

**使用场景**：训练前数据质量检查，识别需要重新标注的样本

### 2. 标准预测模式（TEST_MODE）
**用途**：对新的信号数据进行分类预测，同时处理.dat和.bvsp文件

**配置示例**：
```python
use_run_type = RunType.TEST_MODE
test_single_ds = False
dataset_path = R"C:\path\to\test\data"
```

**输出文件**：
- **CSV文件**：包含所有预测结果统计信息

**使用场景**：模型性能评估，新数据预测分析

### 3. HDFV文件处理模式（HDFV_MODE）
**用途**：专门处理.hdfv格式的信号文件

**配置示例**：
```python
use_run_type = RunType.HDFV_MODE
test_single_ds = False
dataset_path = R"C:\path\to\hdfv\files"
```

**使用场景**：处理特定格式的信号数据文件

### 4. 单文件测试模式
**用途**：测试单个信号文件，便于调试和问题定位

**配置示例**：
```python
test_single_ds = True
dataset_path_single = R"C:\path\to\single\file.bvsp"
```

**使用场景**：单文件调试，问题样本分析

## 数据集组织结构
脚本要求数据集按以下结构组织：
```
dataset_root/
├── class1_name/
│   ├── file1.bvsp
│   ├── file2.dat
│   └── ...
├── class2_name/
│   ├── file1.bvsp
│   ├── file2.dat
│   └── ...
└── ...
```

## 输出文件字段说明

### CSV文件字段
- `File_Name`：原始文件名
- `True_ClassName`：真实类别名称（从文件夹名获取）
- `True_Label`：真实类别标签索引
- `Predicted_Label`：预测类别标签索引
- `Predicted_ClassName`：预测类别名称
- `Predicted_Props`：预测概率（百分比格式）
- `Predicted_Accuracy`：批次预测准确率
- `Prediction_Result`：预测结果（Correct/Incorrect）

### 清洗模式特有输出
- **LOG文件格式**：
  ```
  文件: C:\path\to\error\file.bvsp
  真实类别: nb_crossfire_gfsk
  预测类别: nb_RFD900X
  预测概率: 85.2%
  --------------------------------------------------
  ```
- **LIST文件格式**：每行一个预测错误的文件路径

## 运行示例

### 批量数据集清洗
```bash
# 1. 修改脚本配置
use_run_type = RunType.CLEAN_MODE
dataset_path = R"C:\datasets\labeled_data"

# 2. 运行脚本
python testcase_on_tinydataset.py

# 3. 查看输出文件
# output/dataset_clean_results/dataset_clean_results_0617_1430.csv
# output/dataset_clean_results/dataset_clean_results_0617_1430.log
# output/dataset_clean_results/dataset_clean_results_list_0617_1430.txt
```

### 单文件预测测试
```bash
# 1. 修改脚本配置
test_single_ds = True
dataset_path_single = R"C:\test\sample.bvsp"

# 2. 运行脚本
python testcase_on_tinydataset.py

# 3. 查看输出
# output/predict_results/predict_results_0617_1430.csv
```

### 批量预测测试
```bash
# 1. 修改脚本配置
use_run_type = RunType.TEST_MODE
dataset_path = R"C:\test_datasets"

# 2. 运行脚本
python testcase_on_tinydataset.py
```

## 支持的信号类型
脚本支持识别以下无人机遥控信号类型（根据模型训练数据确定）：
- nb_RFD900X
- nb_crossfire_gfsk
- nb_crossfire_lora
- nb_HITEC_FLAH8_2430
- nb_433M
- nb_radiolink_t8f8
- nb_radiolink_t16d
- nb_elrs_128x
- nb_frsky_td_fsk
- nb_frsky_tw_fsk
- nb_frsky_x9d2
- nb_futaba_sfhss
- nb_crossfire_ethix

## 特殊功能说明

### 阈值过滤机制
脚本会根据设置的阈值过滤预测结果：
- 只有预测概率超过对应类别阈值的结果才会被记录
- 可以通过调整`threshold_classify`数组来设置各类别的阈值
- 有助于减少低置信度的误报

### 自动时间戳命名
所有输出文件都会自动添加时间戳（MMDD_HHMM格式），避免文件覆盖

### 智能文件过滤
批量处理时会自动：
- 排除名称以`_out`结尾的文件夹
- 只处理属于已知类别名称的文件夹
- 根据文件扩展名选择对应的读取方式

## 注意事项
1. **模型文件**：确保模型文件路径正确且文件完整
2. **数据集结构**：数据集必须按类别分文件夹组织
3. **阈值设置**：根据实际需求调整各类别的预测阈值
4. **存储空间**：确保有足够磁盘空间存储输出文件
5. **文件权限**：确保脚本对输出目录有写入权限
6. **类别名称**：文件夹名称必须与模型训练时的类别名称一致

## 故障排除
1. **模型加载失败**：检查model_path路径和文件完整性
2. **数据读取错误**：验证文件格式和路径正确性
3. **预测概率异常**：检查阈值设置和模型兼容性
4. **输出文件为空**：检查是否有样本通过阈值过滤
5. **类别不匹配**：确认数据集文件夹名称与模型类别名称一致

## 依赖项
```python
import numpy as np
import torch
import csv
import os
from datetime import datetime
# 自定义模块
from chanlib.usrp_samp import init_args
from chanlib.func_scansig import proc_wbsig
from usrlib.usrlib import Read_sigfile
from usrlib.predict_proc import predict_classify_proc_all, load_classify_model
from usrlib.MyHelper import read_signal_from_hdf5
```

# 数据查看工具 (Data Viewer)

## 简介

这是一个基于Python和Tkinter的数据文件可视化工具，专门用于查看和分析信号数据文件（支持.bvsp、.dat、.hdfv格式）。

## 功能特点

- **多格式支持**：支持.bvsp、.dat、.hdfv数据文件
- **四种图表显示**：
  - 时域信号点值图（X轴以×10⁵为单位显示）
  - 时域信号图（时间轴，ms）
  - 频谱图（频率轴，MHz）
  - 时频图（时间-频率图）
- **2D/3D视图切换**（3D需要torch库）
- **文件导航**：支持文件夹浏览和前/后文件切换
- **智能抽样**：大数据量时自动抽样优化显示性能
- **多线程计算**：并行FFT和时频图计算

## 环境要求

### 必需依赖
- Python 3.6+
- numpy
- matplotlib
- scipy
- tkinter（通常随Python安装）

### 可选依赖
- torch（仅3D显示功能需要）

## 安装配置

### 1. 使用requirements.txt（推荐）
```bash
# 安装所有必需依赖
pip install -r requirements.txt

# 可选：如需3D功能，额外安装torch
pip install torch

# 运行程序
python data_viewer.py
```

### 2. Conda环境
```bash
# 如果有conda环境
conda activate your_env
pip install -r requirements.txt
python data_viewer.py
```

**自定义conda环境名**：
- 编辑 `start.bat` 文件
- 修改第8行：`set CONDA_ENV=your_env_name`
- 将 `your_env_name` 替换为您的实际conda环境名

### 3. 手动安装依赖
```bash
# 安装必需依赖
pip install numpy matplotlib scipy

# 可选：安装torch（用于3D显示）
pip install torch

# 运行程序
python data_viewer.py
```

## 使用方法

### 1. 启动程序

**方法一：使用启动脚本（推荐）**
```bash
start.bat
```
*注：启动脚本会自动激活conda环境 `py3.9`，如需修改环境名请参考配置说明*

**方法二：直接运行Python脚本**
```bash
python data_viewer.py
```

### 2. 打开文件
- 点击"选择文件"按钮选择单个数据文件
- 或点击"选择文件夹"浏览整个文件夹

### 3. 查看数据
- 程序会自动加载并显示四种图表
- 使用"上一个"/"下一个"按钮切换文件
- 点击"切换视图"在2D/3D模式间切换

### 4. 图表说明
- **时域信号点值图**：显示I/Q信号随采样点的变化（X轴：×10⁵采样点）
- **时域信号图**：显示I/Q信号随时间的变化（X轴：毫秒）
- **频谱图**：显示信号的频域特性（X轴：MHz）
- **时频图**：显示信号的时频特性（X轴：ms，Y轴：MHz）

## 支持的文件格式

### .bvsp文件
- 二进制信号捕获文件
- 包含完整的元数据信息（采样率、中心频率等）

### .dat文件
- 通用数据文件
- 使用默认参数加载

### .hdfv文件
- HDF格式文件
- 支持复杂数据结构

## 性能优化

- **智能抽样**：超过10万点时自动抽样显示
- **多线程计算**：FFT和时频图并行计算
- **内存优化**：大文件时分段处理
- **显示优化**：自动调整显示精度

## 故障排除

### 1. 程序无法启动
- 检查Python环境和依赖包
- 确认tkinter已安装

### 2. 3D功能不可用
- 安装torch：`pip install torch`
- 或只使用2D功能

### 3. 文件加载失败
- 检查文件格式是否支持
- 确认文件未损坏

### 4. 显示性能问题
- 程序会自动优化大文件显示
- 如遇卡顿，程序会切换到备用显示模式

### 5. 验证依赖安装

**验证方法**：
```python
import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import spectrogram
print("所有必需依赖已正确安装！")
```

或者直接运行启动脚本，如果conda环境和依赖都正确，程序会正常启动。

## 技术特性

- **MATLAB兼容**：频率轴和时频图显示与MATLAB对齐
- **精确计算**：使用float64精度确保频率计算准确性
- **自适应界面**：根据数据特性自动调整显示参数
- **容错设计**：多层错误处理和备用方案

## 文件结构

```
prj_predict_usrp/
├── data_viewer.py        # 主程序（数据查看工具）
├── mainproc.py          # 主处理程序
├── requirements.txt     # Python依赖包列表
├── start.bat            # 启动脚本（自动激活conda环境）
├── README.md            # 本说明文档
├── pathsetting.json     # 路径配置文件
├── fchanscan-S1.hdf5    # 示例数据文件
├── usrlib/              # 用户库目录
├── utils/               # 工具函数目录
├── chanlib/             # 通道库目录
├── nets/                # 网络模型目录
└── config/              # 配置文件目录
```

## 版本信息

当前版本已完成以下优化：
- ✅ 频率轴计算精度修复
- ✅ MATLAB显示对齐
- ✅ 时域信号点值图X轴单位优化（×10⁵）
- ✅ 自定义频率刻度显示
- ✅ 多线程性能优化
- ✅ 智能抽样和显示优化

---

**作者**：Caonairui
**更新日期**：2025-06-25 