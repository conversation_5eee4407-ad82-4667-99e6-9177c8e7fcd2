function [bindata] = DemodGfskWave(filename,spos,epos,nb_fc,kBaud,nSampPerSym,nStartPoint,nb_bw)
% 1. 初始化命令
%clc
close all

addpath("lib")     %库函数路径
addpath("usrlib\common\")  %用户自定义路径
addpath("usrlib\nb_433\"); 

nb_fs = kBaud*nSampPerSym;
if exist('nb_bw','var')==0
    nb_bw = floor(nb_fs/2);
end
%nb_bw = kBaud;

%fname_sign    = filename(end-5);
% kBaud = 64e3; %1.2e3*80;
% nb_bw   = kBaud*8;      % 窄带信号的带宽
%需要与 GenBBSig_nb433 中参数对应


% ischannelized=1;   % 是否信道化
% isengine = 1;      % 文件来源是否是引擎
[~,~,ext] = fileparts(filename);
if ext == ".wav"
    [wb_data, wb_fs] = audioread(filename);
    wb_signal = wb_data(:,1)+1j*wb_data(:,2);
    wb_fc = 2.4e9;%默认设置值
    wb_bw = 20e6;
elseif ext == ".hdfv" % 自定义波形文件
    [wb_signal, metadata] = readSignalFromHDF5(filename);
    wb_fs = metadata.fs;
    wb_fc = metadata.fc;
    wb_bw = metadata.bw;
else
    %[nb_rxSig,wb_rxSig] = readCapture(file,ischannelized,nb_bw,nb_fs,nb_fc,isengine);
    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filename);
end

if(exist('spos','var')>0 && exist('epos','var')>0)
    if spos==-1
    spos=1;
    epos=length(wb_signal); 
    end
else
    spos=1;
    epos=length(wb_signal); 
end
wb_signal = wb_signal(spos:epos);
showWBSig(wb_signal, wb_fc, wb_fs, filename);%显示宽带信号 - 时频域

if exist('nb_fc','var')==0
    nb_fc = wb_fc;
end

[nb_sig_td] = ExtractNBSig(wb_signal, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs);
%showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号 - 时频域
showNBSig(0, nb_sig_td, nb_fc, nb_fs);

[bindata] = dmodgfsk_byarcos(nb_sig_td, nSampPerSym, nStartPoint);
%dec2hex(rxSig_demoded);
% len_tx = length(rxSig_demoded);
% fprintf("解析数据: %d bytes\n", len_tx);
% fprintf('%02X ',rxSig_demoded);
% fprintf('\n');

end