function [crcBits]=crc_nb433(payloadBits)
gen_polynomial =[1 de2bi(hex2dec('8005'),length('8005')*8/2, 'left-msb')];
InitialConditions=de2bi(hex2dec('0000'),length('0000')*8/2, 'left-msb');
FinalXOR=de2bi(hex2dec('0000'),length('0000')*8/2, 'left-msb');
crcgenerator = comm.CRCGenerator(gen_polynomial,'InitialConditions',InitialConditions,'DirectMethod',true,'FinalXOR',FinalXOR);
[crcBits] = crcgenerator(payloadBits);
end