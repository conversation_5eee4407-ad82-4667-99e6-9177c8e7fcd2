# =======================================================================================================================
#   Function    ：TestModelNet.py
#   Description : 无人机信号调制类型识别网络测试
#                 
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-07-11
# =======================================================================================================================
from nets.SigAMRNet import SigAMRNet 
import torch

if __name__ == "__main__":
    model = SigAMRNet()
    x = torch.randn(64, 2, 1, 192)
    output = model(x)
    print(output.shape)  # (64, 11)