function [ret] = IsValidBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw)
if wb_bw>wb_fs
    %问题：如果宽带中心频点与窄带中心频点
    wb_bw_lb = wb_fc - wb_bw / 2;
    wb_bw_ub = wb_fc + wb_bw / 2;
else
    % 1. 计算宽带信号上下band
    wb_bw_lb = wb_fc - wb_fs / 2;%modified by lzg for bandwidth
    wb_bw_ub = wb_fc + wb_fs / 2;
end

% 2. 计算窄带信号上下band
nb_bw_lb = nb_fc - floor(nb_bw / 2);
nb_bw_ub = nb_fc + floor(nb_bw / 2);

ret = 0;
if nb_bw_lb < wb_bw_lb
    ret = -1;
    %nb_sig_fd = [];
    warning("Error: nb_bw_lb=%.2f < wb_bw_lb=%.2f",nb_bw_lb,wb_bw_lb);
    %return;
end
% Check upper-bound channel frequency
if nb_bw_ub > wb_bw_ub
    ret = 1;
    %nb_sig_fd = [];
    warning("Error: nb_bw_ub=%.2f < wb_bw_ub=%.2f", nb_bw_ub, wb_bw_ub);
    %return;
end
end