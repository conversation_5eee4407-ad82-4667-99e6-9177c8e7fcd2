#!/bin/bash

# 中文字体安装脚本
# Chinese Font Installation Script

echo "========================================="
echo "    中文字体安装脚本"
echo "    Chinese Font Installation Script"
echo "========================================="
echo ""

# 检测操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "检测到操作系统: $OS $VER"
echo ""

# 检查当前字体状态
echo "检查当前中文字体状态..."
python3 -c "
import platform

if platform.system() == 'Linux':
    try:
        import tkinter as tk
        import tkinter.font as tkFont

        # 创建临时根窗口以避免字体检查错误
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口

        available_fonts = list(tkFont.families())
        chinese_fonts = [
            'Noto Sans CJK SC', 'Source Han Sans SC', 'WenQuanYi Zen Hei',
            'WenQuanYi Micro Hei', 'Droid Sans Fallback', 'AR PL UMing CN'
        ]

        found_fonts = [font for font in chinese_fonts if font in available_fonts]

        # 销毁临时窗口
        root.destroy()

        if found_fonts:
            print(f'✓ 已安装的中文字体: {found_fonts}')
            exit(0)
        else:
            print('✗ 未找到合适的中文字体')
            exit(1)
    except Exception as e:
        print(f'字体检查失败: {e}')
        exit(1)
else:
    print('非Linux系统，无需安装')
    exit(0)
"

FONT_CHECK_RESULT=$?

if [ $FONT_CHECK_RESULT -eq 0 ]; then
    echo "中文字体已正确安装，无需重复安装。"
    exit 0
fi

echo ""
echo "需要安装中文字体..."
echo ""

# 根据不同的Linux发行版安装字体
case "$OS" in
    *Ubuntu*|*Debian*)
        echo "检测到 Ubuntu/Debian 系统"
        echo "正在安装中文字体包..."
        
        # 更新包列表
        echo "更新包列表..."
        sudo apt update
        
        # 安装字体包
        echo "安装 Noto CJK 字体..."
        sudo apt install -y fonts-noto-cjk
        
        echo "安装文泉驿字体..."
        sudo apt install -y fonts-wqy-zenhei fonts-wqy-microhei
        
        echo "安装 Droid 字体..."
        sudo apt install -y fonts-droid-fallback
        ;;
        
    *CentOS*|*Red\ Hat*|*Fedora*)
        echo "检测到 CentOS/RHEL/Fedora 系统"
        echo "正在安装中文字体包..."
        
        if command -v dnf &> /dev/null; then
            # Fedora/新版CentOS使用dnf
            echo "使用 dnf 安装字体..."
            sudo dnf install -y google-noto-sans-cjk-fonts
            sudo dnf install -y wqy-zenhei-fonts wqy-microhei-fonts
        elif command -v yum &> /dev/null; then
            # 旧版CentOS使用yum
            echo "使用 yum 安装字体..."
            sudo yum install -y google-noto-sans-cjk-fonts
            sudo yum install -y wqy-zenhei-fonts wqy-microhei-fonts
        else
            echo "错误: 找不到包管理器 (dnf/yum)"
            exit 1
        fi
        ;;
        
    *SUSE*|*openSUSE*)
        echo "检测到 SUSE/openSUSE 系统"
        echo "正在安装中文字体包..."
        
        sudo zypper install -y google-noto-sans-cjk-fonts
        sudo zypper install -y wqy-zenhei-fonts
        ;;
        
    *Arch*)
        echo "检测到 Arch Linux 系统"
        echo "正在安装中文字体包..."
        
        sudo pacman -S --noconfirm noto-fonts-cjk
        sudo pacman -S --noconfirm wqy-zenhei wqy-microhei
        ;;
        
    *)
        echo "未识别的Linux发行版: $OS"
        echo "请手动安装以下字体包之一:"
        echo "  - Noto Sans CJK SC"
        echo "  - WenQuanYi Zen Hei"
        echo "  - WenQuanYi Micro Hei"
        echo "  - Droid Sans Fallback"
        echo ""
        echo "或者尝试以下命令:"
        echo "  Ubuntu/Debian: sudo apt install fonts-noto-cjk fonts-wqy-zenhei"
        echo "  CentOS/RHEL: sudo yum install google-noto-sans-cjk-fonts wqy-zenhei-fonts"
        echo "  Fedora: sudo dnf install google-noto-sans-cjk-fonts wqy-zenhei-fonts"
        exit 1
        ;;
esac

# 刷新字体缓存
echo ""
echo "刷新字体缓存..."
fc-cache -fv

# 再次检查字体安装状态
echo ""
echo "验证字体安装..."

# 使用fc-list命令检查字体，避免tkinter的GUI依赖问题
python3 -c "
import subprocess
import platform

if platform.system() == 'Linux':
    try:
        # 使用fc-list命令检查字体
        result = subprocess.run(['fc-list', ':lang=zh'], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            chinese_fonts = [
                'Noto Sans CJK SC', 'Source Han Sans SC', 'WenQuanYi Zen Hei',
                'WenQuanYi Micro Hei', 'Droid Sans Fallback', 'AR PL UMing CN'
            ]

            found_fonts = []
            for font in chinese_fonts:
                font_check = subprocess.run(['fc-list', f':family={font}'], capture_output=True, text=True)
                if font_check.returncode == 0 and font_check.stdout.strip():
                    found_fonts.append(font)

            if found_fonts:
                print(f'✓ 字体安装成功! 可用字体: {found_fonts}')
                exit(0)
            else:
                # 检查是否有任何中文字体
                if result.stdout.strip():
                    print('✓ 检测到中文字体支持，但可能不是预期的字体名称')
                    print('可用的中文字体:')
                    for line in result.stdout.strip().split('\n')[:5]:  # 显示前5个
                        font_name = line.split(':')[1].split(',')[0] if ':' in line else line
                        print(f'  - {font_name}')
                    exit(0)
                else:
                    print('✗ 未检测到中文字体支持')
                    exit(1)
        else:
            print('✗ 无法检查字体状态，fc-list命令失败')
            exit(1)
    except Exception as e:
        print(f'✗ 字体检查出错: {e}')
        exit(1)
else:
    print('非Linux系统，跳过字体验证')
    exit(0)
"

FINAL_CHECK_RESULT=$?

echo ""
if [ $FINAL_CHECK_RESULT -eq 0 ]; then
    echo "========================================="
    echo "✓ 中文字体安装完成!"
    echo "现在可以正常使用信道扫描查看工具了。"
    echo "========================================="
else
    echo "========================================="
    echo "✗ 字体安装可能未完全成功"
    echo "建议重启系统后再次运行此脚本验证。"
    echo "========================================="
fi

echo ""
echo "脚本执行完成。"
