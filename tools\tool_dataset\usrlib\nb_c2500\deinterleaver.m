function [out_bin] = deinterleaver(bindata)
%  Function    ：deinterleaver
%  Description : 行列交织
%                参考文献《Design Note DN504，FEC Implementation》, Figure 2. FEC and Interleaving
%  Parameter   : bindata  -- 输入数据    (Bin)
%                out_bin  -- 输出数据    (Bin)
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-21

bindata_2bit=bit2int(bindata, 2);%以2bit为单位
out_array = [];
for i=1:length(bindata_2bit)/16
    s = (i-1)*16;
    c=matintrlv(bindata_2bit(s+1:s+16),4,4);
    a_out = flip(c');
    out_array = [out_array, a_out];
end
%转换为bin
out_array1 = int2bit(out_array,2,true);

n_len = length(out_array1);
out_bin = reshape(out_array1,[n_len*2,1]);

end