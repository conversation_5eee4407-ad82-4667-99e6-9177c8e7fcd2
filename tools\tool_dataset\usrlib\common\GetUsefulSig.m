function [startPos, endPos] = GetUsefulSig(sig, threshold, nSampPerSym,nStartpos, nEndpos)
%  Function    ：GetUsefulSig
%  Description : 根据threshold判定有用信号范围
%                
%  Parameter   : sig         -- 输入信号    (I数据或Q数据中的1路)
%                threshold   -- 门槛值
%                nSampPerSym -- 每符号采样点数
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-19

startPos = nStartpos;
endPos   = 0;
len_sig  = length(sig);

for i=nStartpos:nEndpos
    if (startPos == nStartpos) && (startPos >1) && (abs(sig(i)) > threshold)  
        startPos = i-1;
    elseif (startPos > nStartpos) && (abs(sig(i))<threshold) && (abs(sig(i+1))<threshold) && (mean(abs(sig(i:i+20))) < threshold)
        endPos = i+1;
        break;
    elseif i==nEndpos
        endPos = nEndpos;
        break;
    elseif i==len_sig 
        endPos = len_sig;  
        break;
    end        
end

nRound = ceil((endPos-startPos+1)/nSampPerSym);%圆整计算
endPos = startPos+nRound*nSampPerSym-1;        %得到结束位置  

