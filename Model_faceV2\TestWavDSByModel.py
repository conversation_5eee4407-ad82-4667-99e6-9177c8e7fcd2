# =======================================================================================================================
#   File        : TestWavDSByModel.py
#   Description : 数据集测试测试脚本
#   Features    : 1. 支持多种文件格式（.dat、.bvsp、.hdfv）的读取和处理
#                 2. 支持单文件夹测试和批量测试
#                 3. 自动生成测试报告和问题文件列表
#   Author      : Caonairui
#   Date        : 2025-06-18
# =======================================================================================================================

import numpy as np
import os
import h5py
from datetime import datetime
import torch
import time
import csv
from nets.arcface import Arcface
from usrlib.usrlib import *
from usrlib.usrlib import Read_sigfile
from enum import Enum
import scipy.io.wavfile
from utils.dataloader import SigNbDataset, LSWDataset, SigNbDataset_collate
from torch.utils.data import DataLoader

# ===================== 文件类型枚举 =====================
class FileType(Enum):
    """文件类型枚举"""
    BVSP = 1  # BVSP文件
    HDF5 = 2  # HDF5文件
    WAV = 3   # WAV文件


# ===================== 配置参数 =====================
# 数据文件目录路径
file_dir = R"C:\Users\<USER>\Documents\Project\datafiles\03-GenDataset\2025-06-12\base\nb_elrs_128x"
# 模型文件路径
model_path = "logs/Mtype0-ep100-loss0.009-val_loss0.043.pth"
# 文件类型设置
file_type = FileType.WAV

# 读取路径配置文件
clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local = read_path_config()
clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
cls_ids, cls_names, cls_count = get_classes(clsdef_file)

def load_classify_model(model_path=model_path):
    """
    加载分类模型
    
    Args:
        model_path (str): 模型文件路径
        
    Returns:
        tuple: (模型, 类别数量, 类别ID列表, 类别名称列表)
    """
    modeltype = 0  # 模型类别 0:分类 1:角向量
    print('无人机类别个数:', cls_count)
    
    t1 = time.time()
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)
    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    
    return Model


def list_wav_files(folder_path):
    """
    列出目录中的所有WAV文件，并返回与read_dataset_lines相同格式的数据
    
    Args:
        folder_path (str): 目录路径
        
    Returns:
        list: 格式为 ["label;filepath"] 的列表
    """
    wav_lines = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.wav'):
                # 从目录名获取标签
                class_name = os.path.basename(os.path.dirname(os.path.join(root, file)))
                label = cls_names.index(class_name) if class_name in cls_names else 0
                
                # 构建与read_dataset_lines相同格式的行
                wav_path = os.path.join(root, file)
                line = f"{label};{wav_path}"
                wav_lines.append(line)
    return wav_lines


def process_directory(input_dir, output_dir="output", file_type=FileType.WAV):
    """
    处理目录中的所有文件，进行批量预测
    
    Args:
        input_dir (str): 输入目录路径
        output_dir (str): 输出目录路径
        file_type (FileType): 文件类型
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成带时间戳的文件名（只包含月日_时分）
    timestamp = datetime.now().strftime("%m%d_%H%M")
    csv_filename = os.path.join(output_dir, f"predict_results_{timestamp}.csv")
    check_filename = os.path.join(output_dir, f"prediction_errors_{timestamp}.log")
    list_filename = os.path.join(output_dir, f"error_files_{timestamp}.txt")
    
    # 如果文件已存在，先删除
    for filename in [csv_filename, check_filename, list_filename]:
        if os.path.exists(filename):
            os.remove(filename)
    
    # 获取所有数据文件
    if file_type == FileType.WAV:
        data_files = list_wav_files(input_dir)
    else:
        print(f"当前版本仅支持WAV文件类型")
        return
    
    print(f"找到 {len(data_files)} 个数据文件")
    
    # 加载模型
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return
    
    model = load_classify_model(model_path)
    if model is None:
        return
    
    # 处理所有文件
    LenNorm = 512*46
    test_dataset = SigNbDataset(data_files)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False, collate_fn=SigNbDataset_collate)
    
    print(f"开始处理 {len(data_files)} 个文件...")
    
    # 统计错误信息
    error_count = 0
    
    # 处理每个文件
    for batch_idx, (data, labels) in enumerate(test_loader):
        try:
            # 显示进度条
            progress = (batch_idx + 1) / len(data_files) * 100
            print(f"\r处理进度: [{batch_idx + 1}/{len(data_files)}] {progress:.1f}%", end="", flush=True)
            
            # 将数据移到GPU
            data = data.cuda()
            
            # 进行预测
            with torch.no_grad():
                outputs = model(data)
                predictions = outputs.softmax(dim=1)
            
            # 获取预测结果
            pred_labels = torch.argmax(predictions, dim=1)
            pred_probs = torch.max(predictions, dim=1)[0]
            
            # 检查预测结果
            for i, (true_label, pred_label, pred_prob) in enumerate(zip(labels, pred_labels, pred_probs)):
                true_classname = cls_names[true_label]
                pred_classname = cls_names[pred_label]
                
                # 如果预测错误，记录到日志
                if true_classname != pred_classname:
                    error_count += 1
                    
                    # 如果文件不存在，创建文件
                    if not os.path.exists(check_filename):
                        with open(check_filename, 'w', encoding='utf-8') as f:
                            f.write(f"数据集清洗报告 - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write("="*80 + "\n\n")
                    
                    if not os.path.exists(list_filename):
                        open(list_filename, 'w', encoding='utf-8').close()
                    
                    # 写入详细日志
                    with open(check_filename, 'a', encoding='utf-8') as f:
                        file_path = data_files[batch_idx]
                        f.write(f"文件: {file_path}\n")
                        f.write(f"真实类别: {true_classname}\n")
                        f.write(f"预测类别: {pred_classname}\n")
                        f.write(f"预测概率: {pred_prob.item():.4f}\n")
                        f.write("-"*50 + "\n")
                    
                    # 写入问题文件列表
                    with open(list_filename, 'a', encoding='utf-8') as f:
                        file_path = data_files[batch_idx]
                        f.write(f"{file_path}\n")
                
                # 保存预测结果到CSV
                # 提取相对路径：从完整路径中提取类别文件夹名和文件名
                full_path = data_files[batch_idx]
                if ';' in full_path:
                    # 如果是 "label;path" 格式，只取路径部分
                    file_path = full_path.split(';')[1]
                else:
                    file_path = full_path
                
                # 只保留文件名
                file_name = os.path.basename(file_path)
                
                result = {
                    'File_Name': file_name,
                    'True_ClassName': true_classname,
                    'True_Label': true_label.item(),
                    'Predicted_Label': pred_label.item(),
                    'Predicted_ClassName': pred_classname,
                    'Predicted_Props': f"{pred_prob.item() * 100:.1f}%",
                    'Predicted_Accuracy': "Correct" if true_classname == pred_classname else "Incorrect",
                    'Prediction_Result': "Correct" if true_classname == pred_classname else "Incorrect"
                }
                
                # 写入CSV文件
                file_exists = os.path.isfile(csv_filename)
                with open(csv_filename, 'a', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=result.keys())
                    if not file_exists:
                        writer.writeheader()
                    writer.writerow(result)
            
        except Exception as e:
            print(f"\n处理文件 {batch_idx} 时出错: {str(e)}")
    
    print(f"\n\n所有文件处理完成！")
    print(f"处理文件总数: {len(data_files)}")
    print(f"预测错误数量: {error_count}")
    print(f"结果保存在: {csv_filename}")
    if error_count > 0:
        print(f"预测错误记录已保存到: {check_filename}")
        print(f"问题文件列表已保存到: {list_filename}")


def main():
    """
    主函数：执行数据集批量预测
    """
    print("="*60)
    print("无人机信号分类模型测试脚本")
    print("="*60)
    
    # 处理目录中的所有文件
    process_directory(
        input_dir=file_dir,
        output_dir="output",
        file_type=file_type
    )


if __name__ == "__main__":
    main() 