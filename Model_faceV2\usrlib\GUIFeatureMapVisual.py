'''
这个文件用来展示当前的匹配模型输出特征与当前信号特征库之间的差距的
'''

import torch
import numpy as np
import matplotlib
matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import seaborn as sns
from nets.arcface import Arcface
from usrlib.dataloader import LoadHdfsDataset, DatasetFolder_eval
from utils.dataloader import LSWDataset
from usrlib.VectorToVectorSim import *
from usrlib.usrlib import *
import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QComboBox
from PyQt5.QtCore import Qt

class FeatureMatchViewer(QMainWindow):
    def   __init__(self, avector, vectors, predictions, clsids=None, classid_gt=None):
        super().__init__()
        
        # 保存输入数据
        self.avector = avector  # 信号库特征 shape: (num_classes, feature_dim)
        self.vectors = vectors  # 待匹配信号特征 shape: (num_samples, feature_dim)
        self.predictions = predictions  # 预测结果
        self.clsids = clsids  # 类别ID
        self.classid_gt = classid_gt  # 真实标签
        
        # 计算预测的类别索引
        if 'pred_label' in self.predictions:
            self.pred_labels = self.predictions['pred_label'][:, 0]  # 取top-1预测
        
        # 设置当前显示的样本索引
        self.current_sample = 0
        self.total_samples = self.vectors.shape[0]
        
        # 设置窗口
        self.setWindowTitle("特征匹配可视化器")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建UI
        self.setup_ui()
        
        # 显示第一个样本
        self.update_plot()
    
    def setup_ui(self):
        """设置GUI界面"""
        # 创建中心部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        
        # 添加导航按钮
        self.prev_button = QPushButton("上一个 (←)")
        self.next_button = QPushButton("下一个 (→)")
        self.prev_button.clicked.connect(self.previous_sample)
        self.next_button.clicked.connect(self.next_sample)
        
        # 添加样本选择下拉框
        self.sample_selector = QComboBox()
        for i in range(self.total_samples):
            label = f"Sample {i}"
            if self.classid_gt is not None:
                label += f" (True Label: {self.classid_gt[i]})"
            self.sample_selector.addItem(label)
        self.sample_selector.currentIndexChanged.connect(self.on_sample_selected)
        
        # 添加信息标签
        self.info_label = QLabel()
        
        # 将控件添加到控制面板
        control_layout.addWidget(self.prev_button)
        control_layout.addWidget(self.next_button)
        control_layout.addWidget(self.sample_selector)
        control_layout.addWidget(self.info_label)
        control_layout.addStretch()
        
        # 创建matplotlib图形
        self.fig = plt.figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.fig)
        
        # 将控件添加到主布局
        layout.addWidget(control_panel)
        layout.addWidget(self.canvas)
        
        # 绑定键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Left:
            self.previous_sample()
        elif event.key() == Qt.Key_Right:
            self.next_sample()
    
    def update_plot(self):
        """更新特征图显示"""
        # 清除之前的图像
        self.fig.clear()
        
        # 获取当前样本的特征
        sample_feature = self.vectors[self.current_sample]
        
        # 获取预测的类别
        pred_idx = self.pred_labels[self.current_sample]
        pred_label = self.clsids[pred_idx] if self.clsids is not None else pred_idx
        
        # 获取真实类别（如果有）
        true_label = None
        if self.classid_gt is not None:
            true_label = self.classid_gt[self.current_sample]
        
        # 获取预测类别的特征
        pred_class_feature = self.avector[pred_idx]
        
        # 获取真实类别的特征（如果有）
        true_class_feature = None
        if true_label is not None:
            # 找到真实类别对应的索引
            true_idx = np.where(self.clsids == true_label)[0]
            if len(true_idx) > 0:
                true_class_feature = self.avector[true_idx[0]]
        
        # 创建子图
        if true_class_feature is not None:
            grid = (2, 2)
        else:
            grid = (1, 2)
        
        # 绘制样本特征
        ax1 = self.fig.add_subplot(grid[0], grid[1], 1)
        im1 = ax1.imshow(sample_feature.reshape(1, -1), aspect='auto', cmap='coolwarm')
        ax1.set_title(f'Sample Feature (current index: {self.current_sample})')
        plt.colorbar(im1, ax=ax1)
        
        # 绘制预测类别特征
        ax2 = self.fig.add_subplot(grid[0], grid[1], 2)
        im2 = ax2.imshow(pred_class_feature.reshape(1, -1), aspect='auto', cmap='coolwarm')
        ax2.set_title(f'Predict (class: {pred_label})')
        plt.colorbar(im2, ax=ax2)
        
        # 如果有真实类别，绘制真实类别特征
        if true_class_feature is not None:
            ax3 = self.fig.add_subplot(grid[0], grid[1], 3)
            im3 = ax3.imshow(true_class_feature.reshape(1, -1), aspect='auto', cmap='coolwarm')
            ax3.set_title(f'True Label (Class: {true_label})')
            plt.colorbar(im3, ax=ax3)
            
            # 绘制特征差异
            ax4 = self.fig.add_subplot(grid[0], grid[1], 4)
            diff = sample_feature - true_class_feature
            im4 = ax4.imshow(diff.reshape(1, -1), aspect='auto', cmap='coolwarm')
            ax4.set_title('Sample Feature-True Label')
            plt.colorbar(im4, ax=ax4)
        
        # 设置整体标题
        match_status = ""
        if true_label is not None:
            match_status = f" | Predict {'Correct' if pred_label == true_label else 'False'}"
        
        sim_score = self.predictions['pred_score'][self.current_sample, 0]
        self.fig.suptitle(f'Sample {self.current_sample}/{self.total_samples-1} | ' +
                          f'Predict class: {pred_label} | Cosine similarity: {sim_score:.4f}' +
                          match_status, fontsize=16)
        
        # 更新信息标签
        self.info_label.setText(f"Sample: {self.current_sample}/{self.total_samples-1} | " +
                               f"Predict class: {pred_label}" +
                               (f" | True Label: {true_label}" if true_label is not None else ""))
        
        # 更新下拉框
        self.sample_selector.setCurrentIndex(self.current_sample)
        
        # 调整布局
        self.fig.tight_layout(rect=[0, 0, 1, 0.95])
        
        # 刷新画布
        self.canvas.draw()
    
    def previous_sample(self):
        """显示上一个样本"""
        if self.current_sample > 0:
            self.current_sample -= 1
            self.update_plot()
    
    def next_sample(self):
        """显示下一个样本"""
        if self.current_sample < self.total_samples - 1:
            self.current_sample += 1
            self.update_plot()
    
    def on_sample_selected(self, index):
        """当从下拉框选择样本时调用"""
        if index != self.current_sample:
            self.current_sample = index
            self.update_plot()

def plot_match_feature(avector, vectors, predictions, clsids=None, classid_gt=None):
    """
    绘制匹配特征的图像
    
    Args:
        avector: 信号库特征向量 shape: (num_classes, feature_dim)
        vectors: 待匹配的信号特征向量 shape: (num_samples, feature_dim)
        predictions: 预测结果，包含pred_label和pred_score
        clsids: 类别ID数组
        classid_gt: 真实标签数组
    """
    # 确保输入是numpy数组
    if isinstance(avector, torch.Tensor):
        avector = avector.detach().cpu().numpy()
    if isinstance(vectors, torch.Tensor):
        vectors = vectors.detach().cpu().numpy()
    
    # 启动GUI应用
    app = QApplication(sys.argv)
    viewer = FeatureMatchViewer(avector, vectors, predictions, clsids, classid_gt)
    viewer.show()
    sys.exit(app.exec_())