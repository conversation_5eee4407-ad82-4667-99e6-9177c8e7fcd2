function [bindata] = dmodgfsk_byarcos(xdata, nSamp, nstartpos)
%  Function    ：demodfsk_signal 通过arctan 
%  Description : 利用反正切方式依次计算频率变化率
%                参考：https://blog.csdn.net/qq_41332806/article/details/111311196
%  Parameter   : xdata    -- 输入数据    (I/Q)
%                out_bin  -- 输出数据    (Bin)
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-21

nLen = length(xdata);
m_data = zeros(nLen-1, 1);%频率值 phase = k*integer(m_data)
%得到了I路和Q路信号之后，再对 I路,Q路信号的比值求反正切 ，即可得到基带信号。这个求解m(n)序列的过程就是鉴相。
for i=2:length(xdata)
    m_data(i-1)=real(xdata(i-1))*imag(xdata(i))-real(xdata(i))*imag(xdata(i-1));
    norm_value = abs(xdata(i));
    m_data(i-1) = m_data(i-1)/norm_value;
end
mean_data = mean(m_data(nstartpos:nstartpos+nSamp*32));
m_data = m_data - mean_data;
% 相位变化
%arctan_xdata = atan(imag(xdata)./real(xdata));
arctan_xdata = angle(xdata(nstartpos:nSamp:end));%计算角度
% mean_data = mean(arctan_xdata(nstartpos:nstartpos+5));
% arctan_xdata = arctan_xdata - mean_data;

% 判断起始位置
bShowfig = 1;
if bShowfig==true
    figure(123);
    subplot(3,1,1);
    plot(m_data);
    title('频率调制值 线图');
    subplot(3,1,2);
    plot(arctan_xdata,'.');
    title('相位角 点图');
    subplot(3,1,3);
    plot(m_data>0);ylim([-1,2]);
    title('频率解调值');
end

bindata = (m_data(nstartpos:nSamp:end)>0)';

len_tx = floor(length(bindata)/8);
outbytes_org=bi2de(reshape(bindata(1:len_tx*8)',8,len_tx)', 'left-msb');
fprintf('\n[dmodgfsk_byarcos] 收到信号:%d bytes\n', length(outbytes_org));
fprintf('%02X ',outbytes_org);
fprintf('\n');


end



function [out_bin] = bin2bytes_leftmsb(bindata)
out_bin = [];
for i=1:floor(length(bindata)/8)
    byte_bin = bindata((i-1)*8+1:i*8);

    byte_value = 0;
    for j=1:8
        if byte_bin(j)>0
            add_value = 1;
        else
            add_value = 0;
        end

        byte_value = byte_value*2 + add_value;
    end
    out_bin = [out_bin byte_value];
end
end