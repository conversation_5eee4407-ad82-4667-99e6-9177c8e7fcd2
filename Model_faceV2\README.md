# 数据集分类测试脚本

## 概述

`TestWavDSByModel.py` 是一个用于对WAV格式的无人机信号数据集进行批量预测和结果分析的脚本。

## 功能描述

该脚本用于对WAV格式的无人机信号数据进行分类预测测试，支持批量处理和结果分析。主要功能包括：

1. **WAV文件批量预测**：支持对大量WAV格式的无人机信号文件进行批量分类预测
2. **实时进度监控**：提供处理进度显示和统计信息，实时反馈处理状态
3. **结果分析报告**：自动生成详细的预测结果CSV文件和错误分析报告
4. **错误检测与记录**：识别预测错误的文件，生成问题文件列表和详细错误日志
5. **多格式输出**：支持生成CSV结果文件、错误分析日志和问题文件列表三种输出格式
6. **性能优化**：采用GPU加速推理，支持批处理模式，提高处理效率

## 文件结构

```
Model_faceV2/
├── TestWavDSByModel.py    # 主脚本文件
├── README.md              # 说明文档
├── logs/                  # 模型文件目录
│   └── Mtype0-ep100-loss0.009-val_loss0.043.pth
├── output/                # 输出文件目录
│   ├── predict_results_MMDD_HHMM.csv
│   ├── prediction_errors_MMDD_HHMM.log
│   └── error_files_MMDD_HHMM.txt
└── nets/                  # 模型网络定义
    └── arcface.py
```

## 配置说明

### 1. 路径配置

在脚本开头修改以下配置参数：

```python
# 数据文件目录路径
file_dir = R"C:\Users\<USER>\Documents\Project\datafiles\03-GenDataset\2025-06-12\base\nb_elrs_128x"

# 模型文件路径
model_path = "logs/Mtype0-ep100-loss0.009-val_loss0.043.pth"

# 文件类型设置
file_type = FileType.WAV
```

### 2. 数据目录结构

确保数据目录按以下结构组织：

```
datafiles/03-GenDataset/2025-06-12/base/
├── nb_elrs_128x/
│   ├── CJ0_1M_462-1.wav
│   ├── CJ0_1M_462-2.wav
│   └── ...
├── nb_crossfire_ethix/
│   ├── CJ0_1M_627-1.wav
│   └── ...
└── other_drone_types/
    └── ...
```

### 3. 类别定义文件

确保 `class_def.txt` 文件存在并包含正确的类别定义：

```
0 nb_elrs_128x 128000 6.0
1 nb_crossfire_ethix 128000 6.0
2 other_drone_type 128000 6.0
...
```

## 输出文件说明

### 1. 预测结果文件 (predict_results_MMDD_HHMM.csv)

包含所有文件的预测结果，字段说明：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| File_Name | 文件名 | CJ0_1M_462-1.wav |
| True_ClassName | 真实类别名 | nb_elrs_128x |
| True_Label | 真实类别ID | 0 |
| Predicted_Label | 预测类别ID | 0 |
| Predicted_ClassName | 预测类别名 | nb_elrs_128x |
| Predicted_Props | 预测置信度 | 95.2% |
| Predicted_Accuracy | 预测准确性 | Correct/Incorrect |
| Prediction_Result | 预测结果 | Correct/Incorrect |

### 2. 错误分析日志 (prediction_errors_MMDD_HHMM.log)

详细记录预测错误的文件信息：

```
数据集清洗报告 - 生成时间: 2025-06-18 13:54:32
================================================================================

文件: 1;C:\path\to\file.wav
真实类别: nb_elrs_128x
预测类别: nb_crossfire_ethix
预测概率: 0.8542
--------------------------------------------------
```

### 3. 问题文件列表 (error_files_MMDD_HHMM.txt)

包含所有预测错误文件的路径列表，便于后续处理。

## 使用方法

### 1. 环境准备

确保已安装以下依赖：

```bash
pip install torch numpy h5py scipy
```

### 2. 运行脚本

```bash
cd Model_faceV2
python TestWavDSByModel.py
```

### 3. 运行示例

```
============================================================
无人机信号分类模型测试脚本
============================================================
无人机类别个数: 8
load model：0.30s
找到 150 个数据文件
开始处理 150 个文件...
处理进度: [150/150] 100.0%

所有文件处理完成！
处理文件总数: 150
预测错误数量: 12
结果保存在: output\predict_results_0618_1354.csv
预测错误记录已保存到: output\prediction_errors_0618_1354.log
问题文件列表已保存到: output\error_files_0618_1354.txt
```

## 性能优化

### 1. 批处理设置

当前脚本设置为 `batch_size=1`，每次处理一个文件。如需提高处理速度，可以：

- 增加 `batch_size` 值（需要确保GPU内存足够）
- 使用多进程处理
- 优化数据加载流程

### 2. 内存管理

- 脚本使用 `torch.no_grad()` 减少内存占用
- 及时释放不需要的变量
- 使用进度条避免大量输出占用内存

## 故障排除

### 1. 常见错误

**模型文件不存在**
```
错误: 模型文件不存在: logs/Mtype0-ep100-loss0.009-val_loss0.043.pth
```
解决方案：检查模型文件路径是否正确

**类别定义文件不存在**
```
错误: 类别定义文件不存在
```
解决方案：确保 `class_def.txt` 文件存在且格式正确

**GPU内存不足**
```
CUDA out of memory
```
解决方案：减少 `batch_size` 或使用CPU模式

**注意**：使用前请确保数据文件格式正确，模型文件完整，并备份重要数据。 