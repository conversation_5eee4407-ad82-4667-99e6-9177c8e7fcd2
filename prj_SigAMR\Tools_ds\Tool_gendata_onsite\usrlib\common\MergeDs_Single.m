function []=MergeDs_Single(fname_class, nRecCount, signalDB_dir)
%  Function    ：MergeDs_Single
%  Description : 将signalDB下各个子文件夹中标注文件合并成
%                一个统一的文件
%
%  Parameter   : fname_class       -- 类别文件名称
%                nRecCount         -- 记录数     （可选）
%                signalDB_dir      -- 信号库路径 （可选）
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-27
if exist("nRecCount","var")<=0
    nRecCount = 20;
end

if exist(signalDB_dir,"var")>0
    if exist(signalDB_dir,"dir")<=0
        error("%s 不存在\n", signalDB_dir);
    end
else
    [signalDB_dir,~,~]=fileparts(fname_class);
    signalDB_dir = strcat(signalDB_dir,'\');
end


if exist(fname_class,"file")>0
    Table_cls = readtable(fname_class,'Format','%d %s %d');
    Table_cls.Properties.VariableNames = ["clsid","clsname","bw"];
else
    error("文件：%s 不存在！", fname_class);
end

sOutfilename_train = '.\output\Train_ds_all.txt';
fprintf('Write file %s\n',sOutfilename_train);
for iRow = 1 : height(Table_cls)
    foldername = char(Table_cls(iRow,2).clsname);
    fname_txt = strcat(signalDB_dir,foldername,'\Train_records.txt');% 类别文件
    if exist(fname_txt,"file")>0
        curflines = readlines(fname_txt);
        fprintf("Append file ID=%d, name: %s, nrows=%d\n",iRow, fname_txt, length(curflines));
        if iRow==1
            writelines(curflines(1:nRecCount),sOutfilename_train,'WriteMode','overwrite');
        else
            writelines(curflines(1:nRecCount),sOutfilename_train,'WriteMode','append');
        end
    else
        fprintf("文件：%s 不存在！\n", fname_txt);
    end
end

end