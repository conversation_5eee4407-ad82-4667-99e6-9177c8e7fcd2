
clc
clear all
%get signal
file = '/home/<USER>/work/autel/EVO_V1/1.bvsp';
addpath('/home/<USER>/work/matlab/lib');
% file = 'polaris_resave.dat'

packet = load_sigcap('5787-22.dat');
snr = 50;
ischanneliz=1;
isengine = 1;
nb_bandwidth = 10e6;    
nb_samprate = 15.36e6;
nb_center_freq = 2472e6;
autel_evo_v1 = readCapture(file,ischanneliz,nb_bandwidth,nb_samprate,nb_center_freq,isengine);
autel_evo_v1 = autel_evo_v1(1:packet.header.polling_period);

v = uint8(read_byte_binary('5787-22.dat'));

drone_indicator = typecast(v(89:92), 'uint32');
polaris_center_freq = typecast(v(77:80), 'uint32');
center_freq = double(typecast(v(57:60), 'int32')) * 1000;
samp_rate = double(typecast(v(49:52), 'int32'));
bandwidth = double(typecast(v(53:56), 'int32'));
v(89:92) = typecast(uint32(13),'uint8');
v(77:80) = typecast(uint32(nb_center_freq/1000),'uint8');
v(57:60) = typecast(int32(nb_center_freq/1000),'uint8');
v(49:52) = typecast(int32(nb_samprate),'uint8');
v(53:56) = typecast(int32(nb_bandwidth),'uint8');


header_length =  double(typecast(v(5:6), 'uint16'));
data_vb = v(header_length+1:end);
raw_data_u16 = typecast(data_vb, 'uint16');

%%% get iq high 4bit
data_s16 = typecast(raw_data_u16, 'uint16');
high_4bit = bitshift(data_s16, -12, 'uint16');
high_4bit_i = high_4bit(1:2:end);
high_4bit_q = high_4bit(2:2:end);
low_12bit_q = [];
low_12bit_i = [];
low_12bit = [];
for n=1:length(high_4bit_i)/length(autel_evo_v1)
    low_12bit_i = [low_12bit_i; round(awgn(real(autel_evo_v1),50,'measured'))];
    low_12bit_q = [low_12bit_q; round(awgn(imag(autel_evo_v1),50,'measured'))];
end
    low_12bit_i = [low_12bit_i; round(real(autel_evo_v1(1:mod(length(high_4bit_i),length(autel_evo_v1)))))];
    low_12bit_q = [low_12bit_q; round(imag(autel_evo_v1(1:mod(length(high_4bit_i),length(autel_evo_v1)))))];
    
low_12bit(1:2:length(high_4bit)) = low_12bit_i;
low_12bit(2:2:length(high_4bit)) = low_12bit_q;
low_12bit = typecast(typecast(int16(low_12bit), 'int16'),'uint16');
low_12bit = bitshift(low_12bit, 4, 'uint16');
low_12bit = bitshift(low_12bit, -4, 'uint16');

data_s16 = typecast(bitor(low_12bit,bitshift(high_4bit',12,'uint16')),'int16');



% % Remove 4 MSBits from raw data and convert to floatdata_s16 = typecast(raw_data_u16, 'int16');
% data_s16 = bitshift(data_s16, 4, 'int16');
% data_s16 = bitshift(data_s16, -4, 'int16');
% data_floats = double(data_s16);
% data = data_floats(1:2:end) + 1j*data_floats(2:2:end);
% 
% signal = round(awgn(data,snr,'measured'));
% resave_data=zeros(2*length(signal),1);
% resave_data(1:2:end)=real(signal);
% resave_data(2:2:end)=imag(signal);


v0 = typecast(int16(data_s16'), 'uint8');
vv=[v(1:header_length) ;v0];
fid = fopen ('polaris_resave.dat', 'wb');
fwrite(fid, vv, 'uint8');
fclose(fid);

packet = load_sigcap('polaris_resave.dat');
figure(123)
plot(real(packet.data))
