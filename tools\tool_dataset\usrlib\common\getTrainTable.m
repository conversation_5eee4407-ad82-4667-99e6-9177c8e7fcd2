function [Table_train]=getTrainTable(fname_label)
if exist(fname_label,"file")>0
    Table_train = readtable(fname_label,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    Table_train.lenClipPoints = Table_train.lenPoints;
    Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", fname_label);
end