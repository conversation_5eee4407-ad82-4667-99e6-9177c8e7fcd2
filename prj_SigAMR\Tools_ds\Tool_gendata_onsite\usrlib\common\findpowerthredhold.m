function [min_clippower]=findpowerthredhold(wave_data)
%
% 以切片方式寻找最小rms片段功率值
%
window_size = 1000*2; % 窗口采样点数
num_windows = floor(length(wave_data) / window_size);%窗口格式
min_clippower = 0;
for i=1:num_windows
    start_idx = (i-1) * window_size + 1;
    end_idx = i * window_size;
    window = wave_data(start_idx:end_idx);
    rms_clippower = sqrt(mean(abs(window).^2));%RMS
    if i==1
        min_clippower = rms_clippower;
    else
        if rms_clippower < min_clippower
            min_clippower = rms_clippower;
        end
    end
end

end

