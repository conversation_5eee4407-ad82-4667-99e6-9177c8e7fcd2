function [signal, metadata] = readSignalFromHDF5(filepath)
% 从 HDF5 文件读取信号数据和元信息
%
% 输入:
%   filepath - 要读取的文件路径
%
% 输出:
%   signal - 包含信号数据的复数数组
%   metadata - 包含元信息的结构体

% 初始化输出参数
signal = [];
metadata = struct();

% 检查文件是否存在
if ~exist(filepath, 'file')
    error('文件不存在: %s', filepath);
end

try
    % 打开 HDF5 文件进行读取
    h5Info = h5info(filepath);
    
    % 读取信号数据
    signal = h5read(filepath, '/iq_samples');
    
    % 转换为复数数组
    signal = complex(signal.r, signal.i);
    
    % 读取元数据
    groupInfo = h5info(filepath, '/');
    attrNames = {groupInfo.Attributes.Name}';
    
    % 提取每个属性
    for i = 1:length(attrNames)
        attrName = attrNames{i};
        metadata.(attrName) = h5readatt(filepath, '/', attrName);
    end
    
    % 转换时间戳为 MATLAB 日期格式（如果存在）
    if isfield(metadata, 'timestamp')
        try
            % 尝试解析 ISO 格式的时间戳
            metadata.timestamp = datetime(metadata.timestamp, 'InputFormat', 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS');
        catch
            warning('无法解析时间戳: %s', metadata.timestamp);
        end
    end
    
    fprintf('成功从 %s 读取信号数据\n', filepath);
    
catch ME
    warning(E.indentifer, '读取文件时出错: %s', ME.message);
end
end