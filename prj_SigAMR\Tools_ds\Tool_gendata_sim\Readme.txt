		数据生成工具说明
==================仿真生成调制识别数据集各函数声明================================

目前生成五种调制方式：BPSK、QPSK、16QAM、GFSK和Lora

仿真生成信号时，第一步 在datasetconfig.m中配置信号参数和输出文件路径设置；
               第二步 利用Gendataset_main.m 仿真生成信号,同时将数据集按照比例保存为训练集、验证集和测试集
                
1. moddatasve_h5file.m--------------将仿真生成的五种调制信号数组另存为.h5文件
2. spiltset_saveh5.m----------------将数据集划分为训练集、测试集和验证集，并另存为.h5文件
3. datasetconfig.m------------------参数配置文件(包括信号参数设置和文件路径设置)
4. Gendataset_main.m----------------信号生成主文件
5. helperModClassGetSource.m---------------生成特定长度信号流，如BPSK是01比特流，QPSK是[0,1,2,3]数据流
6. helperModClassGetModulator.m------------封装各类调制函数
7. helperModClassTestChannel.m-------------定义莱斯信道模型(多径衰落+高斯白噪声)
8. random_crop.m---------------------------以固定长度裁剪信号
注：如果后期添加或删减调制方式，需要改动"3.datasetconfig.m" || "5.helperModClassGetSource.m" ||"6. helperModClassGetModulator.m"



                     "数据集分布方式":
                    |=================|
                       调制类型mod1   
                    |-----------------|
                   |+|   信噪比0 dB  |+| 
                    |-----------------|
                    |    样本序号1    |
                    |         .       |
                    |         .       |
                    |         .       |
                    |    样本序号N    |
                    |-----------------|
                   |+|   信噪比2 dB  |+| 
                    |-----------------|
                    |         .       |
                    |         .       |
                    |         .       |
                    |-----------------|
                    |+|   信噪比20dB  |+| 
                    |=================|
                       调制类型mod2   
                    |-----------------|
                    |         .       |
                    |         .       |
                    |         .       |
                    |=================|
                       调制类型modN   
                    |=================|
s