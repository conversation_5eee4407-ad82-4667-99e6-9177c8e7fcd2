function [] = ShowTimeAndSpectral(signal_in, fc, fs,stitle,window)
%SHOWSPECTROGRAM 此处显示有关此函数的摘要
%   此处显示详细说明

if ~exist('window','var')
    window = 4096;
end
figure;
subplot(4,1,1);
plot(real(signal_in))
hold on
plot(imag(signal_in))
xlabel('Points'); ylabel('I/Q Value');title(stitle);

subplot(4,1,2);
tDelta = (0:length(signal_in)-1)*1000/fs;%ms
plot(tDelta, real(signal_in))
hold on
plot(tDelta, imag(signal_in))
xlabel('t(ms)'); ylabel('I/Q Value');title(stitle);

subplot(4,1,3);
sig_len = length(signal_in);
nb_samps = sig_len;
fftdata = fftshift(abs(fft(signal_in))/sig_len);
xfreqs=fc/1e6 - fs/1e6/2 +(0 : nb_samps - 1)/nb_samps*fs/1e6;
plot(xfreqs,fftdata);
subtitle(['频谱图 fc= ' num2str(fc/1000000) 'Mhz' '  fs= ' num2str(fs/1000000) 'Mhz']);

subplot(4,1,4);
%window = 4096;%4096;
noverlap = window/2;
nfft = window;
g = hann(nfft,"symmetric");%,window
%centered
epsion = 1e-15;
[S,F,T,P]=spectrogram(signal_in, g, noverlap, nfft, fs,'yaxis');       %频谱 瀑布图
F = (-nfft/2:1:nfft/2-1)*(fs/nfft/1e6)+fc/1e6;%0.015=fs/4096/1e6;
%F = (-2048:1:2047)*0.015+fc/1e6;
% 将数组 P 中的 0 值替换为 1e-6
P(P == 0) = epsion;
surf(T*1000,F,10*log10(circshift(P,nfft/2)),'edgecolor','none'); axis tight;
%surf(T*1000,F,circshift(P,nfft/2),'edgecolor','none'); axis tight;
view(0,90);
xlabel('Time (ms)'); ylabel('MHz');
title(['瀑布图  fs= ' num2str(fs/1e6) 'Mhz ' 'fc= ' num2str(fc/1e6) 'Mhz' ' FFTLen=' num2str(nfft)]);
end

