import torch
import numpy as np
import matplotlib

matplotlib.use('Qt5Agg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import seaborn as sns
from nets.arcface import Arcface
from utils.dataloader import LSWDataset
from usrlib.usrlib import *
import sys
import torch.nn.functional as F
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import Qt


class LSWFeatureViewer(QMainWindow):
    def __init__(self, model_path, lsw_pairs_path):
        super().__init__()
        self.setWindowTitle("LSW特征对可视化器")
        self.setGeometry(100, 100, 1000, 400)

        # 初始化模型和数据
        self.init_model(model_path)
        self.load_lsw_data(lsw_pairs_path)
        self.current_batch = 0

        # 创建GUI
        self.setup_ui()

        # 显示第一对特征
        self.update_plot()

    def init_model(self, model_path):
        """初始化模型"""
        clsdef_dir, dataset_dir = read_path_config()
        clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
        cls_ids, cls_names, cls_count = get_classes(clsdef_file)

        self.model = Arcface(num_classes=cls_count, backbone="DroneSigNet", ModelType=1)
        Init_model(self.model, True, model_path)
        self.model.eval()
        self.model = self.model.cuda()

    def load_lsw_data(self, lsw_pairs_path):
        """加载LSW数据"""
        with open(lsw_pairs_path, "r") as f:
            self.lines_lsw = f.readlines()

        if sys.platform.startswith('linux'):
            clsdef_dir, dataset_dir = read_path_config()
            windows_to_linux_path(dataset_dir[6], dataset_dir[7], self.lines_lsw)

        self.LSW_loader = torch.utils.data.DataLoader(
            LSWDataset(lines_lsw=self.lines_lsw),
            batch_size=1,
            shuffle=False
        )
        self.total_batches = len(self.LSW_loader)

    def setup_ui(self):
        """设置GUI界面"""
        # 创建中心部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 创建控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)

        # 添加按钮
        self.prev_button = QPushButton("Previous (←)")
        self.next_button = QPushButton("Next (→)")
        self.prev_button.clicked.connect(self.previous_batch)
        self.next_button.clicked.connect(self.next_batch)

        # 添加信息标签
        self.progress_label = QLabel()
        self.similarity_label = QLabel()

        # 将控件添加到控制面板
        control_layout.addWidget(self.prev_button)
        control_layout.addWidget(self.next_button)
        control_layout.addWidget(self.progress_label)
        control_layout.addWidget(self.similarity_label)
        control_layout.addStretch()

        # 创建matplotlib图形
        self.fig, (self.ax1) = plt.subplots(1, 1, figsize=(15, 8))
        self.canvas = FigureCanvas(self.fig)

        # 将控件添加到主布局
        layout.addWidget(control_panel)
        layout.addWidget(self.canvas)

        # 绑定键盘事件
        self.setFocusPolicy(Qt.StrongFocus)

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Left:
            self.previous_batch()
        elif event.key() == Qt.Key_Right:
            self.next_batch()

    def update_plot(self):
        """更新特征对显示"""
        # 清除之前的图像
        self.ax1.clear()
        #self.ax2.clear()
        #self.fig.clear()

        # 获取当前batch的数据
        data_iter = iter(self.LSW_loader)
        for i in range(self.current_batch + 1):
            data_a, data_p, label = next(data_iter)

        # 获取文件信息
        line = self.lines_lsw[self.current_batch].replace("\n", "")
        ida, path_a, idp, path_p = line.split(';')

        # 提取特征
        with torch.no_grad():
            data_a, data_p = data_a.cuda(), data_p.cuda()
            out_a = self.model(data_a)
            out_p = self.model(data_p)
            similarity = F.cosine_similarity(out_a, out_p).item()
        # 转换为numpy数组
        feat_a = out_a.detach().cpu().numpy()
        feat_b = out_p.detach().cpu().numpy()
        feat = np.concatenate((feat_a, feat_b),axis=0)
        # 设置相同的值范围
        vmin = feat.min()
        vmax = feat.max()

        # 绘制热图
        self.ax1.imshow(feat, cmap='coolwarm', vmin=vmin, vmax=vmax)
        #self.ax2.imshow(feat_b.reshape(1, -1), cmap='viridis', vmin=vmin, vmax=vmax)

        # 设置标题
        self.ax1.set_title(f'A:{ida}:-{os.path.basename(path_a)} |||| P:{idp}:-{os.path.basename(path_p)}')
        #self.ax2.set_title(f'Positive (Class {idp})\n{os.path.basename(path_p)}')

        # 更新标签
        self.progress_label.setText(f"Data: {self.current_batch + 1}/{self.total_batches}")
        self.similarity_label.setText(f"Cosine Similarity: {similarity:.4f}")

        # 刷新画布
        self.canvas.draw()

        # 打印信息
        print(f"\n对比分析 #{self.current_batch}:")
        print(f"Anchor类别: {ida}")
        print(f"Positive类别: {idp}")
        print(f"标签: {label.item()}")
        print(f"余弦相似度: {similarity:.4f}")
        print("-" * 50)

    def previous_batch(self):
        """显示上一对特征"""
        if self.current_batch > 0:
            self.current_batch -= 1
            self.update_plot()

    def next_batch(self):
        """显示下一对特征"""
        if self.current_batch < self.total_batches - 1:
            self.current_batch += 1
            self.update_plot()


def run_gui_viewer():
    """启动GUI可视化器"""
    model_path = "logs/model_epoch_80.pth"
    lsw_pairs_path = "/home/<USER>/projects/Model_face_d/model_data/lsw_pair.txt"
    app = QApplication(sys.argv)
    viewer = LSWFeatureViewer(model_path, lsw_pairs_path)
    viewer.show()  # 显示窗口
    sys.exit(app.exec_())


def run_terminal_viewer():
    """启动终端交互版本的可视化器"""
    model_path = "logs/model_epoch_30.pth"
    lsw_pairs_path = "/home/<USER>/projects/Model_face_d/model_data/lsw_pair.txt"

    # 初始化模型和数据，类似于LSWFeatureViewer类中的初始化
    # 这里省略具体实现...

    current_batch = 0
    total_batches = len(open(lsw_pairs_path).readlines())

    while True:
        # 显示当前batch的特征对
        # 这里需要实现类似于update_plot的功能
        # 但不使用GUI，可以使用matplotlib直接显示图像

        command = input("\n输入命令 (n: 下一对, p: 上一对, q: 退出, 数字: 跳转到指定batch): ")

        if command.lower() == 'q':
            break
        elif command.lower() == 'n':
            if current_batch < total_batches - 1:
                current_batch += 1
        elif command.lower() == 'p':
            if current_batch > 0:
                current_batch -= 1
        elif command.isdigit():
            batch_num = int(command)
            if 0 <= batch_num < total_batches:
                current_batch = batch_num
            else:
                print(f"批次号必须在0和{total_batches - 1}之间")


if __name__ == "__main__":
    # 选择运行GUI版本
    run_gui_viewer()

    # 或者选择运行终端交互版本（取消注释下面的行并注释上面的行）
    #run_terminal_viewer()