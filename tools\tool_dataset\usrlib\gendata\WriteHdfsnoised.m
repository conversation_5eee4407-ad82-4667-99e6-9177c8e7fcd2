function []=WriteHdfsnoised()
%  Function    ：WriteHdfsnoised
%  Description : 写入噪声训练数据
%  Parameter   : fname_rd       -- 写入文件名称
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13

% 1. 初始化命令
clc
clear
close all

addpath("lib");             %库函数路径
addpath("usrlib\common");   %用户自定义路径

% 2. 文件读入
% 2.1 文件目录读取
inpath = ".\outdataset\train\base\";
%inpath = 'E:\project\prj_sigClassify\Data\train\base\' %测试用
%inname = "nb-dataset-train";
%inname = "nb-dataset-val";
cls_size = 'S1';

%fname_indslist = dir(strcat(inpath,inname,'-*','.hdf5'));
fname_indslist = dir(strcat(inpath,'nb-dataset-train*','.hdf5'));%只生成训练集

for i=1:length(fname_indslist)
    fname_indataset = strcat(fname_indslist(i).folder,'\',fname_indslist(i).name);
    disp(strcat("--------输入文件:",fname_indataset,"--------"));
    GenNoisedDS(fname_indataset);
end

function [] = GenNoisedDS(fname_indataset)
%4 添加噪声干扰
rowpointer = 0;
for snr =  3:3:20
    fprintf("生成噪声数据：snr=%.2f\n",snr);

    [rd_sig,class_id,class_name, arrayfc, arrayfs, arraybw] = RdTrainSig(fname_indataset);
    fname_outdataset = strrep(fname_indataset,'base','noised');
    for iRow = 1 : length(class_id)
        cls_id = class_id(1,iRow);
        cls_name = class_name(1,iRow);
        fc = arrayfc(1,iRow);
        fs = arrayfs(1,iRow);
        bw = arraybw(1,iRow);

        signal = rd_sig(1,:,iRow)+1j*rd_sig(2,:, iRow);
        wb_rxSig_clip = awgn(signal, snr, 'measured');
        rowpointer = rowpointer+1;
        WrTrainSig(fname_outdataset, wb_rxSig_clip, cls_id,cls_name, fc, fs, bw, rowpointer);  % 生成数据集，hdf文件格式
    end
end
%DispDatasetRecByChart(fname_dataset_noised,1,"噪声");
end


end