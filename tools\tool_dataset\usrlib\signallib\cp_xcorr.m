function [cpcorr,cpcorravg]=cp_xcorr(sig,cp_len,fft_len,samplesPerSlot)

    nFFT = fft_len;
    cpLength = cp_len;

    arm1=sig(1:end-nFFT);
    arm2=sig(1+nFFT:end);

    % Conjugate multiply the inputs and integrate over the cyclic
    % prefix length.
    cpcorrunfilt=arm1.*conj(arm2);
    cpcorr=conv(cpcorrunfilt,ones(cpLength,1));
    cpcorr=cpcorr(cpLength:end);  
    
    %sum power

    sumpow =conv(power(abs(arm1),2),ones(cpLength,1));
    sumpow=sumpow(cpLength:end);
    sumpowavg=sumpow(1:(fix(length(sumpow)/samplesPerSlot)*samplesPerSlot)); 
    sumpowavg=sum(reshape(sumpowavg,samplesPerSlot,length(sumpowavg)/samplesPerSlot),2);  

    % Average the estimates by combining all available slots.
    cpcorravg=cpcorr(1:(fix(length(cpcorr)/samplesPerSlot)*samplesPerSlot)); 
    cpcorravg = abs(cpcorravg);
    cpcorravg=sum(reshape(cpcorravg,samplesPerSlot,length(cpcorravg)/samplesPerSlot),2);  
    figure(111)
    subplot(311)
    plot(abs(cpcorr))  
    subplot(312)
    plot(abs(cpcorravg)) 
    subplot(313)
    plot(abs(cpcorravg./sumpowavg))
    
    
        
end
