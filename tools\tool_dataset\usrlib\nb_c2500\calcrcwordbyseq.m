function [outval] = calcrcwordbyseq(txBuffer)
%  Function    ：calcrcwordbyseq
%  Description : 计算CRC16值，通关输入序列
%                参考文献《Design Note DN502， CRC Implementation》，模式为：Normal mode CRC
%  Parameter   : txBuffer -- 输入数据    (UINT8)
%                outval -- 输出checksum值 (UINT16)
%  Author      : Liuzhiguo
%  Date        : 2024-06-20
checksum = hex2dec('FFFF');%初始值
for i=1:length(txBuffer)
    checksum = culCalcCRC(txBuffer(i), checksum);
end
outval = checksum;
end

