function []=Transhdfv2dat(sfilename)
% ho = rx_data(2:2:end);%uint8字节类型
% lo = rx_data(1:2:end);
% 
% samp_rate_hz = str2num(get(handles.rx_samp_freq,'String'));
% bandwidth_hz = str2num(get(handles.rx_rf_bw,'String'));
% center_freq_khz = str2double(get(handles.rx_lo_freq,'String'))*1000;
[wb_signal, metadata] = readSignalFromHDF5(sfilename);
samp_rate_hz = metadata.fs;
center_freq_khz = metadata.fc;
bandwidth_hz = metadata.bw;
nLen = length(wb_signal);
%rx_data = zeros(nLen*4, "uint8");% complex16=4 bytes
rx_data = complex2array(wb_signal);
[sfPath, sfName, sExt]  = fileparts(sfilename);
sOutfName = strcat(sfName, ".dat");
creatEngineFile('version.mat',sOutfName,rx_data,samp_rate_hz,bandwidth_hz,center_freq_khz);
end

function little_endian_array=complex2array(complex_signal)
% 提取实部和虚部
real_part = real(complex_signal);
imag_part = imag(complex_signal);

% 对实部和虚部分别进行归一化处理并转换为uint8类型
real_uint16 = normalize_to_uint16(real_part);
imag_uint16 = normalize_to_uint16(imag_part);

% 创建小端格式的uint16数组（先存实部字节，再存虚部字节）
% 每个复数对应两个uint16值，实部在前，虚部在后
little_endian_array = zeros(4 * length(complex_signal), 1, 'uint8');
for i = 1:length(complex_signal)
    little_endian_array(4*i-3) = bitand(real_uint16(i),255);  % 实部字节（低字节）
    little_endian_array(4*i-2) = uint8(bitshift(real_uint16(i), -8));  % 实部字节（低字节）
    little_endian_array(4*i-1) = bitand(imag_uint16(i),255);    % 虚部字节（高字节）
    little_endian_array(4*i) = uint8(bitshift(imag_uint16(i), -8));    % 虚部字节（高字节）
end
end

% 归一化函数，将数据映射到0-255范围
function normalized = normalize_to_uint16(data)
    % 找出数据的最小值和最大值
    min_val = min(data(:));
    max_val = max(data(:));
    
    % 处理特殊情况：如果最大值和最小值相等，111直接返回全128的数组
    if max_val == min_val
        normalized = uint8(2^11 * ones(size(data)));
        return;
    end
    
    % 归一化到0-1范围
    normalized_float = (data - min_val) / (max_val - min_val);
    
    % 映射到0-255范围并转换为uint8类型
    normalized = uint16(round(normalized_float * (2^11-1)));
end