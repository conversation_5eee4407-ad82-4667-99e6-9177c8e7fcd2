function [out] = culCalcCRC(crcData, crcReg)
%  Function    ：culCalcCRC
%  Description : 计算CRC16值，生成多项式 x16 + x15 + x2 + 1 (0x8005)
%                参考文献《Design Note DN502， CRC Implementation》，模式为：Normal mode CRC
%  Parameter   : crcData -- 输入数据    (UINT8)
%                crcReg  -- 累计的crc值 (UINT16)
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-18


for i=1:8 %字节bit计数
    A = bitand(crcReg, hex2dec('8000')); % 取CRC sum状态的MSB
    B = bitshift(A,-8);                  % 右移 8 bit
    C = bitand(crcData, hex2dec('80'));  % 取数据MSB
    D = bitxor(B, C);
    E = bitshift(crcReg, 1);
    E = bitand(E, hex2dec('FFFF')); %归一化16bit
    if(D>0) % MSB xor 后 结果为1
        crcReg = bitxor(E, hex2dec('8005')); %生成多项式作用，由于高16不作用，因此x16 + x15，仅仅对15bit
    else
        crcReg = E; % 结果为0，生成多项式不作用
    end
    crcData = bitshift(crcData, 1);          %输入数据左移1
    crcData = bitand(crcData, hex2dec('FF'));%归一化8bit
end

out = crcReg;%返回值

end

