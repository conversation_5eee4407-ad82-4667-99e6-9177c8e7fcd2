function creatEngineFile(version,filename,rx,samp_rate_hz,bandwidth_hz,center_freq_khz)
vv=load(version).version;
len = length(rx) + 112;
v0 = typecast(uint64(len), 'uint8');
vv(9:16) = v0;
v0 = typecast(int32(center_freq_khz), 'uint8');
vv(57:60) = v0;
v0 = typecast(int32(bandwidth_hz), 'uint8');
vv(53:56) = v0;
v0 = typecast(int32(samp_rate_hz), 'uint8');
vv(49:52) = v0;
v4 = [vv; rx'];
fid = fopen (filename, 'wb');
fwrite(fid, v4, 'uint8');
fclose(fid);


end
