function [] = DeflateHdf5(fname_rd, fname_wr)
%  Function    ：DeflateHdf5
%  Description : 压缩Hdf5训练数据文件
%  Parameter   : fname_wr       -- 写入文件名称
%                fname_rd       -- 读取文件名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13

%读出数据文件
if exist(fname_rd,"file")>0
    rd_sig     = h5read(fname_rd,"/rx_signal");
    class_id   = h5read(fname_rd,"/class_id");
    class_name = h5read(fname_rd,"/class_name");
    fc = h5read(fname_rd,"/fc");
    fs = h5read(fname_rd,"/fs");
else
    fprintf("数据文件：%s不存在\r\n",fname_rd);
    return;    
end
%写入转换文件
N_1msPoints = 61.44e6*1e-3;
nMaxSize = [2 Inf Inf]; %[I/Q N-points Batch]
if exist(fname_wr,"file")>0
    fprintf("已有训练数据文件：%s\r\n",fname_wr);
    return;
    %delete(fname_wr)
end

h5create(fname_wr,"/rx_signal",nMaxSize,'Datatype','single','ChunkSize',[2 N_1msPoints 1],'Deflate',9);%,'Deflate',9
h5create(fname_wr,"/class_id",[1 Inf],'Datatype','int32','ChunkSize',[1 1]);
h5create(fname_wr,"/fc",[1 Inf],'Datatype','int64','ChunkSize',[1 1]);
h5create(fname_wr,"/fs",[1 Inf],'Datatype','int64','ChunkSize',[1 1]);
h5create(fname_wr,"/class_name",[1 Inf],'Datatype','string','ChunkSize',[1 1]);

startbatch = 1;
% [IQn, N_points, N_record]=size(rd_sig);
% nCurSize = [2 N_points 1];
h5write(fname_wr,"/rx_signal",rd_sig,[1 1 startbatch],size(rd_sig));
h5write(fname_wr,"/class_id",class_id, [1 startbatch], size(class_id));
h5write(fname_wr,"/fc",fc, [1 startbatch], size(fc));
h5write(fname_wr,"/fs",fs, [1 startbatch], size(fs));
h5write(fname_wr,"/class_name",class_name, [1 startbatch], size(class_name));


end

