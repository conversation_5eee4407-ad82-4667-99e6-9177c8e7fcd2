'''
    - Functions: 这个文件用于根据边缘检测输出最终的信号的标签位置，并将其输出的标签用于 ：
    extract_and_save_signals(filename_in, label, wb_signal, curstartPosOffset,
    wb_fc, wb_fs, clip_ms, const_ms, wb_bw, fft_len, cls_id, fname_dataset, bshowdgraph, nb_bw_def=None)
    -
'''
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np
import uhd
from matplotlib import pyplot as plt, patches

import sys
import os
import cv2

from chanlib.ExtractNBSig import ExtractNBSig
from chanlib.calregions import extract_and_save_signals, findMinPower
from chanlib.findAccuracteBW import findAccuracteBW
from chanlib.usrp_samp import multi_usrp_rx, init_args
from chanlib.wr_train_sig import WrTrainSig
from nets.arcface import Arcface
import torch
import torch.nn as nn
import torch.nn.functional as F

class EdgeDetection:
    def __init__(self, method='Sobel', n_fft=None, fs=66.14e6, signal_duration=0.028):
        # 参数验证
        valid_methods = ['Sobel', 'Canny', 'Prewitt', 'Laplacian']
        if method not in valid_methods:
            raise ValueError(f"不支持的边缘检测方法: {method}. 支持的方法: {valid_methods}")

        if n_fft is None:
            n_fft = 256
        if n_fft <= 0 or (n_fft & (n_fft - 1)) != 0:
            raise ValueError("n_fft必须是正的2的幂")


        self.method = method
        self.n_fft = n_fft
        self.fs = fs

        self.signal_duration = signal_duration # 设置一个default的长度

        # 用于存储时频图参数
        self.spectrum_shape = None
        self.time_frames = None
        self.freq_bins = None
        self.center_freq = None

    def signaltoSpectrum(self, signals):
        real_time_len = signals.shape[1]/self.fs
        self.signal_duration = real_time_len
        N_fft = self.n_fft
        N_window = N_fft
        N_overlap = math.floor(N_fft / 2)
        if len(signals.shape) > 3:
            y = signals.squeeze()  # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = signals
        z = torch.complex(y[:, :, 0], y[:, :, 1])
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window, hop_length=N_overlap, win_length=N_window,
                       center=False, normalized=True)  # 转换到频域

        z = torch.fft.fftshift(z, dim=1)  # [batch, fft_size, timeframe]

        # 存储时频图参数供坐标转换使用
        self.spectrum_shape = z.shape[-2:]  # (freq_bins, time_frames)

        return z

    def processSpectrum(self, spectrum):
        """
        预处理时频图，转换为适合边缘检测的格式
        Args:
            spectrum: 时频图 [batch, fft_size, timeframe]
        Returns:
            original_spectrum: 原始幅度谱
            processed_spectrum: 预处理后的灰度图
        """
        magnitude_spectrum = torch.abs(spectrum)
        # 1. 平滑滤波去除噪点
        # 使用高斯滤波进行平滑
        img_smoothed = cv2.GaussianBlur(magnitude_spectrum.cpu().numpy(), (5, 5), 1.0)
        min = img_smoothed.min()
        max = img_smoothed.max()
        normalized_spectrum = (img_smoothed - min) / (max - min)
        # 转换为0-255范围的灰度图
        gray_spectrum = (normalized_spectrum * 255).astype(np.uint8)
        return magnitude_spectrum, gray_spectrum

    def convert_pixel_to_real_coords(self, pixel_x, pixel_y, wb_fc):
        """
        将时频图像素坐标转换为实际的时间和频率坐标
        Args:
            pixel_x: 时间轴像素坐标
            pixel_y: 频率轴像素坐标
            wb_fc: 中心频率
        Returns:
            real_time: 实际时间 (秒)
            real_freq: 实际频率 (Hz)
        """
        if self.spectrum_shape is None:
            raise ValueError("需要先调用signaltoSpectrum方法获取时频图参数")

        freq_bins, time_frames = self.spectrum_shape

        # 时间轴转换：从像素坐标转换为实际时间 (0 到 signal_duration)
        real_time = (pixel_x / time_frames) * self.signal_duration

        # 频率轴转换：将像素坐标转换为实际频率
        # 频率轴中心点对应wb_fc，频率范围为 ±fs//2 MHz
        freq_center_pixel = freq_bins // 2
        freq_resolution = self.fs / freq_bins  # 频率分辨率

        # 计算相对于中心的频率偏移
        freq_offset = (pixel_y - freq_center_pixel) * freq_resolution
        real_freq = wb_fc + freq_offset

        return real_time, real_freq

    def convert_contours_to_real_coords(self, contour_info, wb_fc):
        """
        将轮廓信息转换为实际坐标
        Args:
            contour_info: 轮廓信息列表
            wb_fc: 中心频率
        Returns:
            converted_contours: 转换后的轮廓信息
        """
        converted_contours = []

        for info in contour_info:
            # 转换中心点坐标
            center_x, center_y = info['center']
            real_center_time, real_center_freq = self.convert_pixel_to_real_coords(center_x, center_y, wb_fc)

            # 转换边界框坐标
            x, y, w, h = info['bounding_box']

            # 转换边界框的四个角点
            real_start_time, real_start_freq = self.convert_pixel_to_real_coords(x, y, wb_fc)
            real_end_time, real_end_freq = self.convert_pixel_to_real_coords(x + w, y + h, wb_fc)

            # 计算实际时间和频率范围
            real_time_duration = abs(real_end_time - real_start_time)
            real_freq_bandwidth = abs(real_end_freq - real_start_freq)

            converted_info = {
                'id': info['id'],
                'pixel_center': info['center'],
                'real_center_time_ms': real_center_time * 1000,  # 转换为毫秒
                'real_center_freq_mhz': real_center_freq / 1e6,  # 转换为MHz
                'pixel_bounding_box': info['bounding_box'],
                'real_time_start_ms': real_start_time * 1000,
                'real_time_end_ms': real_end_time * 1000,
                'real_time_duration_ms': real_time_duration * 1000,
                'real_freq_start_mhz': real_start_freq / 1e6,
                'real_freq_end_mhz': real_end_freq / 1e6,
                'real_freq_bandwidth_mhz': real_freq_bandwidth / 1e6,
                'area': info['area'],
                'perimeter': info['perimeter'],
                'aspect_ratio': info['aspect_ratio']
            }
            converted_contours.append(converted_info)

            # 打印转换后的信息
            print(f"📍 轮廓 {info['id']} 实际坐标:")
            print(f"   中心时间: {real_center_time * 1000:.2f} ms")
            print(f"   中心频率: {real_center_freq / 1e6:.2f} MHz")
            print(f"   时间范围: {real_start_time * 1000:.2f} - {real_end_time * 1000:.2f} ms")
            print(f"   频率范围: {real_start_freq / 1e6:.2f} - {real_end_freq / 1e6:.2f} MHz")
            print(f"   时间持续: {real_time_duration * 1000:.2f} ms")
            print(f"   频率带宽: {real_freq_bandwidth / 1e6:.2f} MHz")
            print("-" * 50)

        return converted_contours

    def detect_edges(self, processed_spectrum):
        """
        改进的边缘检测方法
        """
        try:

            img = processed_spectrum.astype(np.uint8)

            # 检查图像是否为空
            if img.size == 0:
                raise ValueError("输入图像为空")

            if self.method == 'Sobel':
                sobelx = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
                sobely = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
                gradient_magnitude = np.sqrt(sobelx ** 2 + sobely ** 2)

                # 归一化梯度幅值
                gradient_magnitude = (gradient_magnitude / gradient_magnitude.max() * 255).astype(np.uint8)

                # 自适应阈值
                edge_map = cv2.adaptiveThreshold(
                    gradient_magnitude, 255,
                    cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                    cv2.THRESH_BINARY, 11, 2
                )

            elif self.method == 'Canny':
                # 高斯滤波降噪
                img_blur = cv2.GaussianBlur(img, (5, 5), 0)
                edge_map = cv2.Canny(img_blur, 50, 150)
                gradient_magnitude = edge_map.copy()

            elif self.method == 'Prewitt':
                # Prewitt算子
                kernelx = np.array([[-1, 0, 1], [-1, 0, 1], [-1, 0, 1]])
                kernely = np.array([[-1, -1, -1], [0, 0, 0], [1, 1, 1]])
                prewittx = cv2.filter2D(img, cv2.CV_64F, kernelx)
                prewitty = cv2.filter2D(img, cv2.CV_64F, kernely)
                gradient_magnitude = np.sqrt(prewittx ** 2 + prewitty ** 2)
                gradient_magnitude = (gradient_magnitude / gradient_magnitude.max() * 255).astype(np.uint8)
                edge_map = cv2.threshold(gradient_magnitude, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]

            elif self.method == 'Laplacian':
                # 拉普拉斯算子
                img_blur = cv2.GaussianBlur(img, (3, 3), 0)
                laplacian = cv2.Laplacian(img_blur, cv2.CV_64F, ksize=3)
                gradient_magnitude = np.abs(laplacian)
                gradient_magnitude = (gradient_magnitude / gradient_magnitude.max() * 255).astype(np.uint8)
                edge_map = cv2.threshold(gradient_magnitude, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]

            # 转换回张量
            edge_map = torch.from_numpy(edge_map)
            gradient_magnitude = torch.from_numpy(gradient_magnitude)

            return edge_map, gradient_magnitude

        except Exception as e:
            print(f"边缘检测过程中出错: {e}")
            # 返回空结果
            empty_shape = processed_spectrum.shape[-2:]
            return torch.zeros(empty_shape, device=processed_spectrum.device), torch.zeros(empty_shape,
                                                                                           device=processed_spectrum.device)

    def findCounters(self, edge_map, wb_fc=None):
        """
        改进的轮廓检测方法，支持坐标转换
        """
        try:
            if isinstance(edge_map, torch.Tensor):
                edge_map_np = edge_map.cpu().numpy().astype(np.uint8)
            else:
                edge_map_np = edge_map.astype(np.uint8)

            if edge_map_np.max() > 1:
                edge_map_np = (edge_map_np > 127).astype(np.uint8) * 255

            kernel = np.ones((3, 3), np.uint8)
            # edge_map_np = cv2.morphologyEx(edge_map_np, cv2.MORPH_CLOSE, kernel) #note:20250620 注意闭运算因为先进行膨胀操作容易将两个不连续的信号链接在一起，所以先去掉
            edge_map_np = cv2.morphologyEx(edge_map_np, cv2.MORPH_OPEN, kernel)
            contours, hierarchy = cv2.findContours(
                edge_map_np, cv2.RETR_CCOMP, cv2.CHAIN_APPROX_SIMPLE
            )
            
            min_contour_area = 80  # explain：这里是需要手动手动设置举行面积的，只有超过设置的面积才会被认为是有效边缘
            filtered_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_contour_area]
            print(f"🔍 找到 {len(contours)} 个轮廓，过滤后剩余 {len(filtered_contours)} 个")

            # 合并时间重叠度高且频率接近的标签
            filtered_contours = self.merge_close_contours(filtered_contours)
              # 2. 再删除嵌套的轮廓
            filtered_contours = self.delete_nested_contours(filtered_contours)
            


            # 输出轮廓位置信息
            contour_info = []
            for i, contour in enumerate(filtered_contours):
                x, y, w, h = cv2.boundingRect(contour)

                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                else:
                    cx, cy = x + w // 2, y + h // 2

                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)

                info = {
                    'id': i + 1,
                    'center': (cx, cy),
                    'bounding_box': (x, y, w, h),
                    'area': area,
                    'perimeter': perimeter,
                    'aspect_ratio': w / h if h > 0 else 0
                }
                contour_info.append(info)

            # 如果提供了中心频率，进行坐标转换
            if wb_fc is not None and self.spectrum_shape is not None:
                print("🔄 转换为实际坐标...")
                converted_contours = self.convert_contours_to_real_coords(contour_info, wb_fc)
            else:
                converted_contours = contour_info
                print("⚠️ 未提供中心频率，仅输出像素坐标")
                for info in contour_info:
                    print(f"📍 轮廓 {info['id']} (像素坐标):")
                    print(f"   中心点: {info['center']}")
                    print(f"   边界框: {info['bounding_box']}")
                    print(f"   面积: {info['area']:.2f}")
                    print("-" * 40)

            if len(edge_map_np.shape) == 2:
                edge_map_color = cv2.cvtColor(edge_map_np, cv2.COLOR_GRAY2BGR)
            else:
                edge_map_color = edge_map_np.copy()

            for i, (contour, info) in enumerate(zip(filtered_contours, contour_info)):
                cv2.drawContours(edge_map_color, [contour], -1, (0, 0, 255), 2)

                x, y, w, h = info['bounding_box']
                cv2.rectangle(edge_map_color, (x, y), (x + w, y + h), (0, 255, 0), 1)

                cx, cy = info['center']
                cv2.circle(edge_map_color, (cx, cy), 3, (255, 0, 0), -1)

                label = f"#{info['id']}"
                cv2.putText(edge_map_color, label, (cx - 10, cy - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            if isinstance(edge_map, torch.Tensor):
                result = torch.from_numpy(edge_map_color).to(edge_map.device)
            else:
                result = edge_map_color

            return result, converted_contours

        except Exception as e:
            print(f"轮廓检测过程中出错: {e}")
            return edge_map, []
    
    def merge_close_contours(self, contours):
        """
        迭代合并轮廓，分两种情况：
        1. 重叠信号：时间重叠>98%且频率间隔<133像素
        2. 非重叠信号：时间间隔<0.2ms且中心频率间隔<4像素
        """
        if not contours:
            return contours
        
        # 重叠信号的阈值
        TIME_OVERLAP_THRESHOLD = 0.98
        FREQ_GAP_THRESHOLD = 133  # 像素距离
        
        # 非重叠信号的阈值
        TIME_GAP_THRESHOLD = 1
        FREQ_CENTER_GAP_THRESHOLD = 4  # 中心频率间隔阈值(像素)
        
        def single_merge_iteration(input_contours):
            merged_contours = []
            used_indices = set()
            
            for i, contour1 in enumerate(input_contours):
                if i in used_indices:
                    continue
                    
                current_group = [contour1]
                used_indices.add(i)
                
                x1, y1, w1, h1 = cv2.boundingRect(contour1)
                center_freq1 = y1 + h1//2  # 中心频率位置
                
                for j, contour2 in enumerate(input_contours[i+1:], i+1):
                    if j in used_indices:
                        continue
                        
                    x2, y2, w2, h2 = cv2.boundingRect(contour2)
                    center_freq2 = y2 + h2//2  # 中心频率位置
                    
                    # 判断是否有时间重叠
                    overlap_start = max(x1, x2)
                    overlap_end = min(x1 + w1, x2 + w2)
                    has_overlap = overlap_end > overlap_start
                    
                    if has_overlap:
                        # 情况1: 处理重叠信号
                        overlap_duration = overlap_end - overlap_start
                        duration1 = w1
                        duration2 = w2
                        overlap_ratio = overlap_duration / min(duration1, duration2)
                        
                        # 计算频率间隔
                        freq_top1, freq_bottom1 = y1, y1 + h1
                        freq_top2, freq_bottom2 = y2, y2 + h2
                        
                        if freq_bottom1 < freq_top2:
                            freq_gap = freq_top2 - freq_bottom1
                        elif freq_bottom2 < freq_top1:
                            freq_gap = freq_top1 - freq_bottom2
                        else:
                            freq_gap = 0
                        
                        if overlap_ratio > TIME_OVERLAP_THRESHOLD and freq_gap < FREQ_GAP_THRESHOLD:
                            current_group.append(contour2)
                            used_indices.add(j)
                            print(f"合并重叠信号 {i} 和 {j}:")
                            print(f"  时间重叠比例: {overlap_ratio:.2f}")
                            print(f"  频率间隔: {freq_gap}")
                    
                    else:
                        # 情况2: 处理非重叠信号
                        time_gap = min(abs(x2 - (x1 + w1)), abs(x1 - (x2 + w2)))
                        freq_center_gap = abs(center_freq1 - center_freq2)
                        
                        if time_gap < TIME_GAP_THRESHOLD and freq_center_gap < FREQ_CENTER_GAP_THRESHOLD:
                            current_group.append(contour2)
                            used_indices.add(j)
                            print(f"合并非重叠信号 {i} 和 {j}:")
                            print(f"  相隔时间点数为{time_gap}")
                            print(f"  中心频率间隔: {freq_center_gap}像素")
                
                if len(current_group) > 1:
                    merged_contour = self.merge_contour_group(current_group)
                    merged_contours.append(merged_contour)
                else:
                    merged_contours.append(current_group[0])
            
            return merged_contours
        
        # 迭代合并直到无法继续合并
        current_contours = contours
        iteration = 0
        while True:
            iteration += 1
            print(f"\n第 {iteration} 轮合并:")
            print(f"当前轮廓数量: {len(current_contours)}")
            merged_contours = single_merge_iteration(current_contours)
            
            if len(merged_contours) == len(current_contours):
                print("没有更多可合并的轮廓")
                break
            
            print(f"合并后轮廓数量: {len(merged_contours)}")
            current_contours = merged_contours
        
        return current_contours

    def merge_contour_group(self, contour_group):
        """合并一组OpenCV轮廓"""
        # 创建一个包含所有点的数组
        all_points = np.vstack([contour.squeeze() for contour in contour_group])
        
        # 计算凸包
        hull = cv2.convexHull(all_points)
        
        return hull

    def delete_nested_contours(self, contours):
        """
        删除嵌套的轮廓
        判断当前counter之间是否有包含关系（时间和频率都被大的完全覆盖的，删除小的）
        """
        if not contours:
            return contours
        
        
        to_delete = set()
        
        for i, contour1 in enumerate(contours):
            if i in to_delete:
                continue
                
            x1, y1, w1, h1 = cv2.boundingRect(contour1)
            area1 = cv2.contourArea(contour1)
            
            # 轮廓1的边界
            time_start1, time_end1 = x1, x1 + w1
            freq_start1, freq_end1 = y1, y1 + h1
            
            for j, contour2 in enumerate(contours):
                if i == j or j in to_delete:
                    continue
                    
                x2, y2, w2, h2 = cv2.boundingRect(contour2)
                area2 = cv2.contourArea(contour2)
                
                # 轮廓2的边界
                time_start2, time_end2 = x2, x2 + w2
                freq_start2, freq_end2 = y2, y2 + h2
                
                # 检查是否存在包含关系
                time_contained = (time_start1 <= time_start2 and time_end1 >= time_end2) or \
                            (time_start2 <= time_start1 and time_end2 >= time_end1)
                freq_contained = (freq_start1 <= freq_start2 and freq_end1 >= freq_end2) or \
                            (freq_start2 <= freq_start1 and freq_end2 >= freq_end1)
                
                if time_contained and freq_contained:
                    # 如果两个轮廓互相包含，删除面积小的
                    if area1 > area2:
                        to_delete.add(j)
                        print(f"删除被轮廓{i}包含的小轮廓{j}")
                        print(f"  轮廓{i}面积: {area1:.2f}")
                        print(f"  轮廓{j}面积: {area2:.2f}")
                    elif area2 > area1:
                        to_delete.add(i)
                        print(f"删除被轮廓{j}包含的小轮廓{i}")
                        print(f"  轮廓{i}面积: {area1:.2f}")
                        print(f"  轮廓{j}面积: {area2:.2f}")
                        break  # 当前轮廓被删除，不需要继续比较
        
        
        return [cnt for i, cnt in enumerate(contours) if i not in to_delete]
       
        
        


def plot_contour_analysis_with_real_coords(original_spectrum, edge_map_with_contours, contour_info,
                                           wb_fc, signal_duration=0.01, fs=15.36e6):
    try:
        if isinstance(original_spectrum, torch.Tensor):
            spectrum_np = original_spectrum.cpu().numpy()
        else:
            spectrum_np = original_spectrum

        freq_bins, time_frames = spectrum_np.shape
        time_axis = np.linspace(0, signal_duration * 1000, time_frames)

        freq_center_bin = freq_bins // 2
        freq_resolution = fs / freq_bins
        freq_offset = np.arange(freq_bins) - freq_center_bin
        freq_axis = (wb_fc + freq_offset * freq_resolution) / 1e6

        # 创建子图
        fig, axes = plt.subplots(1, 3, figsize=(20, 6))
        fig.suptitle(f'Contour Analysis Results (Center Freq: {wb_fc / 1e6:.2f} MHz)', fontsize=16)

        # 1. 原始时频图
        im1 = axes[0].imshow(spectrum_np, aspect='auto', origin='lower', cmap='jet',
                             extent=[time_axis[0], time_axis[-1], freq_axis[0], freq_axis[-1]])
        axes[0].set_title('Original Spectrogram')
        axes[0].set_xlabel('Time (ms)')
        axes[0].set_ylabel('Frequency (MHz)')
        fig.colorbar(im1, ax=axes[0], label='Magnitude')

        # 2. 带轮廓标注的边缘图
        contour_img = edge_map_with_contours.cpu().numpy() if isinstance(edge_map_with_contours,
                                                                         torch.Tensor) else edge_map_with_contours
        if len(contour_img.shape) == 3:
            contour_img = cv2.cvtColor(contour_img, cv2.COLOR_BGR2RGB)

        axes[1].imshow(contour_img, aspect='auto', origin='lower',
                       extent=[time_axis[0], time_axis[-1], freq_axis[0], freq_axis[-1]])
        axes[1].set_title('Contours Detection')
        axes[1].set_xlabel('Time (ms)')
        axes[1].set_ylabel('Frequency (MHz)')

        # 3. 统计信息（使用实际坐标）
        axes[2].axis('off')

        if contour_info:
            stats_text = "Contour Statistics (Real Coordinates):\n" + "=" * 40 + "\n"
            for info in contour_info:
                if 'real_center_time_ms' in info:  # 如果有实际坐标信息
                    stats_text += f"Contour #{info['id']}:\n"
                    stats_text += f"  Time: {info['real_center_time_ms']:.2f} ms\n"
                    stats_text += f"  Freq: {info['real_center_freq_mhz']:.2f} MHz\n"
                    stats_text += f"  Duration: {info['real_time_duration_ms']:.2f} ms\n"
                    stats_text += f"  Bandwidth: {info['real_freq_bandwidth_mhz']:.2f} MHz\n"
                    stats_text += f"  Area: {info['area']:.1f} pixels\n"
                else:  # 仅有像素坐标
                    stats_text += f"Contour #{info['id']}:\n"
                    stats_text += f"  Center: {info['center']}\n"
                    stats_text += f"  Size: {info['bounding_box'][2]}×{info['bounding_box'][3]}\n"
                    stats_text += f"  Area: {info['area']:.1f}\n"
                stats_text += "-" * 30 + "\n"

            # 总体统计
            total_area = sum(info['area'] for info in contour_info)
            avg_area = total_area / len(contour_info)
            stats_text += f"\nSummary:\n"
            stats_text += f"Total Contours: {len(contour_info)}\n"
            stats_text += f"Total Area: {total_area:.1f} pixels\n"
            stats_text += f"Average Area: {avg_area:.1f} pixels\n"
        else:
            stats_text = "No contours detected"

        axes[2].text(0.05, 0.95, stats_text, transform=axes[2].transAxes,
                     fontsize=9, verticalalignment='top', fontfamily='monospace')
        axes[2].set_title('Statistics')

        plt.tight_layout()


        plt.show()

    except Exception as e:
        print(f"绘制轮廓分析图时出错: {e}")
        plt.close('all')



def proc_data_counter(signal, wb_fs, wb_fc, wb_bw):
    """实时数据处理主函数
    使用方法：在main_proc中替换proc_data函数即可
    """
    global all_predicts, num_samp, fname_sample, num_modelcheck
    # note： 这里的fc尽量靠近当前传入的遥控器的中心频率，这样我们大概就可以确定只有再这个频率附近的信号才是我们想要的那个信号
    #args = init_args(fc=2431e6, fs=15.36e6, bw=12e6)
    n_fft = 4096
    bshowdgraph = False
    # 初始化边缘检测器
    edge_detector = EdgeDetection(
        method='Prewitt',  # 可选: 'Sobel', 'Canny', 'Prewitt', 'Laplacian'
        n_fft=n_fft,       # FFT长度
        fs=wb_fs,     # 采样率
        signal_duration=0.01  # 信号持续时间，可根据实际情况调整
    )
    chan_id = 0

    try:
        # 将复数信号转换为实部虚部的张量格式
        signals_tensor = torch.from_numpy(
            np.stack([np.real(signal), np.imag(signal)], axis=-1)
        ).unsqueeze(0)  # 添加batch维度

        # 生成时频图
        spectrum = edge_detector.signaltoSpectrum(signals_tensor)

        # 处理时频图
        original_spectrum, processed_spectrum = edge_detector.processSpectrum(spectrum[0])

        # 边缘检测
        edge_map, gradient_magnitude = edge_detector.detect_edges(processed_spectrum)

        # 查找轮廓并转换坐标
        result_image, contours = edge_detector.findCounters(edge_map, wb_fc)



        # 2. 再处理检测到的信号
        if len(contours) > 0:
            # 将轮廓信息转换为标签格式
            label = []
            for contour in contours:
                # 创建标签对象
                lbl = type('Label', (), {})()

                # 设置标签属性
                lbl.start_idx = int(contour['real_time_start_ms'] * 1e-3 * wb_fs)
                lbl.stop_idx = int(contour['real_time_end_ms'] * 1e-3 * wb_fs)
                lbl.centor_freq = contour['real_center_freq_mhz'] * 1e6
                lbl.bw = contour['real_freq_bandwidth_mhz'] * 1e6

                # 设置时频图位置信息
                x, y, w, h = contour['pixel_bounding_box']
                lbl.left = x
                lbl.right = x + w
                lbl.up = y
                lbl.down = y + h
                lbl.valid = 1

                # 验证有效性
                if (lbl.bw > 0 and
                    lbl.stop_idx > lbl.start_idx and
                    lbl.start_idx >= 0 and
                    lbl.stop_idx <= len(signal)):
                    label.append(lbl)

            filename_in = "outsig.bvsp"

            # 6 根据label提取待检测信号，并保存为数据文件hdf5格式
            curstartPosOffset= 0 #1000 #起始位置偏差补偿值，可选输入
            clip_ms = 1.0 #限制的最大时间长度，可选输入
            nb_bw_def = 4e6 #for nb433 #4.0e6  # -- 信号带宽，可选输入
            const_ms = 6.0
            cls_id = 0                           #-- 类别标识(方便AI信号检测用)

            fname_dataset =  "fchanscan-S1.hdf5" # 生成输出文件路径
            if os.path.exists(fname_dataset):
                os.remove(fname_dataset)

            extract_and_save_signals(filename_in, label,signal, curstartPosOffset, wb_fc, wb_fs, clip_ms, const_ms, wb_bw, n_fft, cls_id, fname_dataset, bshowdgraph, nb_bw_def=None)

    except Exception as e:
        print(f"处理过程中出现错误: {e}")

def extract_and_save_signals_counter(filename_in, label, wb_signal, curstartPosOffset, wb_fc,  wb_fs, clip_ms, const_ms, wb_bw, fft_len, cls_id, fname_dataset, bshowdgraph, nb_bw_def=None):
    # 从完整文件名中提取最后一部分作为文件名
    sigfsubname = filename_in.split('\\')[-1]
    # 初始化索引指针，用于标记信号的序号
    indexPointer = 0

    # 计算最小功率，认为是噪声功率
    min_clippower = findMinPower(wb_signal)  #功率阈值
    powerthreshold_region = min_clippower*2.1 #
    # note：边缘检测方法是否需要设置起点检测
    # 遍历label列表中的每个元素
    for n in range(len(label)):
        # 检查当前label是否有效
        if label[n].valid==0:
            continue #滤除无效记录


        # 计算信号的起始位置
        lbl_startpos = label[n].start_idx  + curstartPosOffset
        # 确保起始位置不小于0
        if lbl_startpos < 0:
            lbl_startpos = 0
        # 计算信号的结束位置
        lbl_endpos = label[n].stop_idx - curstartPosOffset


        # 计算裁剪长度
        clip_len = int(np.floor(wb_fs * clip_ms / 1000))
        # 计算最大长度
        max_len = int(np.floor(wb_fs * const_ms / 1000))

        # 如果结束位置超过最大长度，将其设置为最大长度
        if lbl_endpos > lbl_startpos + max_len:
            lbl_endpos = lbl_startpos + max_len
        # 如果结束位置小于起始位置加上裁剪长度的一半，跳过当前循环并输出提示信息
        elif lbl_endpos < lbl_startpos + clip_len // 2:
            print(f"[chanscan] 序号:{n} \t 采集文件:{sigfsubname} [{lbl_startpos},{lbl_endpos}] \t 持续时间为{(lbl_endpos - lbl_startpos) * 1000 / wb_fs:.2f} ms << clip_ms={clip_ms / 2:.2f} ms, 长度太短, 不处理")
            continue

        # 根据结束位置截取宽带信号
        if lbl_endpos < len(wb_signal):
            signal = wb_signal[lbl_startpos:lbl_endpos]
        else:
            signal = wb_signal[lbl_startpos:]

        avgpower_cursig = np.sqrt(np.mean(np.abs(signal)**2))  # 平均功率
        if avgpower_cursig < powerthreshold_region:
            print(f"文件:{sigfsubname} [{lbl_startpos}, {lbl_endpos}] 切片功率弱于2.1平均信号RMS,跳过该条")
            continue


        # note： 下面这里需要输入宽频时需要调整
        # 获取信号的带宽
        nb_bw = label[n].bw
        if nb_bw>1.2e6:
            print(f"文件:{sigfsubname} [{lbl_startpos}, {lbl_endpos}] 切片带宽:{nb_bw/1e6}MHz 大于1M,跳过该条")
            continue
        # 调整带宽
        nb_bw = nb_bw * 0.8  #todo：当前易出现窄带信号识别出的窄带更窄的情况，例如125kHz，两种方法输出信号都只有50kHz左右，是否需要再原来的基础上还进行缩减
        # 确保带宽不超过4MHz
        if nb_bw > 4e6:
            nb_bw = 4e6
        # 保存调整前的带宽
        nb_bw_prev = nb_bw
        # 如果提供了默认带宽，使用默认带宽
        if nb_bw_def is not None:
            nb_bw = nb_bw_def

        # 获取信号的中心频率
        nb_fc = label[n].centor_freq
        # 调用自定义函数修正中心频率和带宽 ( 对于不同信号可能会有较大差异)
        if nb_bw_def is None:#无设置时才启动重估
            nb_fc, nb_bw = findAccuracteBW(signal, nb_fc, nb_bw, wb_fc, wb_fs, fft_len)

        # 设置窄带信号的采样率
        nb_fs = 4e6
        # 调用自定义函数提取窄带信号
        ret, nb_signal = ExtractNBSig(signal, wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs)
        if ret!=1: #不合乎信号带宽范围
            continue
        # 窄带信号的有效采样率
        nb_fs_effct = nb_fs
        # 窄带信号的长度
        nb_len_set = len(nb_signal)

        # 索引指针加1
        indexPointer += 1
        # 输出信号的相关信息
        print(f"[chanscan] 序号:{n} (id={indexPointer}) \t 采集文件:{sigfsubname} \t [{lbl_startpos},{lbl_endpos}]\t fs_wb={wb_fs / 1e6:.2f} M fc={nb_fc / 1e6:.2f} MHz \t fs_nb={nb_fs_effct / 1e6:.2f} M \t BW={nb_bw / 1e3:.2f}({nb_bw_prev / 1e3:.2f}) K \t 分类ID:{cls_id} \t 数据点数:{nb_len_set} \t 持续时间为{nb_len_set * 1000 / nb_fs_effct:.2f} ms")

        # 调用保存函数将窄带信号及其相关信息保存到HDF5文件中
        WrTrainSig(fname_dataset, nb_signal, cls_id, sigfsubname, nb_fc, nb_fs_effct, nb_bw, indexPointer-1)

if __name__ == '__main__':
    proc_data_counter()