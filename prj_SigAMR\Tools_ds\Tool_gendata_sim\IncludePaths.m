% 获取操作系统信息
sysinfo = computer;

% 判断操作系统类型
if strcmp(sysinfo, 'PCWIN') || strcmp(sysinfo, 'PCWIN64')
    disp('当前操作系统为Windows');
    %可放在命令行中执行
    addpath("usrlib\dataset_save\"); %库函数路径
    addpath("usrlib\common\");  %用户自定义路径
    addpath("usrlib\gendata\"); 
elseif strcmp(sysinfo, 'MACI') || strcmp(sysinfo, 'MACI64')
    disp('当前操作系统为Mac OS');
elseif strcmp(sysinfo, 'GLNX86') || strcmp(sysinfo, 'GLNXA64')
    disp('当前操作系统为Linux');
    %可放在命令行中执行
    addpath("usrlib\dataset_save\"); %库函数路径
    addpath("usrlib\common\");  %用户自定义路径
    addpath("usrlib\gendata\"); 
else
    disp('未知操作系统');
end