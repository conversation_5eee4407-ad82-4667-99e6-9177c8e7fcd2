%  Function    ：calCalSNR.m
%  Description : 为标注数据单独计算并添加snr，添加在Train_records最后一列
%  Date        : 2025-06-11

clear;
close all;

IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量
if exist(myconfig.fpath_labeldef,"file")>0
    fprintf("启动snr添加");
else
    error("文件：%s 不存在！", myconfig.fpath_labeldef);
end

Table_lbldef = readtable(myconfig.fpath_labeldef,'Format','%s %s %s %f %d','Delimiter',';');
Table_lbldef.Properties.VariableNames = ["foldername","meta_subtype","clsname","duration_ms","forwardoffset"];

% 启动并行池（如果还没有启动）
if ~isempty(gcp('nocreate'))
    delete(gcp('nocreate'));
end

% 获取CPU核心数并限制工作进程数量
maxWorkers = feature('numCores');
workerCount = max(1, floor(maxWorkers * 0.7));  % 使用70%的核心
fprintf('启动并行池，使用 %d 个工作进程（总核心数: %d）\n', workerCount, maxWorkers);

parpool('local', workerCount);

%优化为了parfor快速执行
foldernames = Table_lbldef(:,1).foldername;
meta_subtypes = Table_lbldef(:,2).meta_subtype;
clsnames = Table_lbldef(:,3).clsname;
duration_mss = Table_lbldef(:,4).duration_ms;
forwardoffsets = Table_lbldef(:,5).forwardoffset;
% 生成base文件夹
nRows = height(Table_lbldef);
% 启动计时器
tic;
sep = filesep; %系统分割符
for i = 1 : nRows
    srcFolder = fullfile(myconfig.folder_labeleddata, foldernames(i));
    fname_recs = fullfile(srcFolder, "Train_records_byhand.txt");
   % 检查文件是否存在（不指定文件类型，查找所有类型）
    if ~exist(fname_recs, 'file')
        fprintf('文件不存在: %s\n', fname_recs);
        continue;
    end

    srcFolder = fileparts(fname_recs);  % 获取目录路径
    
    % 读取原始文件内容（用于后续写回）
    fileContent = readlines(fname_recs);
    
    % 判断文件格式：检查第一行的分号数量
    firstLine = fileContent{1};
    semicolonCount = sum(firstLine == ';');
    
    % 如果分号数量不是2，跳过此文件
    if semicolonCount ~= 2
        fprintf('跳过文件 %s，分号数量为 %d，不符合预期\n', fname_recs, semicolonCount);
        continue;
    end
    
    Table_train = readtable(fname_recs,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    Table_train.lenClipPoints = Table_train.lenPoints;
    
    msp = split(string(Table_train.filename),'\',1);
    Table_train.clsname = msp(1,:)';
    
    Table_train.recindex = Table_train.lenPoints;%暂时赋值
    nRows_data = height(Table_train);
    
    % 存储每行计算的SNR值
    snr_values = zeros(nRows_data, 1);
    
    for iRow = 1 : nRows_data 
        sigfsubname      = strjoin(Table_train.filename(iRow));%文件名称
        clsname          = split(sigfsubname,sep,1);       %文件类别名名称
        sigfname         = fullfile(srcFolder, clsname(2));    %文件路径
        
        % 确保sigfname是字符串类型
        sigfname = string(sigfname);
        
        % 调试输出（可选）
        fprintf('处理第 %d 行: %s\n', iRow, sigfname);
        
        % 加载bvsp文件
        [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
        myclip_sig = wb_signal(Table_train.startpos(iRow):Table_train.endpos(iRow));
        min_clippower = findpowerthredhold(wb_signal);
        powerthreshold_region = min_clippower*2.1;
        avgpower_cursig = sqrt(mean(abs(myclip_sig).^2));%平均功率
        snr_db = 10*log10(max((avgpower_cursig^2-min_clippower^2)/min_clippower^2, 1e-10));
        % 存储SNR值
        snr_values(iRow) = snr_db;
    end
    
    % 将SNR值追加到原文件每行末尾
    outputContent = cell(nRows_data, 1);
    for iRow = 1 : nRows_data
        % 在原行末添加分号和SNR值
        outputContent{iRow} = [fileContent{iRow}, ';', num2str(snr_values(iRow))];
    end
    
    % 写入新文件（避免覆盖原文件）
    [~, name, ext] = fileparts(fname_recs);
    outputFile = fullfile(srcFolder, "Train_records_byhand1.txt");
    if exist(outputFile,'file')>0
        fprintf('已经存在%s，请检查是否已经添加过\n', outputFile);
        continue;
    end
    writelines(outputContent, outputFile);
    
    fprintf('处理完成！SNR值已添加到新文件: %s\n', outputFile);
    
    % 将outputFile内容追加到train_records.txt
    fprintf('将内容追加到train_records.txt...\n');
    appendFile = fullfile(srcFolder, 'Train_records.txt');
    originalContent = readlines(appendFile);
    
    if ~isempty(originalContent)
        % 移除原文件最后一行的换行符
        lastLine = originalContent{end};
        lastLine = strtrim(lastLine);
        originalContent{end} = lastLine;
        
        % 读取新内容并移除所有换行符
        contentToAppend = readlines(outputFile);
        contentToAppend = strtrim(contentToAppend);
        
        % 合并内容（原文件内容 + 新内容）
        combinedContent = [originalContent; contentToAppend];
        
        % 写入合并后的内容（先清空文件再写入，确保格式统一）
        fid = fopen(appendFile, 'w');
        if fid > 0
            for i = 1:length(combinedContent)
                fprintf(fid, '%s\n', combinedContent{i});  % 手动添加单个换行符
            end
            fclose(fid);
            fprintf('成功将内容追加到 %s（已处理空行）\n', appendFile);
        else
            error('无法打开文件进行写入');
        end
    end

end


function [min_clippower]=findpowerthredhold(wave_data)
%
% 以切片方式寻找最小rms片段功率值
%
window_size = 1000; % 窗口采样点数
num_windows = floor(length(wave_data) / window_size);%窗口格式
min_clippower = 0;
for i=1:num_windows
    start_idx = (i-1) * window_size + 1;
    end_idx = i * window_size;
    window = wave_data(start_idx:end_idx);
    rms_clippower = sqrt(mean(abs(window).^2));%RMS
    if i==1
        min_clippower = rms_clippower;
    else
        if rms_clippower < min_clippower
            min_clippower = rms_clippower;
        end
    end
end

end