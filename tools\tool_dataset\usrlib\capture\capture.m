function varargout = capture(varargin)
%CAPTURE MATLAB code file for capture.fig
%      CAPTURE, by itself, creates a new CAPTURE or raises the existing
%      singleton*.
%
%      H = CAPTURE returns the handle to a new CAPTURE or the handle to
%      the existing singleton*.
%
%      CAPTURE('Property','Value',...) creates a new CAPTURE using the
%      given property value pairs. Unrecognized properties are passed via
%      varargin to capture_OpeningFcn.  This calling syntax produces a
%      warning when there is an existing singleton*.
%
%      CAPTURE('CALLBACK') and CAPTURE('CALLBACK',hObject,...) call the
%      local function named CALLBACK in CAPTURE.M with the given input
%      arguments.
%
%      *See GUI Options on GUIDE's Tools menu.  Choose "GUI allows only one
%      instance to run (singleton)".
%
% See also: GUIDE, GUIDATA, GUIHANDLES

% Edit the above text to modify the response to help capture

% Last Modified by GUIDE v2.5 26-Nov-2024 09:53:55

% Begin initialization code - DO NOT EDIT
gui_Singleton = 1;
gui_State = struct('gui_Name',       mfilename, ...
                   'gui_Singleton',  gui_Singleton, ...
                   'gui_OpeningFcn', @capture_OpeningFcn, ...
                   'gui_OutputFcn',  @capture_OutputFcn, ...
                   'gui_LayoutFcn',  [], ...
                   'gui_Callback',   []);
if nargin && ischar(varargin{1})
   gui_State.gui_Callback = str2func(varargin{1});
end

if nargout
    [varargout{1:nargout}] = gui_mainfcn(gui_State, varargin{:});
else
    gui_mainfcn(gui_State, varargin{:});
end
% End initialization code - DO NOT EDIT


% --- Executes just before capture is made visible.
function capture_OpeningFcn(hObject, eventdata, handles, varargin)
% This function has no output args, see OutputFcn.
% hObject    handle to figure
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
% varargin   unrecognized PropertyName/PropertyValue pairs from the
%            command line (see VARARGIN)

% Choose default command line output for capture
handles.output = hObject;
delete(instrfindall);
set(handles.Connect,'ForegroundColor',[0 0 0]);

set(handles.tx_lo_freq,'Enable','off');
set(handles.rx_lo_freq,'Enable','off');
set(handles.tx_samp_freq,'Enable','off');
set(handles.rx_samp_freq,'Enable','off');
set(handles.tx_rf_bw,'Enable','off');
set(handles.rx_rf_bw,'Enable','off');
set(handles.tx_attenuation,'Enable','off');
set(handles.rc_agc_mode,'Enable','off');
set(handles.rx_rf_gain,'Enable','off');
set(handles.get_length,'Enable','off');
set(handles.edit19,'Enable','off');
set(handles.edit20,'Enable','off');
set(handles.edit21,'Enable','off');
set(handles.send_phase,'Enable','off');
set(handles.recv_phase,'Enable','off'); 
set(handles.rf_param_ok,'Enable','off'); 
set(handles.circ_capture,'Enable','off'); 
set(handles.single_capture,'Enable','off'); 
set(handles.customize_command,'Enable','off'); 
set(handles.data_save,'Enable','off'); 
set(handles.clear_show,'Enable','off'); 
set(handles.command,'Enable','off'); 

% Update handles structure
guidata(hObject, handles);

% --- Outputs from this function are returned to the command line.
function varargout = capture_OutputFcn(hObject, eventdata, handles)
% varargout  cell array for returning output args (see VARARGOUT);
% hObject    handle to figure
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Get default command line output from handles structure
varargout{1} = handles.output;


% --- Executes on button press in data_save.
function data_save_Callback(hObject, eventdata, handles)
% hObject    handle to data_save (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
% global rx_data;

global rx_data_save;
% [filename filepath] = uigetfile('*.txt*','请选择文件');
lo=get(handles.rx_lo_freq,'String');
samp=get(handles.rx_samp_freq,'String');
bw=get(handles.rx_rf_bw,'String');
len = get(handles.get_length,'String');
formatOut = 'yyyymmddTHHMMSS';
time = datestr(now,formatOut);
if get(handles.protocol,'Value') == 2
   file = ['data/Ch9364_' time '_F' lo 'Mhz_B' bw 'Hz_R' samp 'HZ_L' len '.dat'];
else
   file = ['data/Ch9361_' time '_F' lo 'Mhz_B' bw 'Hz_R' samp 'HZ_L' len '.dat'];
end
fid = fopen(file,'w');
fprintf(fid,'%d ',rx_data_save);
fclose(fid);
disp(file);
% --- Executes on button press in clear_show.
function clear_show_Callback(~, ~, handles)
% hObject    handle to clear_show (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
x=0;
y=0;
plot(handles.axes2,x,y);
plot(handles.axes2,x,y);

% --- Executes on button press in circ_capture.
function circ_capture_Callback(hObject, eventdata, handles)
% hObject    handle to circ_capture (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

crcl = inputdlg('循环次数');
for i=1:str2num(crcl{1})
   single_capture_Callback(hObject, eventdata, handles);
%    data_save_Callback(hObject, eventdata, handles);
%    disp(i);
 set(handles.circ_capture,'String',num2str(i));
end
set(hObject,'String','循环采集');
% --- Executes on button press in single_capture.
function single_capture_Callback(hObject, eventdata, handles)
% hObject    handle to single_capture (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
global t_client;
global rx_data;
global rx_data_save;
global IQ;
use_maint_2019r1 = 1;
ishows = 1;
set(handles.get_length,'Enable','off');
set(handles.single_capture,'String','1%');

if get(handles.protocol,'Value') == 1
    len = str2num(get(handles.get_length,'String'))*8; % ad9361---*8   ad9364---*4;
else
    len = str2num(get(handles.get_length,'String'))*4; % ad9361---*8   ad9364---*4;
end

if strncmp(get(handles.edit22,'String'),'SPD',3)
    setAd936xConfig(t_client,'rx_spd_length',get(handles.get_length,'String'));
    len = 512*118*4;
else
    setAd936xConfig(t_client,'rx_total_length',get(handles.get_length,'String'));
end


data_len = 0;
rx_data = [];
 while(data_len ~= len)
      pause(0.01);
      BytesLen = t_client.BytesAvailable;
      if  BytesLen          
           data=fread(t_client,BytesLen,'uint8');%浠庣紦鍐插尯璇诲彇鏁板瓧鏁版嵁         
             rx_data = [rx_data,data'];     
            data_len = data_len + length(data);
            rx_cont=round(data_len/len*100);
            set(handles.single_capture,'String',[num2str(rx_cont) '%']);
      end
 end
 pause(0.01);
 ho = rx_data(2:2:end);
 lo = rx_data(1:2:end);
 
 samp_rate_hz = str2num(get(handles.rx_samp_freq,'String'));
 bandwidth_hz = str2num(get(handles.rx_rf_bw,'String'));
 center_freq_khz = str2double(get(handles.rx_lo_freq,'String'))*1000;
 creatEngineFile('version.mat','ch9364Capture.dat',rx_data,samp_rate_hz,bandwidth_hz,center_freq_khz);
 
 rx_data = typecast(uint16(ho*256+lo),'int16');
 
 if use_maint_2019r1
    rx_data = bitshift(rx_data,  4, 'int16');
    rx_data = bitshift(rx_data, -4, 'int16');
 end
rx_data = double(rx_data);

set(handles.single_capture,'String','单次采集');

samp_freq = str2num(get(handles.rx_samp_freq,'String'));
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% grid ;%%鏄剧ず缃戞牸
if get(handles.protocol,'Value') == 1
    i1 = rx_data(1:4:end);
    q1 = rx_data(2:4:end);
    i2 = rx_data(3:4:end);
    q2 = rx_data(4:4:end);
    plot(handles.axes1,i2,':');
    hold(handles.axes1,'on'); 
    plot(handles.axes1,i1);
    hold(handles.axes1,'on') 
    plot(handles.axes1,q2,':');
    hold(handles.axes1,'on') 
    plot(handles.axes1,q1);
    hold(handles.axes1,'off') 
    IQ=complex(rx_data(1:4:end),rx_data(2:4:end));
else
    i1 = rx_data(1:2:end);
    q1 = rx_data(2:2:end);
%     [~,test1]= findpeaks(i1,"MinPeakHeight",300,"MinPeakDistance",0.1e6);
% test1
    x=1:length(i1);
%     x=x*1000/samp_freq;
    plot(handles.axes1,x,i1);
    hold(handles.axes1,'on') 
    plot(handles.axes1,x,q1);
    hold(handles.axes1,'off') 
    IQ=complex(rx_data(1:2:end),rx_data(2:2:end));
end
bw=str2num(get(handles.rx_rf_bw,'String'));
get_len = str2num(get(handles.get_length,'String'));
samp_freq = str2num(get(handles.rx_samp_freq,'String'));
lo_freq = str2num(get(handles.rx_lo_freq,'String'));
wb_fc = lo_freq*1000000;
wb_fs = samp_freq;
rx_len = length(IQ);
wb_samps = rx_len;


if ishows == 1
window = 4096;
noverlap = window/2;
nfft = window;
a_channel = IQ;
[S,F,T,P]=spectrogram(a_channel,window,noverlap,nfft,wb_fs,'yaxis');
F = (-2048:1:2047)*wb_fs/4096/1e6/2+wb_fc/1e6;
surf(handles.axes3,T*1000,F,10*log10(circshift(P,2048)),'edgecolor','none'); axis tight;
view(handles.axes3,0,90);
title(['fc= ' num2str(wb_fc/1000000) 'Mhz' '  fs= ' num2str(wb_fs/1000000) 'Mhz']);

data = IQ;
ft_len = 2048;       %fft闀垮害
len = length(data);
signal = data(1:floor(len/ft_len)*ft_len); %灏嗘暟鎹暱搴﹀彉涓篺t_len鐨勬暣鍊嶆暟
signal = reshape(signal,[ft_len,floor(len/ft_len)]);
[row,col] = size(signal);
ffta = abs(fft(signal));
ffta = fftshift(ffta); %璁＄畻鐭╅樀fft
for n=1:col            %褰掍竴鍖�1�7
    max_d = 1;         %max(ffta(:,n));
    ffta(:,n) = ffta(:,n)/max_d;
end

wb_samps = ft_len;
xx=wb_fc/1e6 - wb_fs/1e6/2 +(0 : wb_samps - 1)/wb_samps*wb_fs/1e6;
result = sum(ffta,2);   %鑳介噺绱Н
plot(handles.axes2,xx,10*log(result));
%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% 是否启动AI检测
if get(handles.cbAIDetect,'Value') == 1
    func_dispresult()%调用 ai 识别
end

end

save recvdata rx_data
rx_data_save = rx_data;
 set(handles.get_length,'Enable','on');

 %%%%%%%%%%test droineid
% figure(111)
% id = load('zc_droneid.mat').zc_droneid;
% sig = IQ(1:4:end); 
% p=abs(xcorr(sig.',id));
% plot(p)
%%%%%%%%%%%%%%%%%%%%%%%%

function command_Callback(hObject, eventdata, handles)
% hObject    handle to command (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of command as text
%        str2double(get(hObject,'String')) returns contents of command as a double


% --- Executes during object creation, after setting all properties.
function command_CreateFcn(hObject, eventdata, handles)
% hObject    handle to command (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end


% --- Executes on button press in customize_command.
function customize_command_Callback(hObject, eventdata, handles)
% hObject    handle to customize_command (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
global t_client;
txt_send=get(handles.command,'String');
fprintf(t_client,'%s \r\n',txt_send);%鍙戦�1�7佹枃鏈暟鎹�1�7


function tx_samp_freq_Callback(hObject, eventdata, handles)
% hObject    handle to tx_samp_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of tx_samp_freq as text
%        str2double(get(hObject,'String')) returns contents of tx_samp_freq as a double


% --- Executes during object creation, after setting all properties.
function tx_samp_freq_CreateFcn(hObject, eventdata, handles)
% hObject    handle to tx_samp_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function rx_samp_freq_Callback(hObject, eventdata, handles)
% hObject    handle to rx_samp_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of rx_samp_freq as text
%        str2double(get(hObject,'String')) returns contents of rx_samp_freq as a double


% --- Executes during object creation, after setting all properties.
function rx_samp_freq_CreateFcn(hObject, eventdata, handles)
% hObject    handle to rx_samp_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function tx_rf_bw_Callback(hObject, eventdata, handles)
% hObject    handle to tx_rf_bw (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of tx_rf_bw as text
%        str2double(get(hObject,'String')) returns contents of tx_rf_bw as a double


% --- Executes during object creation, after setting all properties.
function tx_rf_bw_CreateFcn(hObject, eventdata, handles)
% hObject    handle to tx_rf_bw (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function rx_rf_bw_Callback(hObject, eventdata, handles)
% hObject    handle to rx_rf_bw (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of rx_rf_bw as text
%        str2double(get(hObject,'String')) returns contents of rx_rf_bw as a double


% --- Executes during object creation, after setting all properties.
function rx_rf_bw_CreateFcn(hObject, eventdata, handles)
% hObject    handle to rx_rf_bw (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function tx_attenuation_Callback(hObject, eventdata, handles)
% hObject    handle to tx_attenuation (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of tx_attenuation as text
%        str2double(get(hObject,'String')) returns contents of tx_attenuation as a double
global t_client;
% tx1_attenuation =  ['tx1_attenuation=' get(handles.tx_attenuation,'String')];
% fprintf(t_client,'%s \r\n',tx1_attenuation);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'tx1_attenuation',get(handles.tx_attenuation,'String'));

% --- Executes during object creation, after setting all properties.
function tx_attenuation_CreateFcn(hObject, eventdata, handles)
% hObject    handle to tx_attenuation (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function rc_agc_mode_Callback(hObject, eventdata, handles)
% hObject    handle to rc_agc_mode (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of rc_agc_mode as text
%        str2double(get(hObject,'String')) returns contents of rc_agc_mode as a double
global t_client;
% rx1_gc_mode =  ['rx1_gc_mode=' get(handles.rc_agc_mode,'String')];
% fprintf(t_client,'%s \r\n',rx1_gc_mode);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'rx1_gc_mode',get(handles.rc_agc_mode,'String'));

% --- Executes during object creation, after setting all properties.
function rc_agc_mode_CreateFcn(hObject, eventdata, handles)
% hObject    handle to rc_agc_mode (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function rx_rf_gain_Callback(hObject, eventdata, handles)
% hObject    handle to rx_rf_gain (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of rx_rf_gain as text
%        str2double(get(hObject,'String')) returns contents of rx_rf_gain as a double
global t_client;
% rx1_rf_gain =  ['rx1_rf_gain=' get(handles.rx_rf_gain,'String')];
% fprintf(t_client,'%s \r\n',rx1_rf_gain);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'rx1_rf_gain',get(handles.rx_rf_gain,'String'));

% --- Executes during object creation, after setting all properties.
function rx_rf_gain_CreateFcn(hObject, eventdata, handles)
% hObject    handle to rx_rf_gain (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function tx_lo_freq_Callback(hObject, eventdata, handles)
% hObject    handle to tx_lo_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of tx_lo_freq as text
%        str2double(get(hObject,'String')) returns contents of tx_lo_freq as a double
global t_client;
% tx_lo_freq =  ['tx_lo_freq=' get(handles.tx_lo_freq,'String')];
% fprintf(t_client,'%s \r\n',tx_lo_freq);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'tx_lo_freq',get(handles.tx_lo_freq,'String'));
% --- Executes during object creation, after setting all properties.
function tx_lo_freq_CreateFcn(hObject, eventdata, handles)
% hObject    handle to tx_lo_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function rx_lo_freq_Callback(hObject, eventdata, handles)
% hObject    handle to rx_lo_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of rx_lo_freq as text
%        str2double(get(hObject,'String')) returns contents of rx_lo_freq as a double
global t_client;

setAd936xConfig(t_client,'rx_lo_freq',get(handles.rx_lo_freq,'String'));
% --- Executes during object creation, after setting all properties.
function rx_lo_freq_CreateFcn(hObject, eventdata, handles)
% hObject    handle to rx_lo_freq (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end


% --- Executes on button press in rf_param_ok.
function rf_param_ok_Callback(hObject, eventdata, handles)
% hObject    handle to rf_param_ok (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

global t_client;
set(hObject,'BackgroundColor',[1 0 0]);
setAd936xConfig(t_client,'tx_lo_freq',get(handles.tx_lo_freq,'String'));
setAd936xConfig(t_client,'rx_lo_freq',get(handles.rx_lo_freq,'String'));
setAd936xConfig(t_client,'tx_samp_freq',get(handles.tx_samp_freq,'String'));
setAd936xConfig(t_client,'rx_samp_freq',get(handles.rx_samp_freq,'String'));
setAd936xConfig(t_client,'tx_rf_bandwidth',get(handles.tx_rf_bw,'String'));
setAd936xConfig(t_client,'rx_rf_bandwidth',get(handles.rx_rf_bw,'String'));
setAd936xConfig(t_client,'tx1_attenuation',get(handles.tx_attenuation,'String'));
setAd936xConfig(t_client,'rx1_gc_mode',get(handles.rc_agc_mode,'String'));
setAd936xConfig(t_client,'rx1_rf_gain',get(handles.rx_rf_gain,'String'));
if get(handles.protocol,'Value') == 1
    setAd936xConfig(t_client,'tx2_attenuation',get(handles.tx_attenuation,'String'));
    setAd936xConfig(t_client,'rx2_gc_mode',get(handles.rc_agc_mode,'String'));
    setAd936xConfig(t_client,'rx2_rf_gain',get(handles.rx_rf_gain,'String'));
end
set(hObject,'BackgroundColor',[0.94 0.94 0.94]);


function get_length_Callback(hObject, eventdata, handles)
% hObject    handle to get_length (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of get_length as text
%        str2double(get(hObject,'String')) returns contents of get_length as a double


% --- Executes during object creation, after setting all properties.
function get_length_CreateFcn(hObject, eventdata, handles)
% hObject    handle to get_length (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end


% --- Executes on selection change in protocol.
function protocol_Callback(hObject, eventdata, handles)
% hObject    handle to protocol (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
index = get(handles.protocol,'Value');
if index == 2
    set(handles.edit19,'Enable','off');
    set(handles.edit20,'Enable','off');
    set(handles.edit21,'Enable','off');
    set(handles.send_phase,'Enable','off');
    set(handles.recv_phase,'Enable','off');
else
    set(handles.edit19,'Enable','on');
    set(handles.edit20,'Enable','on');
    set(handles.edit21,'Enable','on');
    set(handles.send_phase,'Enable','on');
    set(handles.recv_phase,'Enable','on');
end
% Hints: contents = cellstr(get(hObject,'String')) returns protocol contents as cell array
%        contents{get(hObject,'Value')} returns selected item from protocol


% --- Executes during object creation, after setting all properties.
function protocol_CreateFcn(hObject, eventdata, handles)
% hObject    handle to protocol (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: popupmenu controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function ip_Callback(hObject, eventdata, handles)
% hObject    handle to ip (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of ip as text
%        str2double(get(hObject,'String')) returns contents of ip as a double


% --- Executes during object creation, after setting all properties.
function ip_CreateFcn(hObject, eventdata, handles)
% hObject    handle to ip (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function port_Callback(hObject, eventdata, handles)
% hObject    handle to port (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of port as text
%        str2double(get(hObject,'String')) returns contents of port as a double


% --- Executes during object creation, after setting all properties.
function port_CreateFcn(hObject, eventdata, handles)
% hObject    handle to port (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end


% --- Executes on button press in Connect.
function Connect_Callback(hObject, ~, handles)
% hObject    handle to Connect (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

global t_client;
 t_client=tcpip(get(handles.ip,'String'),str2num(get(handles.port,'String')),'NetworkRole','client','TransferDelay','off');%涓庢湰鍦颁富鏈哄缓绔嬭繛鎺ワ紝绔彛鍙蜂负30000锛屼綔涓哄鎴锋満杩炴帴銆�1�7
 t_client.inputBuffersize=2000000;%璁剧疆鏈�1�7澶х紦鍐插尯
 t_client.outputBuffersize=2000000;%璁剧疆鏈�1�7澶х紦鍐插尯
 fopen(t_client);%涓庝竴涓湇鍔″櫒寤虹珛杩炴帴锛岀洿鍒板缓绔嬪畬鎴愯繑鍥烇紝鍚﹀垯鎶ラ敊銆�1�7
if t_client.Status == 'open'
    set(hObject,'ForegroundColor',[0 1 0]); 
    set(handles.tx_lo_freq,'Enable','on');
    set(handles.rx_lo_freq,'Enable','on');
    set(handles.tx_samp_freq,'Enable','on');
    set(handles.rx_samp_freq,'Enable','on');
    set(handles.tx_rf_bw,'Enable','on');
    set(handles.rx_rf_bw,'Enable','on');
    set(handles.tx_attenuation,'Enable','on');
    set(handles.rc_agc_mode,'Enable','on');
    set(handles.rx_rf_gain,'Enable','on');
    set(handles.get_length,'Enable','on');
    set(handles.edit19,'Enable','on');
    set(handles.edit20,'Enable','on');
    set(handles.edit21,'Enable','on');
    set(handles.send_phase,'Enable','on');
    set(handles.recv_phase,'Enable','on'); 
    set(handles.rf_param_ok,'Enable','on'); 
    set(handles.circ_capture,'Enable','on'); 
    set(handles.single_capture,'Enable','on'); 
    set(handles.customize_command,'Enable','on');
    set(handles.data_save,'Enable','on'); 
    set(handles.clear_show,'Enable','on'); 
    set(handles.command,'Enable','on');
    index = get(handles.protocol,'Value');
    if index == 2
        set(handles.edit19,'Enable','off');
        set(handles.edit20,'Enable','off');
        set(handles.edit21,'Enable','off');
        set(handles.send_phase,'Enable','off');
        set(handles.recv_phase,'Enable','off');
    else
        set(handles.edit19,'Enable','on');
        set(handles.edit20,'Enable','on');
        set(handles.edit21,'Enable','on');
        set(handles.send_phase,'Enable','on');
        set(handles.recv_phase,'Enable','on');    
    end
end
% --- Executes on button press in discon.
function discon_Callback(hObject, eventdata, handles)
% hObject    handle to discon (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
global t_client;
 fclose(t_client);%涓庝竴涓湇鍔″櫒寤虹珛杩炴帴锛岀洿鍒板缓绔嬪畬鎴愯繑鍥烇紝鍚﹀垯鎶ラ敊銆�1�7
set(handles.Connect,'ForegroundColor',[0 0 0]);
delete(instrfindall);
set(handles.tx_lo_freq,'Enable','off');
set(handles.rx_lo_freq,'Enable','off');
set(handles.tx_samp_freq,'Enable','off');
set(handles.rx_samp_freq,'Enable','off');
set(handles.tx_rf_bw,'Enable','off');
set(handles.rx_rf_bw,'Enable','off');
set(handles.tx_attenuation,'Enable','off');
set(handles.rc_agc_mode,'Enable','off');
set(handles.rx_rf_gain,'Enable','off');
set(handles.get_length,'Enable','off');
set(handles.edit19,'Enable','off');
set(handles.edit20,'Enable','off');
set(handles.edit21,'Enable','off');
set(handles.send_phase,'Enable','off');
set(handles.recv_phase,'Enable','off'); 
set(handles.rf_param_ok,'Enable','off'); 
set(handles.circ_capture,'Enable','off'); 
set(handles.single_capture,'Enable','off'); 
set(handles.customize_command,'Enable','off'); 
set(handles.data_save,'Enable','off'); 
set(handles.clear_show,'Enable','off'); 
set(handles.command,'Enable','off'); 



% --- Executes when user attempts to close figure1.
function figure1_CloseRequestFcn(hObject, eventdata, handles)
% hObject    handle to figure1 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
% global t_client;
%  fclose(t_client);%涓庝竴涓湇鍔″櫒寤虹珛杩炴帴锛岀洿鍒板缓绔嬪畬鎴愯繑鍥烇紝鍚﹀垯鎶ラ敊銆�1�7
% Hint: delete(hObject) closes the figure
 delete(hObject);



function send_phase_Callback(hObject, eventdata, handles)
% hObject    handle to send_phase (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
global t_client;
% send_phase_compensate =  ['send_phase_compensate=' get(handles.send_phase,'String')];
% fprintf(t_client,'%s \r\n',send_phase_compensate);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'send_phase_compensate',get(handles.send_phase,'String'));
% Hints: get(hObject,'String') returns contents of send_phase as text
%        str2double(get(hObject,'String')) returns contents of send_phase as a double


% --- Executes during object creation, after setting all properties.
function send_phase_CreateFcn(hObject, eventdata, handles)
% hObject    handle to send_phase (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function recv_phase_Callback(hObject, eventdata, handles)
% hObject    handle to recv_phase (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of recv_phase as text
%        str2double(get(hObject,'String')) returns contents of recv_phase as a double
global t_client;
% recv_phase_compensate =  ['recv_phase_compensate=' get(handles.recv_phase,'String')];
% fprintf(t_client,'%s \r\n',recv_phase_compensate);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'recv_phase_compensate',get(handles.recv_phase,'String'));
% --- Executes during object creation, after setting all properties.
function recv_phase_CreateFcn(hObject, eventdata, handles)
% hObject    handle to recv_phase (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function edit19_Callback(hObject, eventdata, handles)
% hObject    handle to edit19 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of edit19 as text
%        str2double(get(hObject,'String')) returns contents of edit19 as a double
global t_client;
% tx2_attenuation =  ['tx2_attenuation=' get(handles.edit19,'String')];
% fprintf(t_client,'%s \r\n',tx2_attenuation);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'tx2_attenuation',get(handles.edit19,'String'));

% --- Executes during object creation, after setting all properties.
function edit19_CreateFcn(hObject, eventdata, handles)
% hObject    handle to edit19 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function edit20_Callback(hObject, eventdata, handles)
% hObject    handle to edit20 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of edit20 as text
%        str2double(get(hObject,'String')) returns contents of edit20 as a double
global t_client;
% rx2_gc_mode =  ['rx2_gc_mode=' get(handles.edit20,'String')];
% fprintf(t_client,'%s \r\n',rx2_gc_mode);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'rx2_gc_mode',get(handles.edit20,'String'));

% --- Executes during object creation, after setting all properties.
function edit20_CreateFcn(hObject, eventdata, handles)
% hObject    handle to edit20 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end



function edit21_Callback(hObject, eventdata, handles)
% hObject    handle to edit21 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of edit21 as text
%        str2double(get(hObject,'String')) returns contents of edit21 as a double
global t_client;
% rx2_rf_gain =  ['rx2_rf_gain=' get(handles.edit21,'String')];
% fprintf(t_client,'%s \r\n',rx2_rf_gain);%鍙戦�1�7佹枃鏈暟鎹�1�7
setAd936xConfig(t_client,'rx2_rf_gain',get(handles.edit21,'String'));

% --- Executes during object creation, after setting all properties.
function edit21_CreateFcn(hObject, eventdata, handles)
% hObject    handle to edit21 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end


% --------------------------------------------------------------------
function device_restart_Callback(hObject, eventdata, handles)
% hObject    handle to device_restart (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

global t_client;
button=questdlg('确认重启设备？','确认选择','确认','取消','确认');
switch button
    case '确认'
        setAd936xConfig(t_client,'ps_reset','1');
        disp('device_restart');
    case '取消'
        disp('cancal_restart');
end




function edit22_Callback(hObject, eventdata, handles)
% hObject    handle to edit22 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hints: get(hObject,'String') returns contents of edit22 as text
%        str2double(get(hObject,'String')) returns contents of edit22 as a double


% --- Executes during object creation, after setting all properties.
function edit22_CreateFcn(hObject, eventdata, handles)
% hObject    handle to edit22 (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    empty - handles not created until after all CreateFcns called

% Hint: edit controls usually have a white background on Windows.
%       See ISPC and COMPUTER.
if ispc && isequal(get(hObject,'BackgroundColor'), get(0,'defaultUicontrolBackgroundColor'))
    set(hObject,'BackgroundColor','white');
end


% --------------------------------------------------------------------
function soft_update_Callback(hObject, eventdata, handles)
% hObject    handle to soft_update (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
[file,path] = uigetfile('*.bin');
file_path = [path,file];
if path == 0
    return;
end
fid = fopen(file_path, 'rb');
bin_9364 = fread(fid,'uint8');
bin_9364 = [bin_9364;171;171;171;171];
length(bin_9364)
global t_client;
fwrite(t_client,bin_9364,'uint8');


% --------------------------------------------------------------------
function sendAd936xData_Callback(hObject, eventdata, handles)
% hObject    handle to sendAd936xData (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)


%%struct I Q I Q....  I/Q range is -2047 -- 2047

global t_client;
[filename,path] = uigetfile('*.mat');
filepath=[path filename];
ad936x = get(handles.protocol,'Value');
if filename == 0
    return;
elseif ad936x == 1
    return;
else
    sigSend = importdata(filepath);
    [row,col] = size(sigSend);
    if col > row
        sigSend = sigSend';
    end
    sigSend = [sigSend ; int16(23130);int16(23130)]; %0x5A5A 
    if length(sigSend)*2 < 30720000111
       fwrite(t_client,sigSend,'int16');
    else
        disp('signal too large');
    end
end
    


% --------------------------------------------------------------------
function signalSel_Callback(hObject, eventdata, handles)
% hObject    handle to signalSel (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)
global IQ;
signal = IQ;
a = inputdlg({'start sample','end sample'},'signal sel');
start = str2num(a{1});
stop  = str2num(a{2});
signal = signal(start:stop);
bw=str2num(get(handles.rx_rf_bw,'String'));
get_len = str2num(get(handles.get_length,'String'));
samp_freq = str2num(get(handles.rx_samp_freq,'String'));
lo_freq = str2num(get(handles.rx_lo_freq,'String'));
wb_fc = lo_freq*1000000;
wb_fs = samp_freq;
rx_len = length(signal);
wb_samps = rx_len;

window = 4096;
noverlap = window/2;
nfft = window;
a_channel = signal;
[S,F,T,P]=spectrogram(a_channel,window,noverlap,nfft,wb_fs,'yaxis');
F = (-2048:1:2047)*wb_fs/window/1e6+wb_fc/1e6;
surf(handles.axes3,T*1000,F,10*log10(circshift(P,2048)),'edgecolor','none'); axis tight;
view(handles.axes3,0,90);
title(['fc= ' num2str(wb_fc/1000000) 'Mhz' '  fs= ' num2str(wb_fs/1000000) 'Mhz']);

data = signal;
ft_len = 2048;       %fft闀垮害
len = length(data);
signal = data(1:floor(len/ft_len)*ft_len); %灏嗘暟鎹暱搴﹀彉涓篺t_len鐨勬暣鍊嶆暟
signal = reshape(signal,[ft_len,floor(len/ft_len)]);
[row,col] = size(signal);
ffta = abs(fft(signal));
ffta = fftshift(ffta); %璁＄畻鐭╅樀fft
for n=1:col            %褰掍竴鍖�1�7
    max_d = 1;         %max(ffta(:,n));
    ffta(:,n) = ffta(:,n)/max_d;
end

wb_samps = ft_len;
xx=wb_fc/1e6 - wb_fs/1e6/2 +(0 : wb_samps - 1)/wb_samps*wb_fs/1e6;
result = sum(ffta,2);   %鑳介噺绱Н
plot(handles.axes2,xx,10*log(result));
%%%%%%%%%%%%%%%%%%%%%%%%%%%%


% --- Executes on button press in cbAIDetect.
function cbAIDetect_Callback(hObject, eventdata, handles)
% hObject    handle to cbAIDetect (see GCBO)
% eventdata  reserved - to be defined in a future version of MATLAB
% handles    structure with handles and user data (see GUIDATA)

% Hint: get(hObject,'Value') returns toggle state of cbAIDetect
