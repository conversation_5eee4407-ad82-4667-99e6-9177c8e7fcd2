function [corr_param] = getcpcorrvals(signal_td, N_fft, cp_len, len_calc)
%  Function    ：getcpcorrvals
%  Description : CP相关峰值计算程序 
%                
%  Parameter   : signal_td   -- 输入信号    (I/Q数据)
%                N_fft       -- FFT点数
%                cp_len      -- CP长度
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-28
len_data = N_fft;
if nargin >= 4
    len_data = len_calc; %输入长序列的情况 
end

%采用滑动窗口取法，依次判别相关峰值
corr_param = zeros(N_fft,1);
for i=1:len_data%N_fft % +-(N-1),表示不包括第1点，会有N-1个间隔，共加起来为N
    sig_cp   =  signal_td(i:i+cp_len-1);            %确定cp起始位置

    j = i+N_fft; % 向后偏移N_fft个数
    sig_ofdata  = signal_td(j:j+cp_len-1);%注意对于复信号, ' 代表共轭转置
    corval      = sig_cp'*sig_ofdata;

    corr_param(i) = abs(corval);%abs(real(corval));
end
end

