clear;
close all;
%clc;
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量
%% demod nb433 gfsk数据文件
%[bindata] = Demodnb433Wave(filename,spos,epos,nb_fc,kBaud, nSampPerSym,nStartPoint);
%生成信号的解析
% [bindata] = Demodnb433Wave('E:\project\tool_dataset\outdataset\test\gendataset\nb_433M\N4.dat', ...
%                         -1,-1,433.8e6,64e3, 32,16);
% [bindata] = Demodnb433Wave('E:\project\tool_dataset\outdataset\test\gendataset\nb_433M\N3.dat', ...
%                         -1,-1,433.8e6,64e3, 32,16);
%采集信号
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\P16.dat', ...
%                         1092110, 1198380,434.35e6, 64e3, 8, 37);%长帧 1.73ms
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\yh1.dat', ...
%                         112391, 248132, 434.21e6, 64e3, 4, 15);%长帧 5.50ms,有帧头
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\y1.bvsp', ...
%                         948769, 1044884, 433.499e6, 64e3, 16, 52);%短帧 1.79ms，无帧头
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\y1.bvsp', ...
%                         1093900, 1429350, 433.499e6, 64e3, 16, 67);%长帧 5.58ms，无帧头
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\y1.bvsp', ...
%                         2498610, 2811530, 433.45e6, 64e3, 16, 57);%长帧 5.58ms，无帧头
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\odoor_5M.bvsp', ...
%                         4091904, 4225024, 4.335e+08, 64e3, 8, 26);
% 
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\odoor_100M.bvsp', ...
%                         601009, 706997, 4.341e+08, 64e3, 8, 33);
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\odoor_Moving.bvsp', ...
%                         2284110+9435, 2401680, 4.34506e+08, 64e3, 8, 29);
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\M0.dat', ...
%                         468144,573212, 4.3451e+08, 64e3, 8, 28);
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_433M\M0.dat', ...
%                         53478,436081, 4.3451e+08, 64e3, 8, 28);
[bindata] = Demodnb433Wave('E:\lz_signaldB\datafiles\LabeledData\nb_433M\CJ0_1M_2381.bvsp', ...
                        295641,530931, 433.304e6, 64e3, 8, 28);
[bindata] = Demodnb433Wave('E:\lz_signaldB\datafiles\LabeledData\nb_433M\CJ0_1M_2795.bvsp', ...
                        722781,1687671, 433.892e6, 64e3, 8, 28);
%% frskyx9d
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_frskyx9d\1.dat', ...
%                          447328+8586+1000, 761682-1000,2456.46e6, 4.8e3*16, 8, 16);%长帧 5.50ms

% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_frskyx9d\1.dat', ...
%                          447328+8586+800, 761682-1000,2456.446e6, 4.8e3*16, 8, 17);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_frskyx9d\7.dat', ...
%                          247841, 507164,2442.966e6, 4.8e3*16, 8, 40);%长帧 5.50ms
%% C2500
% [bindata] = DemodC2500Wave('E:\project\demo_scratch\demo-narrowband\datafiles\c2500\2.bvsp', ...
%                          194924-20, 403121,2404.03e6, 250e3, 8, 6);
%% flyskyfsst8,扩频信号 old
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_flyskyfsst8\1.dat', ...
%                          81958+7476+435,332661,2460.06e6, 4000e3, 2, 24);%长帧 5.50ms

% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_flyskyfsst8\2.dat', ...
%                          258870,499397,2454.06e6, 4000e3, 2, 1+4*2);%长帧 5.50ms

% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_flyskyfsst8\19.dat', ...
%                          275503,517530,2427.14e6, 4000e3, 2, 1+4*2);%长帧 5.50ms
%% flyskyfsst8,扩频信号 new 与FLYSKY_FS16X_2439相同
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_flyskyfsst8\1.dat', ...
%                          189305,261094,2435.5e6, 500e3, 4, 25);%长帧 5.50ms

% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_flyskyfsst8\2.dat', ...
%                          258870,499397,2454.06e6, 4000e3, 2, 1+4*2);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\11.20sample\20241120-135039-1km-flysky-st8-2400-master-checked\20241120-135039\2400-23559.dat', ...
%                          503198,526683,2426.04e6, 4000e3, 2, 1+4*2);%长帧 5.50ms
%% Flysky_PL18
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Flysky_PL18\1.bvsp', ...
%                          67634,165658,2402.5e6, 320e3, 8, 116);%长帧 5.50ms
%% Flysky_PL18new elrs
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_flysky_pl18new\CJ0_1M_1.bvsp', ...
%                          837496,866990,2456.010e6, 1.3e6, 1, 16);%长帧 0.47ms,150e4
%% nb_radiolink_t16d
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_radiolink_t16d\CJ0_1M_1048.bvsp', ...
%                          448492,660961,2454.815e6, 38e3, 4, 10);%nb_radiolink_t16d:152e3:3.47

%% nb_frsky_r9m_fsk
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_frsky_r9m_fsk\CJ0_1M_336.bvsp', ...
%                          386800,755440,911.022e6, 125e3, 2, 92);%nb_frsky_r9m_fsk:250e3:2.1

%% nb_frsky_tw_fsk
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_frsky_tw_fsk\CJ0_1M_1314.bvsp', ...
%                          664490,796210,2410.042e6, 200e3, 3, 18);% 11:nb_frsky_tw_fsk:300e3:2.1
%% nb_Ardupilot 谱有一定问题(目前标记的是图传信号)
%55 55 55 55 AA AA AA AA 96 EA 07 B4 ED 79 DB A6 12 2B AF 13 1A 29 B2 5B
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Ardupilot\35.dat', ...
%                          1139160,1325070,917.796e6, 65e3, 12, 38);%长帧 5.50ms

%% nb_Anxiangdongli 怀疑为扩频信号
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Anxiangdongli\1.bvsp', ...
%                          1078500,1178900,2427.9e6, 1200e3, 4, 20);%长帧 5.50ms

%% nb_Glider
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Glider\1.bvsp', ...
%                          8192+795,43008,2457.05e6, 5e5, 4, 124);%长帧 0.55ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Glider\1.bvsp', ...
%                          98304+1433, 135168,2420.03e6, 5e5, 6, 185);%长帧 0.55ms

%% nb_Jouav
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Jouav\1.dat', ...
%                          958464+125,1005568,2444.04e6, 250e3, 8, 109);%长帧 0.55ms

%% nb_Keweitai_x6lm 怀疑为扩频信号,或者4cpm
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Keweitai_x6lm\1.bvsp', ...
%                          1507328+1194,1617920,2409.48e6, 900e3, 4, 25);
%% nb_Kwt_General_921 不确定
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Kwt_General_921\3.bvsp', ...
%                          5261312+595, 5595136,909.55e6, 75e3, 8, 127);
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Kwt_General_921\1.bvsp', ...
%                          556318, 1055800,921.16e6, 75e3, 8, 62);
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Kwt_General_921\2.bvsp', ...
%                          475670, 1043760, 920.46e6, 125e3, 4, 113, 250e3);
%% nb_rc_Graupner
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_rc_Graupner\1.bvsp', ...
%                          1181190,1390592, 2419.24e6, 256e3, 8, 8, 512e3);
%% nb_rc_Microzone_6cmini
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_rc_Microzone_6cmini\30.dat', ...
%                          604160, 653312, 2426.98e6, 250e3, 8, 131);
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_rc_Microzone_6cmini\30.dat', ...
%                          247808,299008, 2441.98e6, 250e3, 8, 151);
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_rc_Microzone_6cmini\30.dat', ...
%                          485376,536576, 2451.98e6, 250e3, 8, 151);
%% nb_rc_Upair
%52 A2 1E 13 62 80 00 00 A9 01 38 4E 02 34 06 00 A9 01 A0 4E 02 34 0E DE 49 
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_rc_Upair\1.bvsp', ...
%                          225280,290816, 2458.96e6, 200e3, 10, 133);
%52 A2 1E 13 62 80 00 00 A9 01 38 4E 02 34 06 00 A9 01 A0 4E 02 34 0E DE 49 
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_rc_Upair\2.bvsp', ...
%                          419840+300, 485376, 2448.96e6, 200e3, 10, 101);

%% nb_yaboot
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_yaboot\1.bvsp', ...
%                          149504+409,184320, 2443.94e6, 250e3, 8, 176);

%% heiyoung lora (采集信号不正确)
% [bindata] = DemodGfskWave('E:\ftproot\far-field-testing\hyoung\830-1878.dat', ...
%                          181385,238105, 830e6, 30e3, 8, 176);
%% Futaba_433M (为nb433信号)
% [bindata] = Demodnb433Wave('E:\ftproot\signalDB\nb_DIYData\Futaba_433M\1.bvsp', ...
%                         948758, 1044710,433.498e6, 64e3, 8, 27);%长帧 1.73ms

%% FF-433
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\FF-433\3.bvsp', ...
%                         1995550, 2949220,430.99e6, 9.48e3, 54, 110);%长帧 17ms

%% CUAV P9
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\CUAV\CUAV-P9\1.bvsp', ...
%                         189247+3387, 336483,913.894e6, 170.67e3, 12, 378,200e3);%长帧 1.73ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\CUAV\CUAV-P9\1.bvsp', ...
%                         368000, 1331250,913.894e6, 170.67e3, 12, 388,200e3);%长帧 1.73ms

%% CUAV xbee_pro
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\CUAV\xbee_pro\1.bvsp', ...
%                         1695530, 4589440, 921.796e6, 18.28e3, 28, 185,200e3);

%% frsky Frsky_x9dse_r9m_access (x9d区别大,lora)
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\Frsky_x9dse_r9m_access\2.dat', ...
%                          3593, 489887,918.54e6, 15.36e3, 40, 28,400e3);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\Frsky_x9dse_r9m_access\3.dat', ...
%                          138311, 620506,909.049e6, 15.36e3, 40, 21,400e3);%长帧 5.50ms

%% Frsky_x9dse_r9m_accst (x9d区别大,lora)
% [bindata] = DemodLoraWave('E:\ftproot\signalDB\nb_DIYData\Frsky_x9dse_r9m_accst\1.dat', ...
%                          625404, 1112000,920.042e6, 500e3, 8, 1,500e3);%长帧 5.50ms

%% INS54 (宽带信号，未解)
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\INS54\1.bvsp', ...
%                          155464, 357889, 924.47e6, 60e3, 20, 44,150e3);

%% tw_433
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\tw_433\2.bvsp', ...
%                          11189+4389, 1324300,433.401e6, 8e3, 40, 158,40e3);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\tw_433\3.bvsp', ...
%                          702217, 1716840,433.577e6, 8e3, 40, 171,40e3);%长帧 5.50ms

%% Skydroid
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\Skydroid\1.dat', ...
%                          165120, 221673,2439.4e6, 1.3333e6, 3, 129,2e6);% 0.9ms, 极短信号
%% RFD900X
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\RFD900X\1.dat', ...
%                          155464, 357889, 924.47e6, 60e3, 20, 44,150e3);
% [bindata] = DemodGfskWave('E:\project\prj_predict_usrp\saved_hdfs\00-20250515-000128-316.hdfv', ...
%                          45812,87542, 917.09e6, 60e3, 20, 75,150e3);

%% lora （实质为gfsk信号）
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\lora\2.dat', ...
%                          477317, 514202,930.2e6, 250e3, 8, 1,250e3);%长帧 5.50ms
%% VelociDrone crossfire gfsk
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\3.dat', ...
%                          224837, 414808,863.535e6, 80e3, 10, 90,100e3);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\3.dat', ...
%                          224837, 414808,863.535e6, 83.33e3, 12, 113);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\3.dat', ...
%                          1455560, 1643750,864.315e6, 83.33e3, 6, 50);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\6.dat', ...
%                          390145, 577722, 861.45e6, 83.33e3, 6, 53);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\6.dat', ...
%                          801074, 992055, 860.67e6, 83.33e3, 6, 44);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\1.bvsp', ...
%                          386063, 572344, 905.748e6, 83.33e3, 6, 41);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\1.bvsp', ...
%                          793890, 979302, 913.283e6, 83.33e3, 6, 46);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_crossfire_gfsk\otherversion\CJ0_1M_1.bvsp', ...
%                          726877,852214, 907.347e6, 80e3, 1, 46);%信号越界
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\microhard_840\1.dat', ...
%                          451815+4611, 571994,825.5e6, 200e3, 5, 25);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\microhard_840\4.dat', ...
%                          86795, 203830,828.97e6, 200e3, 5, 26);%长帧 5.50ms
%% microhard_pico900
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_DIYData\microhard_pico900\8.dat', ...
%                          1100460, 1228390,912.9025e6, 200e3, 5, 223,200e3);%长帧 5.50ms

%% DEVO10_2427 扩频信号
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\DEVO10_2427\3.bvsp', ...
%                          232978+8000, 282762-1000, 2420.94e6, 0.6e6, 5, 9,1e6);%长帧 5.50ms
%% FLYSKY_FS16X_2439 gfsk信号
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_FLYSKY_FS16X_2439\1.bvsp', ...
%                          849444+2818, 927923, 2438.52e6, 500e3, 4, 122,1e6);%长帧 5.50ms
%% Frsky_x9dse_black 
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Frsky_x9dse_black\1.dat', ...
%                          383351, 649535, 2438.48e6, 80e3, 5, 49,500e3);%长帧 5.50ms
%% Frsky_x9dse_huase 与 Frsky_x9dse_black 相同
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\Frsky_x9dse_huase\2.dat', ...
%                          434661, 700877, 2436.94e6, 80e3, 5, 37,200e3);%长帧 5.50ms
%% Frsky_X9D_plus 与 Frsky_x9dse_black 相同
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\Frsky_X9D_plus\1.bvsp', ...
%                          344464, 615366, 2456.44e6, 80e3, 5, 22,200e3);%长帧 5.50ms
%% FUTABA_T14SG_2430 （narrow band）
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_FUTABA_T14SG_2430\1.bvsp', ...
%                          1704160, 1785450, 2425.00e6, 133.3e3, 3, 5,200e3);%长帧 5.50ms
%% FUTABA_T14SG_2430 （wide band）
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_FUTABA_T14SG_2430\otherversion\CJ0_1M_600.bvsp', ...
%                          669407,758495, 2451.500e6, 1e6, 3, 137,3e6);%长帧 5.50ms
%% nb_crossfire_lora 
% [bindata] = DemodLoraWave('E:\ftproot\signalDB\nb_crossfire_lora\1.bvsp', ...
%                          87179+6437, 472589-6000,927.094e6, 500e3, 8, 1,250e3);%长帧 5.50ms
%% MICROZONE_MC6C_2450 与 nb_rc_Microzone_6cmini 相同
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\MICROZONE_MC6C_2450\2.bvsp', ...
%                          219687, 262479, 2456.98e6, 250e3, 8, 167);
%% HITEC_FLAH8_2430
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_HITEC_FLAH8_2430\2.bvsp', ...
%                          510910, 617388, 2439.78e6, 250e3, 8, 60,250e3);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_HITEC_FLAH8_2430\otherversion\CJ1_40M_817.bvsp', ...
%                          348423,465159, 2433.793e6, 250e3, 8, 60,250e3);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_HITEC_FLAH8_2430\otherversion\CJ1_70M_792.bvsp', ...
%                          1021436,1138172, 2419.426e6, 250e3, 8, 71,250e3);%长帧 5.50ms
%% RadioLink_AT9S_2443 扩频信号
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RadioLink_AT9S_2443\2.bvsp', ...
%                          635622+5000, 758320+1000, 2442.44e6, 1500e3, 4, 19,3e6);%长帧 5.50ms
%% Rediolink_T8F8
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\Rediolink_T8F8\1.bvsp', ...
%                          100126, 678193, 2435.57e6, 40e3, 8, 37,80e3);%长帧 5.50ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Radiolink_T8F8\otherversion\CJ0_1M_639.bvsp', ...
%                          694858,756298, 2421.225e6, 40e3, 8, 37,80e3);%长帧 帧长应该为 0.5ms
%% Spectrum_DX6e_2423
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\Spectrum_DX6e_2423\1.bvsp', ...
%                          987400, 1075740-6000, 2423.18e6, 500e3, 3, 20,800e3);%长帧 1.57ms
%% Spectrum_DX6e_2470
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\Spectrum_DX6e_2470\1.bvsp', ...
%                          1100740+10527, 1197660-5000, 2469.94e6, 500e3, 3, 27,800e3);%长帧 1.57ms
%% TBS_Ethim 8M宽带信号
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\TBS_Ethim\1.dat', ...
%                          324339, 377800, 5779.92e6, 500e3, 3, 27,800e3);
%% TFMODEL_TF8G
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\TFMODEL_TF8G\1.bvsp', ...
%                          467285, 600890, 2425.17e6, 100e3, 3, 4,100e3);
%% WFLY_ET07_2412
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\WFLY_ET07_2412\1.bvsp', ...
%                          858144, 899936, 2424.99e6, 500e3, 3, 94, 500e3);
% [bindata] = DemodGfskWave('E:\software\nxtool\packages\wfly_1km\587.bvsp', ...
%                          363785,409250, 2416.001e6, 500e3, 2, 34, 500e3);
%% WFLY_ET07_2450 与 WFLY_ET07_2412 相同
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\WFLY_ET07_2450\1.bvsp', ...
%                          718586, 763565, 2458.00e6, 500e3, 3, 162, 500e3);
%% WFT_09Sll
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\WFT_09Sll\1.dat', ...
%                          59812, 107271, 2464.00e6, 500e3, 4, 56, 1000e3);
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\WFT_09Sll\1.dat', ...
%                          145222, 183251, 2421.00e6, 500e3, 4, 5, 1000e3);
%% X5 信号太差（不标记）
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_RC\X5\1.dat', ...
%                          145222, 183251, 2421.00e6, 500e3, 4, 5, 1000e3);
%% XK_Detect_X380
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_XK_Detect_X380\11.dat', ...
%                          392348, 492785, 2445.58e6, 250e3, 8, 168, 750e3);
%% nb_HOTRC_HT-8A
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_HOTRC_HT-8A\2.bvsp', ...
%                          4091780+1200, 4144170-1000, 2408e6, 250e3*4, 2, 135, 1000e3);
[bindata] = DemodGfskWave('E:\lz_signaldB\datafiles\WMisclassifiedData\FalsePositive\2025_05_19\13-20250517-051959-241.hdfv', ...
                         64949, 71122, 2402e6, 250e3*4, 2, 41, 1000e3);
%% nb_SIJI300 宽带信号（速率高，不确定什么信号）？？
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_SIJI300\2.bvsp', ...
%                          713710, 725309, 2474.92e6, 250e3*4, 4, 135, 1000e3);
%% nb_siyiD23K
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_siyiD23K\2.bvsp', ...
%                          1063410+15628, 1170520+2000, 2415.23e6, 300e3, 6, 62, 300e3);
% [bindata] = DemodGfskWave('E:\ftproot\11.20sample\20241120-153723-1km-SIYIFM30-2400-master-checked\20241120-153723\2450-8410.dat', ...
%                          1344460, 1369050, 2467.04e6, 300e3, 6, 62, 300e3);
%% nb_feimiX8Pro ???（怀疑为宽带信号）与图传合并在一起
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_feimiX8Pro\2.bvsp', ...
%                          1073520, 1207260, 2405e6, 1000e3, 4, 62, 2000e3);
%% nb_xingqi
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_xingqi\2.bvsp', ...
%                          429406, 526617-15000, 2452.86e6, 250e3, 8, 134, 512e3);% 1.58ms
%% nb_heqi_swan-K1
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_heqi_swan-K1\2380-1833.dat', ...
%                          671770, 692452, 2406.39e6, 1.333e6, 3, 81, 2e6);% 0.33ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_heqi_swan-K1\2380-1833.dat', ...
%                          702086, 758863, 2406.39e6, 1.333e6, 3, 84, 2e6);% 0.33ms
%% nb_weikong-LR900P 不确定信号类型
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_weikong-LR900P\1.bvsp', ...
%                          1470880, 1879740, 304.97e6, 250e3, 8, 134, 512e3);% 1.58ms
%% frsky-900
% [bindata] = DemodGfskWave('E:\ftproot\11.20sample\20241119-153941-3km-frsky-900-master\20241119-153941\3320-26259.dat', ...
%                          606210, 740197, 852.96e6, 250e3, 8, 134, 512e3*2);% 1.58ms
% [bindata] = DemodGfskWave('E:\ftproot\11.20sample\20241120-160425-2km-frsky-x8r-2400-master-checked\20241120-160425\2442-18460.dat', ...
%                          420901, 445524, 2468.04e6, 250e3, 8, 4, 512e3*2);% 1.58ms
% [bindata] = DemodGfskWave('E:\ftproot\11.20sample\20241120-160425-2km-frsky-x8r-2400-master-checked\20241120-160425\2380-18462.dat', ...
%                          1532740, 1556210, 2402.29e6, 250e3*4, 2, 9, 512e3*2);% 1.58ms
%% Ehang
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Ehang\3.bvsp', ...
%                           380422, 450965, 2453.04e6, 512e3, 8, 256, 512e3);% 1.58ms
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Ehang\3.bvsp', ...
%                           810976, 881442, 2463.04e6, 512e3, 8, 253, 512e3);% 1.58ms
%% nb_Feima_D2000 （OFDM）
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Feima_D2000\7.dat', ...
%                           272152, 395433, 844.00e6, 700e3, 2, 2, 700e3);% 1.58ms
%% nb_Hikvision_6150A (gfsk)
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_Hikvision_6150A\1.bvsp', ...
%                           2421560, 2522470, 925.794e6, 120e3, 8, 43, 120e3);% 1.58ms
%% nb_syma_x56w
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_syma_x56w\1.bvsp', ...
%                           194506, 219727, 2453e6, 1e6, 8, 120, 1.5e6);% 1.58ms
                          %194506, 219727, 2453e6, 120e3, 8, 43, 600e3);% 1.58ms
%% nb_xiaomi
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_xiaomi\1.bvsp', ...
%                           949859, 1167110, 2429.78e6, 80e3, 8, 12, 250e3);% 1.58ms
%% nb_blackHnornet
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_blackHnornet\1.bvsp', ...
%                           763066, 823224, 2464.46e6, 160e3, 8, 12, 600e3);% 1.58ms
%% nb_hotrc_ht8a
% [bindata] = DemodGfskWave('E:\ftproot\signalDB\nb_hotrc_ht8a\CJ0_1M_1.bvsp', ...
%                           526985,619145, 2418.000e6, 160e3, 8, 12, 1.2e6);% 1.58ms