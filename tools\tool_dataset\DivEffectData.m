%  Function    ：DivEffectData
%  Description : 基于标注文件，移动无效的原始.bvsp及.meta数据-
%  by          ：lty     
%  Date        : 2025-06-13

IncludedPaths; %统一引入路径
InitMyParams; %初始化路径参数
if exist(myconfig.fpath_labeldef,"file")>0
    fprintf("启动snr添加");
else
    error("文件：%s 不存在！", myconfig.fpath_labeldef);
end

Table_lbldef = readtable(myconfig.fpath_labeldef,'Format','%s %s %s %f %d','Delimiter',';');
Table_lbldef.Properties.VariableNames = ["foldername","meta_subtype","clsname","duration_ms","forwardoffset"];
foldernames = Table_lbldef(:,1).foldername;
nRows = height(Table_lbldef);
sep = filesep; %系统分割符
for i = 1 : nRows
    srcFolder = fullfile(myconfig.folder_labeleddata, foldernames(i));
    fname_recs = fullfile(srcFolder, "Train_records.txt");
    if ~exist(fname_recs, 'file')
        fprintf('文件不存在: %s\n', fname_recs);
        continue;
    end
    fid = fopen(fname_recs, 'r');
    line1 = fgetl(fid);
    fclose(fid);
    
    % 计算分号数量判断格式
    semicolon_count1 = sum(line1 == ';');

    if semicolon_count1 == 2 
        Table_train = readtable(fname_recs,'Format','%s [%d,%d] %f','Delimiter',';');
        Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    else
        Table_train = readtable(fname_recs, 'Format', '%s [%d,%d] %f %f', 'Delimiter', ';');
        Table_train.Properties.VariableNames = ["filename", "startpos", "endpos", "fc",'snr'];
    end
    
    % 提取训练记录中的文件名
    train_files = strings(height(Table_train), 1);
    for iRow = 1 : height(Table_train)
        sigfsubname = strjoin(Table_train.filename(iRow));
        clsname = split(sigfsubname,sep,1);
        train_files(iRow) = clsname(2);
    end

    newFolderName = strcat(clsname(1), '_out');
    newFolderPath = fullfile(srcFolder, newFolderName);
    if ~exist(newFolderPath, 'dir')
    mkdir(newFolderPath);
    fprintf('创建文件夹: %s\n', newFolderPath);
    end
    
    if ~exist(newFolderPath, 'dir')
        mkdir(newFolderPath);
        fprintf('创建文件夹: %s\n', newFolderPath);
    end
    
    % 获取子文件夹中所有.meta文件
    bvsp_files = dir(fullfile(srcFolder, '*.bvsp'));
    bvsp_files = {bvsp_files.name};
    
    if isempty(bvsp_files)
        fprintf('子文件夹 %s 中没有找到.meta文件\n', subfolder_name);
        continue;
    end
    
    % 处理.bvsp文件
    for k = 1:length(bvsp_files)
        bvsp_file = bvsp_files{k};
        bvsp_file_path = fullfile(srcFolder, bvsp_file);
        % 使用fileparts函数分离路径、文件名和扩展名
        [pathstr, name, ext] = fileparts(bvsp_file);
        % 构建对应的.bvsp文件名
        meta_file = [name, '.meta'];
        meta_file_path = fullfile(srcFolder, meta_file);
        % 检查文件是否在训练记录中
      
     if ~any(strcmp(bvsp_file, train_files))
        % 文件不在训练记录中，复制到副本文件夹
        dest_file = fullfile(newFolderPath, bvsp_file);
        
        if exist(dest_file, 'file')
            fprintf('跳过: 文件已存在 %s\n', dest_file);
        else
            % 检查meta文件是否存在
            if exist(meta_file_path, 'file')
                % 两个文件都存在，进行复制
                movefile(bvsp_file_path, dest_file);
                fprintf('已移动文件: %s -> %s\n', bvsp_file_path, dest_file);
                
                % 修改目标文件名以匹配meta文件
                dest_meta_file = fullfile(newFolderPath, meta_file);
                movefile(meta_file_path, dest_meta_file);
                fprintf('已移动文件: %s -> %s\n', meta_file_path, dest_meta_file);
            else
                fprintf('警告: 未找到对应的.meta文件 %s，仅复制.bvsp文件\n', meta_file_path);
                % 只复制bvsp文件
                movefile(bvsp_file_path, dest_file);
                fprintf('已移动文件: %s -> %s\n', bvsp_file_path, dest_file);
            end
        end
    
     else
            fprintf('有效文件：%s\n',bvsp_file_path)
     end
        end
end
