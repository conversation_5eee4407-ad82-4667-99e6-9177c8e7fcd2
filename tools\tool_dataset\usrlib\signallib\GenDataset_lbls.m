function []=GenDataset_lbls(fname_label,nSelCount)
%  Function    ：GenDataset_lbls
%  Description : 将标注文件合并成一个
%  Parameter   : fname_label       -- 类别文件名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-27
[signalDB_dir,~,~]=fileparts(fname_label);
signalDB_dir = strcat(signalDB_dir,'\');
fname_class = fname_label;%strcat(signalDB_dir,"class_def.txt");% 类别文件

if exist(fname_class,"file")>0
    Table_cls = readtable(fname_class,'Format','%d %s %d %f');
    Table_cls.Properties.VariableNames = ["clsid","clsname","bw","Tms"];
else
    error("文件：%s 不存在！", fname_class);
end

sOutfilename_train = '.\output\Train_ds_gen.txt';
sOutfilename_test = '.\output\Val_ds_gen.txt';
nTrainSet = floor(nSelCount*3/4);
nTestSet = floor(nSelCount*1/4);
fprintf('Write file %s 每类训练集: %d\n',sOutfilename_train,nTrainSet);
fprintf('Write file %s 每类验证集：%d\n',sOutfilename_test,nTestSet);
for iRow = 1 : height(Table_cls)
    foldername = char(Table_cls(iRow,2).clsname);
    fname_txt = strcat(signalDB_dir,foldername,'\Train_records.txt');% 类别文件
    if exist(fname_txt,"file")>0
        curflines = readlines(fname_txt);
        fprintf("Append file ID=%d, name: %s, nrows=%d\n",iRow, fname_txt, length(curflines));
        RowIndexs = randperm(nSelCount);%原则上不需要混淆处理,但记录会出自同一个文件
        %RowIndexs = (1:nSelCount);
        RowIDs_train = RowIndexs(1:nTrainSet);
        RowIDs_test = RowIndexs(nTrainSet+1:end);
        if iRow==1
            writelines(curflines(RowIDs_train),sOutfilename_train,'WriteMode','overwrite');
            writelines(curflines(RowIDs_test),sOutfilename_test,'WriteMode','overwrite');
        else
            writelines(curflines(RowIDs_train),sOutfilename_train,'WriteMode','append');
            writelines(curflines(RowIDs_test),sOutfilename_test,'WriteMode','append');
        end
    else
        fprintf("文件：%s 不存在！\n", fname_txt);
    end
end

end