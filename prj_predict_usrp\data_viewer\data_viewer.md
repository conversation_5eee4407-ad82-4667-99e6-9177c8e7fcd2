# 数据文件可视化查看工具 (Data File Visualization Tool)

## 简介

这是一个基于 Python 和 Tkinter 的多功能数据分析工具，集成了数据文件可视化、深度学习模型推理、信号段分析等功能。主要用于查看和分析信号数据文件，并提供 AI 模型推理能力。

## 核心功能

### 1. 数据文件可视化
- **多格式支持**：支持 .bvsp、.dat、.hdfv 数据文件
- **四种图表显示**：
  - 时域信号点值图（X 轴以 ×10^5 为单位显示，支持滚轮缩放）
  - 时域信号图（时间轴，ms）
  - 频谱图（频率轴，MHz，MATLAB 兼容计算）
  - 时频图（时间-频率图，严格按 MATLAB 方式计算）
- **2D/3D 视图切换**（3D 需要 torch 库）
- **文件导航**：支持文件夹浏览和前/后文件切换
- **智能抽样**：大数据量时自动抽样优化显示性能
- **交互式缩放**：时域信号点值图和频谱图支持鼠标滚轮横向缩放查看信号细节

### 2. 深度学习模型推理功能
- **模型选择**：支持加载 .pth 格式的深度学习模型
- **类别定义**：支持自定义 class_def.txt 类别定义文件
- **实时推理**：对当前数据文件进行 AI 模型推理
- **结果分析**：显示推理结果、置信度、类别信息
- **多线程处理**：避免界面卡顿，后台执行推理
- **信号段标记**：自动在图表上标记检测到的信号段

### 3. 信号段分析功能
- **位置信息显示**：显示每个信号段的起止位置
- **信号参数**：显示信号长度、持续时间、中心频率
- **详细分析**：包含类别 ID、置信度、SNR 等完整信息
- **可视化标记**：基于模型推理结果自动标记信号段
- **统计汇总**：提供类别分布和检测统计信息

### 4. 高级界面交互功能
- **可调节面板**：支持拖拽调节面板高度的 PanedWindow 系统
- **折叠功能**：各面板支持折叠/展开，节省空间
- **滚轮缩放**：时域信号点值图支持 1-50 倍横向缩放查看细节
- **多语言支持**：完整的中英文界面切换
- **1.5 倍字体放大**：改善可读性，支持跨平台字体优化

## 环境要求

### 必需依赖
- Python 3.6+
- numpy >= 1.19.0
- matplotlib >= 3.3.0
- scipy >= 1.5.0
- tkinter（通常随 Python 安装）
- h5py >= 2.10.0

### 可选依赖
- torch >= 1.7.0（3D 显示和模型推理功能）

### 项目依赖
- 需要在 deepsc_research 项目根目录下运行
- 依赖项目中的 chanlib、usrlib、nets 等模块
- 需要 class_def.txt 配置文件（用于模型推理）

## 安装配置

### 1. 环境准备
```bash
# 确保在项目根目录
cd deepsc_research

# 安装必需依赖
pip install numpy matplotlib scipy h5py

# 可选：安装 torch（用于 3D 显示和模型推理）
pip install torch

# 运行程序
python prj_predict_usrp/data_viewer/data_viewer.py
```

### 2. 使用启动脚本（推荐）
```bash
# 进入 data_viewer 目录
cd prj_predict_usrp/data_viewer

# 运行启动脚本
start.bat
```

### 3. 手动安装依赖
```bash
pip install numpy matplotlib scipy h5py torch
```

## 使用方法

### 1. 启动程序

**方法一：使用启动脚本（推荐）**
```bash
cd prj_predict_usrp/data_viewer
start.bat
```

**方法二：直接运行 Python 脚本**
```bash
cd deepsc_research
python prj_predict_usrp/data_viewer/data_viewer.py
```

### 2. 基本数据查看流程

1. **选择文件**：
   - 点击"选择文件"按钮选择单个数据文件
   - 或点击"选择文件夹"浏览整个文件夹

2. **查看数据**：
   - 程序会自动加载并显示四种图表
   - 使用"上一个"/"下一个"按钮切换文件
   - 点击"切换视图"在 2D/3D 模式间切换
   - 在时域信号点值图上使用鼠标滚轮缩放查看细节

3. **文件信息**：
   - 左侧显示文件基本信息
   - 包括文件大小、采样率、中心频率等

### 3. 深度学习模型推理流程

1. **首次使用**：
   - 点击"选择类别定义"按钮选择 class_def.txt 文件
   - 点击"选择模型"按钮选择 .pth 模型文件

2. **执行推理**：
   - 确保已加载数据文件
   - 点击"模型推理"按钮
   - 等待推理完成（多线程后台执行）

3. **查看结果**：
   - 推理结果面板会自动展开
   - 显示每个信号段的详细信息
   - 包含位置、类别、置信度等信息
   - 图表上会自动添加信号段标记

### 4. 界面操作技巧

1. **面板调节**：
   - 拖拽面板间的分割线调节高度
   - 点击面板标题栏的折叠按钮（▼/▶）

2. **推理结果操作**：
   - 右键点击结果区域显示菜单
   - 支持复制全部、复制选中、清空结果等
   - 快捷键：Ctrl+C 复制、Ctrl+A 全选、Delete 清空

3. **滚轮缩放操作**：
   - 将鼠标移动到时域信号点值图或频谱图上
   - 向上滚动：放大图形（最大 50 倍）
   - 向下滚动：缩小图形（最小为默认大小）
   - 缩放中心：以鼠标光标位置为中心进行缩放
   - 自动边界：缩放范围自动限制在原始数据范围内
   - 加载新数据时自动重置为默认大小

4. **键盘快捷键**：
   - F5：展开推理结果面板
   - Ctrl+C：复制选中内容
   - Ctrl+A：全选内容
   - Delete：清空结果
   - Escape：关闭弹窗
   - Enter：确认弹窗

## 支持的文件格式

### .bvsp/.dat 文件
- 二进制信号捕获文件
- 使用 Read_sigfile 函数解析，与项目其他模块保持一致
- 包含完整的元数据信息（采样率、中心频率、带宽等）
- 推荐格式，信息最完整

### .hdfv 文件
- HDF5 格式文件
- 使用 read_signal_from_hdf5 函数解析
- 支持复杂数据结构
- 包含完整的信号信息和时间戳

## 深度学习模型推理技术说明

### 1. 支持的模型格式
- PyTorch 模型（.pth 文件）
- 需要与项目中的网络结构兼容
- 支持分类任务的深度学习模型
- 使用 Arcface 网络架构

### 2. 类别定义文件格式
文件名：class_def.txt
格式：类别ID:类别名称:带宽:最短时间(ms)

示例：
```
0:nb_RFD900X:384e3:3.34
1:nb_crossfire_gfsk:80e3:2
2:nb_crossfire_lora:250e3:6
3:nb_HITEC_FLAH8_2430:465e3:1.88
4:nb_433M:384e3:1.73
5:nb_radiolink_t8f8:152e3:9.3
6:nb_radiolink_t16d:152e3:10.16
7:nb_elrs_128x:812.5e3:1.55
8:nb_frsky_td_fsk:250e3:3.5
9:nb_flysky_pl18new:1500e3:0.5
```

### 3. 推理流程
1. 使用 `proc_wbsig` 函数处理原始信号
2. 生成临时信号段数据集文件
3. 调用 `predict_classify_proc_all` 进行批量推理
4. 解析推理结果并显示详细分析
5. 自动在图表上标记检测到的信号段
6. 清理临时文件

### 4. 推理结果格式
```
文件: example_signal.bvsp
检测到 2 个信号段
==================================================

信号段 1:
  类别: nb_elrs_128x
  置信度: 99.0%
  类别ID: 7
  位置: [338848, 547839]
  长度: 208992 点 (3.40 ms)
  中心频率: 2422.39 MHz
  带宽: 812.50 kHz
  信噪比: 25.3 dB

信号段 2:
  类别: nb_crossfire_lora
  置信度: 95.5%
  类别ID: 2
  位置: [647840, 856831]
  长度: 208992 点 (3.40 ms)
  中心频率: 2422.39 MHz
  带宽: 250.00 kHz
  信噪比: 22.1 dB

==================================================
汇总统计:
有效预测: 2/2

检测到的类别分布:
  nb_elrs_128x: 1 次
  nb_crossfire_lora: 1 次

技术信息:
  阈值设置: 0.7 (70%)
  处理信号段: 2 个
  数据来源: fchanscan-S1.hdf5
  采样率: 61.44 MHz
  信号带宽: 12.00 MHz
```

## 性能优化技术

### 1. 显示优化
- **智能抽样**：超过 10 万点时自动抽样显示
- **多线程计算**：FFT 和时频图并行计算
- **内存优化**：大文件时分段处理
- **自适应策略**：根据信号长度选择最优处理方式

### 2. 推理优化
- **多线程推理**：避免界面卡顿
- **批量处理**：高效的批量推理
- **内存管理**：自动清理临时文件
- **错误恢复**：完善的异常处理机制

### 3. 界面优化
- **延迟加载**：按需加载界面组件
- **异步更新**：异步更新界面状态
- **缓存机制**：缓存计算结果
- **响应式设计**：1.5 倍 UI 缩放改善可读性

## 技术实现细节

### 1. 信号处理算法
- **MATLAB 兼容**：FFT 和时频图计算严格按 MATLAB 方式实现
- **精确频率轴**：nb_freqs = fc - fs/2 + (0:N-1)*fs/N
- **自适应参数**：根据信号长度动态调整计算参数
- **多策略 FFT**：直接 FFT、智能抽样、分段平均三种策略

### 2. 3D 显示技术
- **PyTorch STFT**：使用 PyTorch 进行高效 STFT 计算
- **功率谱密度**：3D 功率谱密度可视化
- **FIR 滤波**：48 阶低通滤波器处理
- **多视角显示**：不同角度的 3D 可视化

### 3. 界面架构
- **现代化界面**：基于 Tkinter 的现代化界面设计
- **可调节布局**：PanedWindow 实现可调节面板高度
- **多线程处理**：避免界面卡顿的多线程架构
- **事件驱动**：matplotlib 事件监听实现交互式缩放

### 4. 数据处理
- **统一接口**：使用 Read_sigfile 函数确保参数一致性
- **多格式支持**：自动识别和处理不同格式的数据文件
- **错误恢复**：智能错误恢复和备用方案
- **元数据管理**：完整的信号元数据提取和管理

## 故障排除

### 1. 程序启动问题
- 确保在 deepsc_research 根目录运行
- 检查 Python 环境和依赖包版本
- 确认 matplotlib 后端支持 GUI 显示

### 2. 模型加载问题
- 确保模型文件格式正确（.pth）
- 检查 class_def.txt 文件格式是否符合规范
- 确认文件路径中没有中文字符
- 验证模型与当前项目版本兼容性

### 3. 推理失败问题
- 检查数据文件是否完整且格式正确
- 确认模型与数据格式兼容
- 查看控制台错误信息定位问题
- 确保有足够的内存进行推理计算

### 4. 界面响应问题
- 大文件处理时请耐心等待（多线程后台处理）
- 推理过程中避免频繁操作界面
- 如遇界面卡死，检查是否有计算任务在后台运行
- 必要时重启程序清理资源

### 5. 显示问题
- 确保信号频率在合理范围内
- 检查采样率和中心频率设置
- 验证文件数据完整性
- 如果图表显示异常，尝试切换 2D/3D 视图

### 6. 字体显示问题
- Linux 系统确保安装了中文字体支持
- Windows 系统确保系统字体完整
- 如字体显示异常，程序会自动回退到系统默认字体

## 文件结构

```
prj_predict_usrp/data_viewer/
├── data_viewer.py           # 主程序（数据查看与模型推理工具）
├── data_viewer.md          # 说明文档
├── requirements.txt        # Python 依赖包列表
├── start.bat              # Windows 启动脚本
├── example_class_def.txt  # 示例类别定义文件
└── __pycache__/          # Python 缓存目录
```

## 版本历史

### v2.1 (2025-07-16)
- **优化**: 改进界面响应性能和用户体验
- **修复**: 多线程处理中的潜在问题
- **增强**: 错误处理和异常恢复机制
- **文档**: 更新完整的使用说明和故障排除指南

### v2.0 (2025-01-20)
- 完整重构代码注释和文档结构
- 优化多线程计算性能
- 增强 MATLAB 兼容性
- 改进错误处理和用户体验
- 新增交互式滚轮缩放功能
- 支持信号段自动标记

### v1.5 (2025-07-03)
- 增加模型推理功能
- 支持 3D 显示模式
- 优化界面布局和字体显示
- 新增多语言支持

### v1.0 (2025-06-24)
- 初始版本发布
- 基本数据可视化功能
- 支持多种文件格式

## 使用示例

### 1. 基本使用流程
```bash
# 1. 进入目录
cd prj_predict_usrp/data_viewer

# 2. 启动程序
start.bat

# 3. 选择数据文件（.bvsp/.dat/.hdfv）
# 4. 查看四种图表显示
# 5. 选择模型文件（.pth）和类别定义文件
# 6. 执行模型推理
# 7. 查看推理结果和信号段标记
```

### 2. 高级功能使用
- 使用面板折叠功能优化界面布局
- 使用滚轮缩放查看信号细节
- 使用右键菜单复制推理结果
- 使用快捷键快速操作
- 调节面板高度适应不同显示需求
- 切换 2D/3D 视图模式

### 3. 性能优化建议
- 对于大文件，等待智能抽样完成后再进行交互
- 推理过程中避免同时加载多个大文件
- 定期清理推理结果以释放内存
- 使用合适的缩放级别查看信号细节

## 相关工具

### chanscan_viewer
本项目还包含一个专门的信道扫描分段查看工具 `chanscan_viewer`，它基于 `data_viewer` 修改而来：

**主要区别**：
- **data_viewer**: 专注于数据可视化和模型推理
- **chanscan_viewer**: 专注于信道扫描分段和向量数据库管理

**功能对比**：
| 功能 | data_viewer | chanscan_viewer |
|------|-------------|-----------------|
| 数据可视化 | ✓ | ✓ |
| 模型推理 | ✓ | ✗ |
| 信道扫描分段 | ✗ | ✓ |
| 向量数据库管理 | ✗ | ✓ |
| 分段结果管理 | ✗ | ✓ |

**使用建议**：
- 需要模型推理和信号分析时使用 `data_viewer`
- 需要信道扫描和数据库管理时使用 `chanscan_viewer`

---

**开发者**: Caonairui
**最后更新**: 2025-07-16