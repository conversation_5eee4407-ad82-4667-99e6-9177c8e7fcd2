function []=func_dispresult(Sig_filename)
%可放在命令行中执行
fprintf("[start]AI识别采集信号过程...\n")
InitMyParams;
chscanfile = myconfig.CaptureParseOut ;
[filepath, ~, ~] = fileparts(chscanfile);
if exist(chscanfile,"file")>0
    delete(chscanfile);
elseif exist(filepath,"dir")<=0
    warning("路径:%s 不存在,请创建", filepath)
    return;
end
if exist("Sig_filename","var")<=0
    Sig_filename = '.\ch9364Capture.dat';
elseif exist(Sig_filename,"file")<=0
    Sig_filename = '.\ch9364Capture.dat';    
end
%捕获信号
[nSigChecked] = func_scansignal(Sig_filename, 4);    %fc=433M;0:nb_433M:256e3
%[nSigChecked] = func_scansignal('.\ch9364Capture.dat', 4, 1.73,256e3,0);    %fc=433M;0:nb_433M:256e3

%[nSigChecked] = func_scansignal('.\ch9364Capture.dat',13, 3,312e3); %fc=2435M;13:nb_frskyx9d2:312e3
%[nSigChecked] = func_scansignal('.\ch9364Capture.dat',7, 2,80e3); %fc=915M;7:nb_crossfire_gfsk:80e3 
%[nSigChecked] = func_scansignal('.\ch9364Capture.dat',20,6,250e3,-1600);%fc=915M;;20:nb_crossfire_lora:250e3

%[nSigChecked] = func_scansignal('.\ch9364Capture.dat',6,3.34,150e3);%fc=924M;6:nb_RFD900X:150e3
%[nSigChecked] = func_scansignal('.\ch9364Capture.dat',23,1.8,200e3);%fc=2430M;23:nb_HITEC_FLAH8_2430:200e3
%[nSigChecked] = func_scansignal('.\ch9364Capture.dat', 19, 1.3,200e3);%fc=2430M;19:nb_FUTABA_T14SG_2430:200e3
%[nSigChecked] = func_scansignal('.\sam_evo\433\ch9364Capture433-5.dat',0,2,256e3,1000);

%解析采集文件
%[nSigChecked] = func_scansignal('E:\software\nxtool\packages\test-packages\TBS.915.GSFK\4.bvsp',7,4,80e3);%fc=915M;7:nb_crossfire_gfsk:80e3
%[nSigChecked] = func_scansignal('E:\software\nxtool\packages\test-packages\TBS.868.lora\5.bvsp',20,6,250e3);%fc=915M;;20:nb_crossfire_lora:250e3

fprintf("检测到信号个数=%d\n",nSigChecked);

if nSigChecked>0
    web('http://127.0.0.1:5000/classify?fid=2')
end
fprintf("[end]AI识别采集信号过程...\n")
end