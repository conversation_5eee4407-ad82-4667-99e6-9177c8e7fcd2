function [white_bits]=scramble_nb433(inBits)
%输入为列向量
init_state=ones(1,9);
all_state=[];
for i1=1:length(inBits)/8
    for num=1:8
        next_state=init_state;
        next_state(1)=bitxor(next_state(4),next_state(9));
        init_state=[next_state(1)  init_state(1:end-1)];
    end
    all_state=[all_state flip(init_state(1:end-1))];
end
%pn9hex=dec2hex(bi2de(reshape(all_state(1:length(inBits))',8,length(all_state(1:length(inBits)))/8)', 'left-msb'));
white_bits = bitxor(inBits, double(all_state(1:length(inBits))).');
end