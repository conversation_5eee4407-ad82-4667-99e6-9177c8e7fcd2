import torch
import torch.backends.cudnn as cudnn
import sys

from nets.arcface import Arcface
from utils.dataloader import LSWDataset
from utils.utils_metrics import test
from usrlib.usrlib import  read_dataset_lines
from usrlib.usrlib import *

if __name__ == "__main__":
    #--------------------------------------#
    #   是否使用Cuda
    #   没有GPU可以设置成False
    #--------------------------------------#
    cuda            = True
    #--------------------------------------#
    #   主干特征提取网络的选择
    #   mobilefacenet
    #   mobilenetv1
    #   iresnet18
    #   iresnet34
    #   iresnet50
    #   iresnet100
    #   iresnet200
    #--------------------------------------#
    backbone        = "DroneSigNet"
    #--------------------------------------#
    #   训练好的权值文件
    #--------------------------------------#
    model_path      = "logs/ep100-loss0.098-val_loss0.291.pth"
        #--------------------------------------------------------#
    #   指向根目录下的分类文件，读取信号路径与标签
    #--------------------------------------------------------#
    clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:', cls_count)
    #--------------------------------------#
    #   LSW评估数据集的文件路径
    #   以及对应的txt文件
    #--------------------------------------#
    lsw_pairs_path  = "model_data/lsw_pair.txt"
    #--------------------------------------#
    #   评估的批次大小和记录间隔
    #--------------------------------------#
    batch_size      = 64
    log_interval    = 1
    #--------------------------------------#
    #   ROC图的保存路径
    #--------------------------------------#
    png_save_path   = "model_data/roc_test.png"

    test_loader = torch.utils.data.DataLoader(
        LSWDataset(pairs_path=lsw_pairs_path), batch_size=batch_size, shuffle=False)

    model = Arcface(num_classes=cls_count, backbone=backbone, mode="predict")

    print('Loading weights into state dict...')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.load_state_dict(torch.load(model_path, map_location=device), strict=False)
    model  = model.eval()

    if cuda:
        model = torch.nn.DataParallel(model)
        cudnn.benchmark = True
        model = model.cuda()

    test(test_loader, model, png_save_path, log_interval, batch_size, cuda)
