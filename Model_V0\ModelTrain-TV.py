# =======================================================================================================================
#   Function    ：ModelTrain.py
#   Description : Model训练
#                 读入hdf5文件，并对其进行分类识别、arc特征向量提取
# 
#   Parameter   : ../data/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2024-09-06
# =======================================================================================================================
import os
import numpy as np

from net.modelDesign import *
from usrlib.dataloader import *
from usrlib.usrlib import *
from usrlib.callbacks import *
from torch.utils.tensorboard import SummaryWriter

from sklearn.metrics import confusion_matrix
from sklearn.metrics import ConfusionMatrixDisplay
import matplotlib.pyplot as plt

import sys
print(sys.argv)
import getopt


# os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
# os.environ['TORCH_USE_CUDA_DSA'] = '1'
# =========================================================
# Parameters Setting
# ========================================================
EPOCHS = 30#10000
bestLoss = 2#0.005
stopLoss = 0.0001
batch_size = 32

dataset_file = "" # 数据文件路径
modeltype =  1 #模型类别 0:分类模型 1:指纹特征模型
clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件

if __name__ == "__main__":
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    # 命令行方式获取训练文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['help','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modetype'):
            modeltype = int(opt_value)
        if opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something

    print("The model type is ", modeltype)
    #exit() 调试用
    if modeltype == 0:
        fname_model = "DroneSigCls.pth"
    else:    
        fname_model = "DroneSigArc.pth"

    if dataset_file=="":
        datasettype = 'train'
        subdir = 'base' # base,noised,multipath
        train_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)

        dataset_nb = "nb-dataset-S1.hdf5" #数据本身文件  
        dataset_file = os.path.join(train_dataset_dir, dataset_nb)

    (curpath, curfilename) = os.path.split(dataset_file)

    model_dir = './PretrainedModel/'
    model_saved = os.path.join(model_dir, fname_model)
    b_loadmodel = False
    if os.path.exists(model_saved):
        b_loadmodel = True
    # =========================================================    
    # Data Loading
    # ========================================================
    print('===load data: {0} ==============='.format(dataset_file))

    
    rx_signal_train,class_id_train,class_name_train,fs_value_train, bw_value_train = LoadSpecialMultiHdfsDataset(dataset_dir,'dataset-train')
    rx_signal_val,class_id_val,class_name_val,fs_value_val, bw_value_val = LoadSpecialMultiHdfsDataset(dataset_dir,'dataset-val')
    train_datahandler = MDataHandler(rx_signal_train,class_id_train,class_name_train,fs_value_train, bw_value_train)
    val_datahandler = MDataHandler(rx_signal_val,class_id_val,class_name_val,fs_value_val, bw_value_val)
    train_loader = torch.utils.data.DataLoader(train_datahandler, batch_size=batch_size, shuffle=True)
    val_loader = torch.utils.data.DataLoader(val_datahandler, batch_size=batch_size, shuffle=True)

    if batch_size > class_id_train.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = class_id_train.size  

    tb_writer = SummaryWriter(log_dir='logs') # tensorboard
    # train_ratio = 0.99
    # rx_signal_train = rx_signal[:int(rx_signal.shape[0] * train_ratio)]
    # rx_signal_val = rx_signal[int(rx_signal.shape[0] * train_ratio):]
    # class_id_train = class_id[:int(class_id.shape[0] * train_ratio)]
    # class_id_val = class_id[int(class_id.shape[0] * train_ratio):]
    # class_name_train = class_id[:int(class_name.shape[0] * train_ratio)]
    # class_name_val = class_id[int(class_name.shape[0] * train_ratio):]

    # =========================================================
    # Model Constructing
    # ========================================================
    # 设置设备索引为1（使用第二个GPU，索引从0开始计数）
    #torch.cuda.set_device(1)
    torch.cuda.set_device(0)

    Model = DroneSig_classifier(nc=cls_count, ModelType=modeltype)
    Model = Model.cuda()

    criterion = nn.CrossEntropyLoss().cuda()
    optimizer_type = "Adam" # "Sgd", "Adam"
    opt_lr = 1e-4
    weight_decay = 5e-4

    lr_step = 10
    lr_decay = 0.95  # when val_loss increase, lr = lr*lr_decay

    if optimizer_type == "Adam":
        optimizer = torch.optim.Adam(Model.parameters(), lr=opt_lr, weight_decay=weight_decay)
    else:
        optimizer = torch.optim.SGD(Model.parameters(), lr=opt_lr, weight_decay=weight_decay)

    scheduler = torch.optim.lr_scheduler.MultiStepLR(optimizer, milestones=[5000,7000,9000], gamma=0.5)

    #scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=lr_step, gamma=0.5)
    #scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10,verbose=False, threshold=0.0001, threshold_mode='rel', cooldown=0, min_lr=0, eps=1e-08)
    #torch.optim.Adam([{'params': model.parameters()}, {'params': metric_fc.parameters()}], lr=opt.lr, weight_decay=opt.weight_decay)

    (bestLoss, start_epoch) = Init_model(Model, b_loadmodel, model_saved)
    start_epoch = 0
    print("The bestloss is {0:0.5f} epoch= {1}".format(bestLoss, start_epoch)) 
    # 用于存储真实标签和预测标签
    true_labels = []
    predicted_labels = []
    num_train = len(class_id_train)
    num_val = len(class_id_val)

    # =========================================================
    # Model Training and Saving
    # =========================================================
    for epoch in range(start_epoch, EPOCHS):
        epoch_params = Epoch_params(epoch, EPOCHS, num_train, num_val, batch_size)
        
        # (loss, acc, val_loss, val_acc, val_label, val_predict) = Train_one_epoch(Model, optimizer, scheduler, criterion, batch_size, train_datahandler, val_datahandler)
        # print('Epoch: [{0}/{1}]\t' 'F:{2}\t' 'Loss {loss:.5f}\t' 'Acc {acc:.5f}\t' 'val_loss {val_loss:.5f}\t' 'val_acc {val_acc:.5f}\t'.format(
        #             epoch+1, EPOCHS, curfilename, loss=loss, acc=acc, val_loss=val_loss, val_acc=val_acc))
        
        (loss, acc, val_loss, val_acc, val_label, val_predict) = Train_one_epoch_all(Model, optimizer, scheduler, criterion, train_loader, val_loader, epoch_params)
        true_labels.extend(val_label)
        predicted_labels.extend(val_predict)    

        
        # tb_writer.add_scalar('loss', loss, epoch)
        # tb_writer.add_scalar('val_loss', val_loss, epoch)
        tb_writer.add_scalars('multiloss', {'loss':loss, 'val_loss':val_loss}, epoch) #
        tb_writer.add_scalars('multiAcc', {'acc':acc, 'val_acc':val_acc}, epoch) #
        # tb_writer.flush()

        if val_loss < bestLoss:
            # Model saving
            bestLoss = val_loss
            checkpoint = {
                "net": Model.state_dict(),
                "bestLoss":bestLoss,
                "epoch": epoch
            }
            if epoch > 0.01*EPOCHS:
                #torch.save(Model.state_dict(), model_saved, _use_new_zipfile_serialization=False)
                torch.save(checkpoint, model_saved, _use_new_zipfile_serialization=False)
                print("Model: {0} saved, bestloss is {1:0.5f} ".format(model_saved, bestLoss))      

            if val_loss < stopLoss:
                print("Loss = {0:0.5f} < StopLoss = {1:0.5f} Exit".format(val_loss, stopLoss))
                break  

    #tb_writer.add_graph(Model, rgb)
    tb_writer.flush()
    tb_writer.close()
    print('Training for {0} is finished! bestloss is {1:0.5f} '.format(model_saved, bestLoss))

    # 计算混淆矩阵
    cm = confusion_matrix(true_labels, predicted_labels)

    # 创建ConfusionMatrixDisplay对象并进行可视化
    disp = ConfusionMatrixDisplay(confusion_matrix=cm)
    # 根据1920x1080像素和96dpi进行换算，得到对应的英寸尺寸
    width_inches = 1080 / 96
    height_inches = 1080 / 96
    fig, ax = plt.subplots(figsize=(width_inches, height_inches))  # 可根据需要调整具体尺寸
    disp.plot(values_format='.2g',text_kw={'size': 9},ax=ax, colorbar=False)

    plt.rcParams['font.size'] = 9  # 可根据需要调整具体数值
    # 调整子图布局，不留白边
    plt.subplots_adjust(left=0.05, right=0.95, bottom=0.05, top=0.95) #left=0.1表示子图左边距离图形左边的距离为整个图形宽度的 10%。right=0.9表示子图右边距离图形右边的距离为整个图形宽度的 90%。
    plt.savefig(os.path.join("logs", "ClassificationMatrix.png"))
    #plt.show()



# 查看tensorboard变量
# 设置路径，输入tensorboard --logdir=./logs
# http://localhost:6006/
#
