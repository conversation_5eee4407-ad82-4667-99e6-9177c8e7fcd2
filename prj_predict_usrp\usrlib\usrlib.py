# =======================================================================================================================
#   Function    ：usrlib.py
#   Description : 用户库函数文件
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-1-20
# =======================================================================================================================
import os
import numpy as np
import torch
import h5py
import math
from functools import partial
import json

def get_config_path():
    """
    智能获取config文件夹中sig-vectordB.cfg的正确路径

    优先级逻辑：
    1. 如果文件存在，直接返回该路径
    2. 如果文件不存在但目录存在，优先在已存在的目录中创建文件
    3. 如果目录也不存在，返回默认路径

    Returns:
        str: 配置文件的完整路径
    """
    # 可能的路径列表（按优先级排序）
    possible_paths = [
        "./config/sig-vectordB.cfg",      # 从 prj_predict_usrp 目录运行
        "../config/sig-vectordB.cfg",     # 从子目录（如 chanscan_viewer）运行
        "../../config/sig-vectordB.cfg",  # 从更深的子目录运行
    ]

    # 首先检查文件是否存在
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果文件都不存在，检查目录是否存在，优先在已存在的目录中创建文件
    for path in possible_paths:
        config_dir = os.path.dirname(path)
        if os.path.exists(config_dir):
            return path

    # 如果目录也都不存在，返回默认路径（用于创建文件和目录）
    return "./config/sig-vectordB.cfg"

def AddArcVector(aVec, clsid, clsName, fpath, fs_value, bw_value, start_sample=None, end_sample=None):
    '''
    添加一个信号特征向量到数据库中
    新增参数:
        start_sample: 分段起始样本位置
        end_sample: 分段结束样本位置
    '''
    import re

    # 清理类名和文件路径，确保存储的是纯字符串
    def clean_string_for_storage(s):
        if s is None:
            return ""
        s = str(s)
        # 移除字节字符串标记
        s = re.sub(r"^b['\"](.*)['\"]\s*$", r'\1', s)
        # 移除多余的引号
        s = s.strip('\'"')
        # 标准化路径分隔符
        s = s.replace('\\', '/')
        return s

    clsName = clean_string_for_storage(clsName)
    fpath = clean_string_for_storage(fpath)

    fname = get_config_path()
    index = 1
    nDemention = 640
    nDemention = aVec.shape[0]
    
    #创建库文件
    if os.path.exists(fname)==False:
        # 确保config目录存在
        config_dir = os.path.dirname(fname)
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)

        h5f = h5py.File(fname, "w-") #build File object
        v1 = h5f.create_dataset("sigvector", (1,nDemention), maxshape=(None,nDemention), dtype =np.float32, chunks=True)# create dataset
        v2 = h5f.create_dataset("record_id", (1,1), maxshape=(None,1), dtype =np.int32, chunks=True)# 新增：记录ID（自增主键）
        v3 = h5f.create_dataset("classid", (1,1), maxshape=(None,1), dtype =np.int32, chunks=True)# 修改：类别ID
        dtstr = h5py.special_dtype(vlen=str)
        v4 = h5f.create_dataset("classname", (1,1), maxshape=(None,1), dtype =dtstr, chunks=True)# create dataset
        v5 = h5f.create_dataset("filepath", (1,1), maxshape=(None,1), dtype =dtstr, chunks=True)# create dataset
        v6 = h5f.create_dataset("fs", (1,1), maxshape=(None,1), dtype =np.float32, chunks=True)
        v7 = h5f.create_dataset("bw", (1,1), maxshape=(None,1), dtype =np.float32, chunks=True)
        v8 = h5f.create_dataset("start_sample", (1,1), maxshape=(None,1), dtype =np.int64, chunks=True)  # 新增：起始样本位置
        v9 = h5f.create_dataset("end_sample", (1,1), maxshape=(None,1), dtype =np.int64, chunks=True)    # 新增：结束样本位置
        h5f.close()
        index = 0
    
    #添加数据
    h5f = h5py.File(fname, "a")
    curvector  = h5f["sigvector"]
    curclsid  = h5f["classid"]
    curclsname = h5f["classname"]
    curfpath = h5f["filepath"]
    curfs = h5f["fs"]
    curbw = h5f["bw"]

    # 处理record_id字段（向后兼容）
    if "record_id" in h5f:
        currecord = h5f["record_id"]
    else:
        # 为旧数据库添加record_id字段
        currecord = h5f.create_dataset("record_id", (curclsid.shape[0],1), maxshape=(None,1), dtype=np.int32, chunks=True)
        # 为现有记录填充record_id（使用原class_id值）
        for i in range(curclsid.shape[0]):
            currecord[i] = curclsid[i]

    # 处理新字段（向后兼容）
    if "start_sample" in h5f:
        curstart = h5f["start_sample"]
    else:
        # 为旧数据库添加新字段
        curstart = h5f.create_dataset("start_sample", (curclsid.shape[0],1), maxshape=(None,1), dtype=np.int64, chunks=True)
        # 为现有记录填充默认值
        for i in range(curclsid.shape[0]):
            curstart[i] = 0

    if "end_sample" in h5f:
        curend = h5f["end_sample"]
    else:
        # 为旧数据库添加新字段
        curend = h5f.create_dataset("end_sample", (curclsid.shape[0],1), maxshape=(None,1), dtype=np.int64, chunks=True)
        # 为现有记录填充默认值
        for i in range(curclsid.shape[0]):
            curend[i] = 0

    if index!=0:#不为第1条数据
        index = curclsid.shape[0]
        curvector.resize(curvector.shape[0]+1, axis=0)
        currecord.resize(currecord.shape[0]+1, axis=0)
        curclsid.resize(curclsid.shape[0]+1, axis=0)
        curclsname.resize(curclsname.shape[0]+1, axis=0)
        curfpath.resize(curfpath.shape[0]+1, axis=0)
        curfs.resize(curfs.shape[0]+1, axis=0)
        curbw.resize(curbw.shape[0]+1, axis=0)
        curstart.resize(curstart.shape[0]+1, axis=0)
        curend.resize(curend.shape[0]+1, axis=0)

    # 计算下一个record_id
    next_record_id = 1
    if currecord.shape[0] > 0:
        # 获取当前最大的record_id
        max_record_id = np.max(currecord[:])
        next_record_id = int(max_record_id) + 1

    curvector[index]  = aVec
    currecord[index]  = next_record_id  # 新增：自动生成record_id
    curclsid[index]   = clsid           # 修改：现在是真实的类别ID
    curclsname[index]  = clsName
    curfpath[index]    = fpath
    curfs[index] = fs_value
    curbw[index] = bw_value
    curstart[index] = start_sample if start_sample is not None else 0
    curend[index] = end_sample if end_sample is not None else 0
    
    h5f.close()

def ViewArcVectorDB(fname = None):
    '''
    查看数据库文件结构
    '''
    if fname is None:
        fname = get_config_path()
        
    #创建库文件
    if os.path.exists(fname)==False:
        print("{0}:文件不存在".format(fname))
        return
    
    #添加数据
    h5f = h5py.File(fname, "r")
    vectors  = h5f["sigvector"][:]
    clsids  = h5f["classid"][:]
    clsnames = h5f["classname"][:]
    filepaths = h5f["filepath"][:]
    curfs = h5f["fs"][:]
    curbw = h5f["bw"][:]
    h5f.close()

    print('数据库文件：{0} 结构'.format(fname))
    for i in range(vectors.shape[0]):
        print("Rec:{0} clsid:{1} clsname:{2} filepath:{3} fs:{4} bw:{5} vector size:{6} ".format(i, clsids[i,:], clsnames[i,:], filepaths[i,:], curfs[i,:], curbw[i,:], vectors.shape[1]))

def GetArcVectorDB():
    '''
    查看数据库文件结构
    返回9个字段：(vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples)
    '''
    fname = get_config_path()
    #创建库文件
    if os.path.exists(fname)==False:
        print("{0}:文件不存在".format(fname))
        return

    #添加数据
    h5f = h5py.File(fname, "r")
    vectors  = h5f["sigvector"][:]
    clsids  = h5f["classid"][:]
    clsnames = h5f["classname"][:]
    filepaths = h5f["filepath"][:]
    curfs = h5f["fs"][:]
    curbw = h5f["bw"][:]

    # 读取record_id字段（向后兼容）
    if "record_id" in h5f:
        record_ids = h5f["record_id"][:]
    else:
        # 旧数据库：使用原class_id作为record_id
        record_ids = clsids.copy()

    # 读取新字段（向后兼容）
    if "start_sample" in h5f:
        start_samples = h5f["start_sample"][:]
    else:
        start_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)

    if "end_sample" in h5f:
        end_samples = h5f["end_sample"][:]
    else:
        end_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)

    h5f.close()

    return (vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples)


def infer_class_id_from_path(file_path):
    """
    从文件路径推断class_id，基于class_def.txt文件中的类别定义

    Args:
        file_path (str): 文件路径

    Returns:
        int: 推断的class_id
    """
    try:
        import os

        # 清理路径字符串
        if isinstance(file_path, bytes):
            file_path = file_path.decode('utf-8', errors='ignore')
        file_path = str(file_path).strip()

        # 尝试从路径中提取类别名称
        class_name_candidate = None

        # 检查是否包含DataFiles路径结构
        if 'DataFiles' in file_path:
            # 提取DataFiles后的第一级文件夹名
            parts = file_path.split('/')
            datafiles_index = -1
            for i, part in enumerate(parts):
                if 'DataFiles' in part:
                    datafiles_index = i
                    break

            if datafiles_index >= 0 and datafiles_index + 1 < len(parts):
                class_name_candidate = parts[datafiles_index + 1]

        # 如果没有找到DataFiles结构，尝试提取文件所在目录名
        if not class_name_candidate:
            dir_path = os.path.dirname(file_path)
            if dir_path:
                class_name_candidate = os.path.basename(dir_path)

        # 如果还是没有找到，使用文件名（不含扩展名）
        if not class_name_candidate:
            file_name = os.path.basename(file_path)
            class_name_candidate = os.path.splitext(file_name)[0]

        # 如果仍然为空，使用默认值
        if not class_name_candidate:
            class_name_candidate = "unknown"

        # 尝试读取class_def.txt文件
        try:
            # 获取class_def.txt文件路径
            try:
                clsdef_dir, _, _, _, _, _, _ = read_path_config()
                clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
            except Exception as path_error:
                # 如果无法读取路径配置，尝试查找本地class_def.txt文件
                print(f"无法读取路径配置文件: {path_error}")

                # 尝试在常见位置查找class_def.txt
                local_class_def_paths = [
                    "class_def.txt",
                    "chanscan_viewer/class_def.txt",
                    "../chanscan_viewer/class_def.txt",
                    os.path.join(os.path.dirname(__file__), "..", "chanscan_viewer", "class_def.txt"),
                    "Model_faceV2/logs/class_def.txt",
                    "../Model_faceV2/logs/class_def.txt"
                ]

                clsdef_file = None
                for path in local_class_def_paths:
                    if os.path.exists(path):
                        clsdef_file = path
                        print(f"找到本地class_def.txt文件: {path}")
                        break

                if clsdef_file is None:
                    # 如果找不到本地文件，提示用户选择
                    raise Exception(f"无法找到class_def.txt文件。请确保以下位置之一存在该文件：{local_class_def_paths}，或手动选择文件。")

            print(f"使用class_def.txt文件: {clsdef_file}")

            if os.path.exists(clsdef_file):
                cls_ids, cls_names, cls_count = get_classes(clsdef_file)

                # 尝试精确匹配
                for i, cls_name in enumerate(cls_names):
                    if cls_name == class_name_candidate:
                        return int(cls_ids[i])

                # 尝试部分匹配（包含关系）
                for i, cls_name in enumerate(cls_names):
                    if class_name_candidate.lower() in cls_name.lower() or cls_name.lower() in class_name_candidate.lower():
                        print(f"部分匹配: {class_name_candidate} -> {cls_name} (class_id: {cls_ids[i]})")
                        return int(cls_ids[i])

                print(f"未找到匹配的类别: {class_name_candidate}")
                raise Exception(f"在class_def.txt中未找到匹配的类别: {class_name_candidate}。无法推断正确的class_id。")
            else:
                print(f"class_def.txt文件不存在: {clsdef_file}")
                # 文件不存在时，触发备用路径检测
                raise Exception(f"配置的class_def.txt文件不存在: {clsdef_file}")

        except Exception as e:
            print(f"读取class_def.txt失败: {e}")
            # 抛出异常，不使用哈希值
            raise Exception(f"无法读取class_def.txt文件，无法推断正确的class_id。错误: {e}")

    except Exception as e:
        print(f"推断class_id失败: {e}")
        # 不返回默认值，而是抛出异常
        raise Exception(f"推断class_id失败: {e}")


def get_next_record_id():
    """
    获取数据库中下一个可用的record_id（最后一条记录的record_id + 1）

    Returns:
        int: 下一个可用的record_id
    """
    try:
        fname = get_config_path()
        if not os.path.exists(fname):
            # 数据库文件不存在，返回起始ID
            return 1

        # 获取数据库内容
        db_result = GetArcVectorDB()
        if db_result is None:
            # 数据库文件不存在或读取失败
            return 1

        # 解析返回的9个字段
        vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result

        if len(record_ids) == 0:
            # 数据库为空，返回起始ID
            return 1

        # 返回最大record_id + 1
        max_record_id = int(np.max(record_ids))
        return max_record_id + 1

    except Exception as e:
        print(f"获取下一个record_id失败: {e}")
        # 出错时返回起始ID
        return 1


def detect_database_version():
    """
    检测数据库版本，判断是否包含record_id字段

    Returns:
        tuple: (version, has_record_id, error_msg)
            - version (str): 数据库版本 ('v1_legacy', 'v2_with_samples', 'v3_with_record_id')
            - has_record_id (bool): 是否包含record_id字段
            - error_msg (str): 错误信息，成功时为空字符串
    """
    try:
        fname = get_config_path()

        if not os.path.exists(fname):
            return 'v1_legacy', False, "数据库文件不存在"

        # 打开数据库文件检查结构
        with h5py.File(fname, "r") as h5f:
            # 检查是否有record_id字段
            has_record_id = "record_id" in h5f

            # 检查是否有start_sample和end_sample字段
            has_samples = "start_sample" in h5f and "end_sample" in h5f

            # 确定版本
            if has_record_id:
                version = 'v3_with_record_id'
            elif has_samples:
                version = 'v2_with_samples'
            else:
                version = 'v1_legacy'

            return version, has_record_id, ""

    except Exception as e:
        return 'unknown', False, f"检测数据库版本失败: {str(e)}"


def migrate_database_structure(progress_callback=None):
    """
    将旧版本数据库迁移到新结构（包含record_id字段）

    Args:
        progress_callback: 进度回调函数，接收参数(current, total, message)

    Returns:
        tuple: (success, migrated_records, error_msg)
            - success (bool): 是否迁移成功
            - migrated_records (int): 迁移的记录数量
            - error_msg (str): 错误信息，成功时为空字符串
    """
    import shutil
    from datetime import datetime

    try:
        fname = get_config_path()

        if not os.path.exists(fname):
            return False, 0, "数据库文件不存在"

        # 检查数据库版本
        version, has_record_id, version_error = detect_database_version()
        if version_error:
            return False, 0, f"检测数据库版本失败: {version_error}"

        if has_record_id:
            return True, 0, "数据库已经是最新版本，无需迁移"

        if progress_callback:
            progress_callback(0, 0, "开始数据库迁移...")

        # 创建备份
        backup_fname = f"{fname}.migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            shutil.copy2(fname, backup_fname)
            print(f"数据库备份已创建: {backup_fname}")
        except Exception as e:
            return False, 0, f"创建数据库备份失败: {str(e)}"

        if progress_callback:
            progress_callback(1, 4, "备份完成，开始读取数据...")

        # 读取现有数据
        try:
            with h5py.File(fname, "r") as h5f:
                vectors = h5f["sigvector"][:]
                clsids = h5f["classid"][:]
                clsnames = h5f["classname"][:]
                filepaths = h5f["filepath"][:]
                curfs = h5f["fs"][:]
                curbw = h5f["bw"][:]

                # 读取新字段（如果存在）
                if "start_sample" in h5f:
                    start_samples = h5f["start_sample"][:]
                else:
                    start_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)

                if "end_sample" in h5f:
                    end_samples = h5f["end_sample"][:]
                else:
                    end_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)

                total_records = len(clsids)

        except Exception as e:
            return False, 0, f"读取数据库失败: {str(e)}"

        if progress_callback:
            progress_callback(2, 4, f"数据读取完成，共{total_records}条记录，开始重建数据库...")

        # 创建新的数据库结构
        try:
            # 删除原文件
            os.remove(fname)

            # 创建新数据库
            with h5py.File(fname, "w") as h5f:
                # 获取向量维度
                nDimension = vectors.shape[1] if len(vectors) > 0 else 640

                # 创建数据集（支持动态调整大小）
                v1 = h5f.create_dataset("sigvector", (total_records, nDimension), maxshape=(None, nDimension), dtype=np.float32, chunks=True)
                v2 = h5f.create_dataset("record_id", (total_records, 1), maxshape=(None, 1), dtype=np.int32, chunks=True)  # 新增：记录ID
                v3 = h5f.create_dataset("classid", (total_records, 1), maxshape=(None, 1), dtype=np.int32, chunks=True)    # 修改：类别ID

                dtstr = h5py.special_dtype(vlen=str)
                v4 = h5f.create_dataset("classname", (total_records, 1), maxshape=(None, 1), dtype=dtstr, chunks=True)
                v5 = h5f.create_dataset("filepath", (total_records, 1), maxshape=(None, 1), dtype=dtstr, chunks=True)
                v6 = h5f.create_dataset("fs", (total_records, 1), maxshape=(None, 1), dtype=np.float32, chunks=True)
                v7 = h5f.create_dataset("bw", (total_records, 1), maxshape=(None, 1), dtype=np.float32, chunks=True)
                v8 = h5f.create_dataset("start_sample", (total_records, 1), maxshape=(None, 1), dtype=np.int64, chunks=True)
                v9 = h5f.create_dataset("end_sample", (total_records, 1), maxshape=(None, 1), dtype=np.int64, chunks=True)

                # 迁移数据
                if total_records > 0:
                    v1[:] = vectors
                    v2[:] = np.arange(1, total_records + 1).reshape(-1, 1)  # record_id从1开始自增

                    # 根据文件路径推断class_id
                    new_class_ids = np.zeros((total_records, 1), dtype=np.int32)
                    for i in range(total_records):
                        file_path = str(filepaths[i][0]) if hasattr(filepaths[i], '__len__') else str(filepaths[i])
                        try:
                            class_id = infer_class_id_from_path(file_path)
                            new_class_ids[i] = class_id
                        except Exception as e:
                            print(f"警告：无法为文件 {file_path} 推断class_id，使用默认值0。错误: {e}")
                            new_class_ids[i] = 0  # 使用默认值0

                    v3[:] = new_class_ids  # 使用推断的class_id
                    v4[:] = clsnames
                    v5[:] = filepaths
                    v6[:] = curfs
                    v7[:] = curbw
                    v8[:] = start_samples
                    v9[:] = end_samples

        except Exception as e:
            # 恢复备份
            try:
                if os.path.exists(backup_fname):
                    shutil.copy2(backup_fname, fname)
                    print("数据库已从备份恢复")
            except Exception as restore_e:
                print(f"恢复备份失败: {restore_e}")

            return False, 0, f"重建数据库失败: {str(e)}"

        if progress_callback:
            progress_callback(4, 4, "数据库迁移完成")

        print(f"数据库迁移成功，共迁移 {total_records} 条记录")
        print(f"备份文件保存在: {backup_fname}")

        return True, total_records, ""

    except Exception as e:
        return False, 0, f"数据库迁移失败: {str(e)}"


def DeleteArcVectorRecords(record_indices):
    """
    删除数据库中指定索引的记录

    Args:
        record_indices (list): 要删除的记录索引列表（从0开始）

    Returns:
        tuple: (success, deleted_count, error_msg)
            - success (bool): 是否删除成功
            - deleted_count (int): 实际删除的记录数
            - error_msg (str): 错误信息，成功时为空字符串
    """
    import shutil
    from datetime import datetime

    try:
        fname = get_config_path()

        # 检查数据库文件是否存在
        if not os.path.exists(fname):
            return False, 0, "数据库文件不存在"

        # 验证输入参数
        if not record_indices or not isinstance(record_indices, (list, tuple)):
            return False, 0, "无效的记录索引列表"

        # 去重并排序索引
        record_indices = sorted(list(set(record_indices)))

        print(f"准备删除记录索引: {record_indices}")

        # 读取当前数据库内容
        db_result = GetArcVectorDB()
        if db_result is None:
            return False, 0, "无法读取数据库内容"

        # 兼容新旧格式
        if len(db_result) == 9:  # v3版本数据库（包含record_id）
            vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
        elif len(db_result) == 8:  # v2版本数据库（包含样本字段但无record_id）
            vectors, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
            # 生成record_id
            record_ids = np.arange(1, len(vectors) + 1).reshape(-1, 1)
        else:  # v1版本数据库
            vectors, clsids, clsnames, filepaths, curfs, curbw = db_result
            start_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)
            end_samples = np.zeros((clsids.shape[0], 1), dtype=np.int64)
            # 生成record_id
            record_ids = np.arange(1, len(vectors) + 1).reshape(-1, 1)

        if vectors is None or len(vectors) == 0:
            return False, 0, "数据库为空"

        total_records = len(vectors)
        print(f"数据库总记录数: {total_records}")

        # 验证索引范围
        invalid_indices = [idx for idx in record_indices if idx < 0 or idx >= total_records]
        if invalid_indices:
            return False, 0, f"无效的记录索引: {invalid_indices}，有效范围: 0-{total_records-1}"

        # 创建备份文件
        backup_fname = f"{fname}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            shutil.copy2(fname, backup_fname)
            print(f"数据库备份已创建: {backup_fname}")
        except Exception as e:
            return False, 0, f"创建数据库备份失败: {str(e)}"

        # 创建保留记录的掩码（True表示保留，False表示删除）
        keep_mask = [True] * total_records
        for idx in record_indices:
            keep_mask[idx] = False

        # 过滤数据，保留未被删除的记录
        keep_indices = [i for i, keep in enumerate(keep_mask) if keep]

        if len(keep_indices) == 0:
            # 如果所有记录都被删除，删除数据库文件
            try:
                os.remove(fname)
                print("所有记录已删除，数据库文件已移除")
                return True, len(record_indices), ""
            except Exception as e:
                # 恢复备份
                shutil.copy2(backup_fname, fname)
                return False, 0, f"删除数据库文件失败: {str(e)}"

        # 过滤各个数据集
        new_vectors = vectors[keep_indices] if vectors is not None else None
        new_record_ids = record_ids[keep_indices] if record_ids is not None else None
        new_clsids = clsids[keep_indices] if clsids is not None else None
        new_clsnames = clsnames[keep_indices] if clsnames is not None else None
        new_filepaths = filepaths[keep_indices] if filepaths is not None else None
        new_curfs = curfs[keep_indices] if curfs is not None else None
        new_curbw = curbw[keep_indices] if curbw is not None else None
        new_start_samples = start_samples[keep_indices] if start_samples is not None else None
        new_end_samples = end_samples[keep_indices] if end_samples is not None else None

        print(f"过滤后记录数: {len(new_vectors) if new_vectors is not None else 0}")

        # 重建数据库文件
        try:
            # 删除原文件
            os.remove(fname)

            # 创建新的数据库文件
            if new_vectors is not None and len(new_vectors) > 0:
                # 确保config目录存在
                config_dir = os.path.dirname(fname)
                if not os.path.exists(config_dir):
                    os.makedirs(config_dir)

                # 创建新的HDF5文件
                import h5py
                with h5py.File(fname, "w") as h5f:
                    # 获取数据维度
                    nDimension = new_vectors.shape[1] if len(new_vectors.shape) > 1 else len(new_vectors[0])
                    nRecords = len(new_vectors)

                    # 创建数据集 - v3版本格式（包含record_id，支持chunked）
                    v1 = h5f.create_dataset("sigvector", (nRecords, nDimension), maxshape=(None, nDimension), dtype=np.float32, chunks=True)
                    v2 = h5f.create_dataset("record_id", (nRecords, 1), maxshape=(None, 1), dtype=np.int32, chunks=True)  # 新增：记录ID
                    v3 = h5f.create_dataset("classid", (nRecords, 1), maxshape=(None, 1), dtype=np.int32, chunks=True)

                    # 字符串数据集
                    dtstr = h5py.special_dtype(vlen=str)
                    v4 = h5f.create_dataset("classname", (nRecords, 1), maxshape=(None, 1), dtype=dtstr, chunks=True)
                    v5 = h5f.create_dataset("filepath", (nRecords, 1), maxshape=(None, 1), dtype=dtstr, chunks=True)
                    v6 = h5f.create_dataset("fs", (nRecords, 1), maxshape=(None, 1), dtype=np.float32, chunks=True)
                    v7 = h5f.create_dataset("bw", (nRecords, 1), maxshape=(None, 1), dtype=np.float32, chunks=True)
                    v8 = h5f.create_dataset("start_sample", (nRecords, 1), maxshape=(None, 1), dtype=np.int64, chunks=True)
                    v9 = h5f.create_dataset("end_sample", (nRecords, 1), maxshape=(None, 1), dtype=np.int64, chunks=True)

                    # 写入数据
                    v1[:] = new_vectors

                    # 写入record_id
                    if new_record_ids is not None:
                        v2[:] = new_record_ids.reshape(-1, 1) if new_record_ids.ndim == 1 else new_record_ids
                    else:
                        # 重新生成连续的record_id
                        v2[:] = np.arange(1, nRecords + 1).reshape(-1, 1)

                    v3[:] = new_clsids.reshape(-1, 1) if new_clsids.ndim == 1 else new_clsids
                    v4[:] = new_clsnames.reshape(-1, 1) if new_clsnames.ndim == 1 else new_clsnames
                    v5[:] = new_filepaths.reshape(-1, 1) if new_filepaths.ndim == 1 else new_filepaths
                    v6[:] = new_curfs.reshape(-1, 1) if new_curfs.ndim == 1 else new_curfs
                    v7[:] = new_curbw.reshape(-1, 1) if new_curbw.ndim == 1 else new_curbw

                    # 写入样本字段
                    if new_start_samples is not None:
                        v8[:] = new_start_samples.reshape(-1, 1) if new_start_samples.ndim == 1 else new_start_samples
                    else:
                        v8[:] = np.zeros((nRecords, 1), dtype=np.int64)

                    if new_end_samples is not None:
                        v9[:] = new_end_samples.reshape(-1, 1) if new_end_samples.ndim == 1 else new_end_samples
                    else:
                        v9[:] = np.zeros((nRecords, 1), dtype=np.int64)

                print(f"数据库重建完成，新记录数: {nRecords}")
            else:
                print("所有记录已删除，数据库文件已移除")

            # 删除备份文件（可选，保留一段时间以防需要恢复）
            # os.remove(backup_fname)

            deleted_count = len(record_indices)
            print(f"成功删除 {deleted_count} 条记录")

            return True, deleted_count, ""

        except Exception as e:
            # 恢复备份
            try:
                if os.path.exists(backup_fname):
                    shutil.copy2(backup_fname, fname)
                    print("数据库已从备份恢复")
            except Exception as restore_e:
                print(f"恢复备份失败: {restore_e}")

            return False, 0, f"重建数据库失败: {str(e)}"

    except Exception as e:
        import traceback
        error_msg = f"删除记录时发生异常: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return False, 0, error_msg


def Read_sigfile(file_path, clip_pos=[0, -1]):
    '''
    读取信号文件 bvsp/dat 格式
    file_path: 文件名称
    clip_pos : 切片位置, 如果位置为[0, -1], 则取整个长度  
    '''
    if os.path.exists(file_path)==False:
        print("{0}:文件不存在".format(file_path))
        return
    bySignal = np.fromfile(file_path, dtype=np.uint8,count=-1)
    magic_flag = bySignal[0] + bySignal[1]*256
    version = bySignal[2]
    if magic_flag != 20565:
        print("{0}:文件格式不正确,flog={1},version={2}".format(file_path,magic_flag, version))
        return

    header_length =  int.from_bytes(bySignal[4:6],byteorder='little') 
    total_length = np.frombuffer(bySignal[8:16],dtype=np.uint64)[0]
    bandwidth = np.frombuffer(bySignal[52:56],dtype=np.uint32)[0]
    rx_gain = np.frombuffer(bySignal[60],dtype=np.uint8)[0]
    samp_rate = int.from_bytes(bySignal[48:52],byteorder='little')
    center_freq = int.from_bytes(bySignal[56:60],byteorder='little')*1000

    data_vb = bySignal[header_length:]
    data_u16 = np.frombuffer(data_vb[0:],dtype=np.uint16)
    data_u16 = data_u16*pow(2,4)
    data_s16 = data_u16.astype(np.int16) # Remove 4 MSBits from raw data and convert to float
    data_s16 = data_s16/pow(2,4)
    data_s16 = data_s16.astype(np.float32)
    # realpart =  data_s16[0::2]
    # imagpart = data_s16[1::2]
    # vec = [realpart, imagpart]
    sigdata = np.reshape(data_s16,[1,-1,2])
    #sigdata = sigdata[:,range(clip_pos[0]-1,clip_pos[1]),:] #matlab 数组从1开始，为了对应点

    # 修复索引越界问题
    if clip_pos[1] == -1:
        clip_pos[1] = sigdata.shape[1]

    # 确保索引不越界
    clip_pos[0] = max(0, clip_pos[0])
    clip_pos[1] = min(clip_pos[1], sigdata.shape[1])

    # 确保起始位置小于结束位置
    if clip_pos[0] >= clip_pos[1]:
        clip_pos[0] = 0
        clip_pos[1] = sigdata.shape[1]

    print(f"Read_sigfile: 信号形状={sigdata.shape}, 切片范围=[{clip_pos[0]}, {clip_pos[1]})")

    # 使用切片而不是range来避免索引问题
    sigdata = sigdata[:, clip_pos[0]:clip_pos[1], :]
    return (sigdata, np.float32(samp_rate), np.float32(center_freq),  np.float32(bandwidth))
    

def write_sigfile(file_path, sigdata, samp_rate, center_freq, bandwidth, rx_gain=0):
    """
    写入复数信号数据到bvsp/dat格式文件
    
    参数:
    file_path: 输出文件路径
    sigdata: 复数信号数据，形状为 [批次, 时间点] 或 [时间点]
    samp_rate: 采样率
    center_freq: 中心频率(Hz)
    bandwidth: 带宽
    rx_gain: 接收增益
    """
    # 确保数据是正确的形状
    if sigdata.ndim == 1:
        sigdata = np.expand_dims(sigdata, axis=0)  # 添加批次维度
    
    # 确保数据类型正确
    sigdata = sigdata.astype(np.complex64)
    
    # 分离实部和虚部
    iq_data = np.stack((sigdata.real, sigdata.imag), axis=-1)  # [批次, 时间点, I/Q]
    
    # 归一化并转换为uint16
    # 将复数从[-1,1]+j[-1,1]映射到uint16范围[0,65535]
    data_s16 = (iq_data * 32767.0).astype(np.int16)  # 范围: [-32767,32767]
    data_u16 = (data_s16 + 32768).astype(np.uint16)  # 转换为无符号: [0,65535]
    
    # 展平数据
    flat_data = data_u16.flatten()
    
    # 计算文件参数
    data_length = len(flat_data)  # 字节数
    total_length = 112 + data_length  # 总长度，包括头部
    
    # 创建文件头部
    header = bytearray()
    
    # 魔术字和版本
    header.extend([0x35, 0x50, 0x01])  # 魔术字=20565, 版本=1
    
    # 保留字节
    header.extend([0x00] * 1)
    
    # 头部长度 (固定为16)
    header.extend((16).to_bytes(2, byteorder='little'))
    
    # 总长度
    header.extend(total_length.to_bytes(8, byteorder='little'))
    
    # 保留字节
    header.extend([0x00] * 32)
    
    # 采样率
    header.extend(int(samp_rate).to_bytes(4, byteorder='little'))
    
    # 中心频率 (kHz转Hz)
    header.extend((int(center_freq) // 1000).to_bytes(4, byteorder='little'))
    
    # 带宽
    header.extend(np.uint32(bandwidth).tobytes())
    
    # 接收增益
    header.extend(np.uint8(rx_gain).tobytes())
    
    # 保留字节
    header.extend([0x00] * 16)
    
    # 写入文件
    with open(file_path, 'wb') as f:
        # 写入头部
        f.write(header)
        
        # 写入数据
        f.write(flat_data.tobytes())
    
    return os.path.getsize(file_path)


def get_classes(classes_path):
    '''
    获取类定义文件中的类别id及类名称
    '''
    with open(classes_path, encoding='utf-8') as f:
        class_names = f.readlines()
    class_ids = [c.strip().split(':')[0] for c in class_names]    
    class_names = [c.strip().split(':')[1] for c in class_names]
    return class_ids, class_names, len(class_names)

def get_dsdata(vec_ds_def):
    '''
    获取dataset定义文件中的数据行
    '''
    with open(vec_ds_def, encoding='utf-8') as f:
        ds_lines = f.readlines()
    file_paths = [c.strip().split(';')[0] for c in ds_lines]    
    posset = [c.strip().split(';')[1] for c in ds_lines]
    return file_paths, posset, len(file_paths)    
 


#---------------------------------------------------#
#   获得学习率
#---------------------------------------------------#
def get_lr(optimizer):
    for param_group in optimizer.param_groups:
        return param_group['lr']

def get_lr_scheduler(lr_decay_type, lr, min_lr, total_iters, warmup_iters_ratio = 0.05, warmup_lr_ratio = 0.1, no_aug_iter_ratio = 0.05, step_num = 10):
    def yolox_warm_cos_lr(lr, min_lr, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter, iters):
        if iters <= warmup_total_iters:
            # lr = (lr - warmup_lr_start) * iters / float(warmup_total_iters) + warmup_lr_start
            lr = (lr - warmup_lr_start) * pow(iters / float(warmup_total_iters), 2) + warmup_lr_start
        elif iters >= total_iters - no_aug_iter:
            lr = min_lr
        else:
            lr = min_lr + 0.5 * (lr - min_lr) * (
                1.0 + math.cos(math.pi* (iters - warmup_total_iters) / (total_iters - warmup_total_iters - no_aug_iter))
            )
        return lr

    def step_lr(lr, decay_rate, step_size, iters):
        if step_size < 1:
            raise ValueError("step_size must above 1.")
        n       = iters // step_size
        out_lr  = lr * decay_rate ** n
        return out_lr

    if lr_decay_type == "cos":
        warmup_total_iters  = min(max(warmup_iters_ratio * total_iters, 1), 3)
        warmup_lr_start     = max(warmup_lr_ratio * lr, 1e-6)
        no_aug_iter         = min(max(no_aug_iter_ratio * total_iters, 1), 15)
        func = partial(yolox_warm_cos_lr ,lr, min_lr, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter)
    else:
        decay_rate  = (min_lr / lr) ** (1 / (step_num - 1))
        step_size   = total_iters / step_num
        func = partial(step_lr, lr, decay_rate, step_size)

    return func

def set_optimizer_lr(optimizer, lr_scheduler_func, epoch):
    lr = lr_scheduler_func(epoch)
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr

def show_config(**kwargs):
    print('Configurations:')
    print('-' * 70)
    print('|%25s | %40s|' % ('keys', 'values'))
    print('-' * 70)
    for key, value in kwargs.items():
        print('|%25s | %40s|' % (str(key), str(value)))
    print('-' * 70)

def weights_init(net, init_type='normal', init_gain=0.02):
    def init_func(m):
        classname = m.__class__.__name__
        if hasattr(m, 'weight') and classname.find('Conv') != -1:
            if init_type == 'normal':
                torch.nn.init.normal_(m.weight.data, 0.0, init_gain)
            elif init_type == 'xavier':
                torch.nn.init.xavier_normal_(m.weight.data, gain=init_gain)
            elif init_type == 'kaiming':
                torch.nn.init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
            elif init_type == 'orthogonal':
                torch.nn.init.orthogonal_(m.weight.data, gain=init_gain)
            else:
                raise NotImplementedError('initialization method [%s] is not implemented' % init_type)
        elif classname.find('BatchNorm2d') != -1:
            torch.nn.init.normal_(m.weight.data, 1.0, 0.02)
            torch.nn.init.constant_(m.bias.data, 0.0)
    print('initialize network with %s type' % init_type)
    net.apply(init_func)
    
def Init_model(model, pretrained = False, model_path = "", local_rank = 0):

    device      = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    if model_path != "" and pretrained:
        if local_rank == 0:
            print('Load weights {}.'.format(model_path))
        
        #------------------------------------------------------#
        #   根据预训练权重的Key和模型的Key进行加载
        #------------------------------------------------------#
        model_dict      = model.state_dict()
        pretrained_dict = torch.load(model_path, map_location = device)
        load_key, no_load_key, temp_dict = [], [], {}
        for k, v in pretrained_dict.items():
            if k in model_dict.keys() and np.shape(model_dict[k]) == np.shape(v):
                temp_dict[k] = v
                load_key.append(k)
            else:
                no_load_key.append(k)
        model_dict.update(temp_dict)
        model.load_state_dict(model_dict)


        #------------------------------------------------------#
        #   显示没有匹配上的Key
        #------------------------------------------------------#
        if local_rank == 0:
            print("\nSuccessful Load Key:", str(load_key)[:500], "……\nSuccessful Load Key Num:", len(load_key))
            print("\nFail To Load Key:", str(no_load_key)[:500], "……\nFail To Load Key num:", len(no_load_key))
            #print("\n\033[1;33;44m温馨提示，head部分没有载入是正常现象，Backbone部分没有载入是错误的。\033[0m")
    else:
        weights_init(model)
         
    model = model.cuda()
    return model
        
def read_path_config():
    with open(r"pathsetting.json") as json_file:
        config = json.load(json_file)
    clsdef_dir = config["clsdef_dir"]                       #class_def 文件路径
    annotation_path_train = config["annotation_path_train"]  #Train_ds_gen.txt 训练文件路径
    annotation_path_val = config["annotation_path_val"]      #val_ds_gen.txt   验证文件路径
    annotation_path_test = config["annotation_path_test"]     #test_ds_gen.txt  验证文件路径
    server_path = config["server_path"]                     #server生成数据集路径
    linux_path = config["linux_path"]                        #linux本地数据集路径
    windows_path_local = config["windows_path_local"]        #windows本地数据集路径
    
    #dataset_dir = [annotation_path_train, annotation_path_val, windows_path, linux_path, windows_path_local]
    return clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, server_path, linux_path, windows_path_local       


def read_dataset_lines(annotation_path_train, folders):
    '''
        function  :  read_dataset_lines
                     获取数据文件列表
        parameters:
            annotation_path_train -- 标注训练文件
            folders -- 训练信号的文件夹列表
    '''
    # 基本数据集
    with open(annotation_path_train,"r") as f:
        lines_train = f.readlines()
        
    #train-noised等其它文件夹数据集
    len_folders = len(folders); 
    base_folder = folders[0]
    for i in range(1, len_folders):
        sName = folders[i]
        sfile_noised = annotation_path_train.replace(base_folder,sName)
        with open(sfile_noised,"r") as f:
            lines_train_noised = f.readlines()    
        lines_train.extend(lines_train_noised)  

    return lines_train

def windows_to_linux_path(windows_path, linx_path, lines_path):
    nCount = len(lines_path)
    for nRow in range(nCount):
        lines_path[nRow] = lines_path[nRow].replace(windows_path, linx_path).replace("\\","/")

def windows_to_local_path(windows_path, local_path, lines_path):
    nCount = len(lines_path)
    for nRow in range(nCount):
        lines_path[nRow] = lines_path[nRow].replace(windows_path, local_path)

def compute_stft(rx_signals):
    # 1. 计算stft
    # rx_signals格式 [batch, 1, seqlen, I/Q]
    N_fft = 1024 #self.subcarriers
    N_window = N_fft  
    N_overlap = math.floor(N_fft/2) 
    if len(rx_signals.shape)>3:
        y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
    else:
        y = rx_signals

    # 2.组合为复数     
    z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
    #hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
    #使用不同的窗，频域曲线的平滑程度，不同频率分量信息的清晰程度均有区别。
    # Blackman Window 的效果相对较好，频域曲线相对比较平滑并且不同频率分量的信息也比较清晰。
    # 从频域可以看到，主瓣宽度 6 格， 旁瓣最高值 -58 dB
    blackman_window = torch.blackman_window(N_fft, periodic=True, device=z.device)
    # 参考 https://blog.csdn.net/weixin_44618906/article/details/116356081
    #
    
    # 3.计算，不对信号补0
    z = torch.stft(input=z, n_fft=N_fft, window=blackman_window, hop_length=N_overlap, win_length=N_window, center=False, normalized=True) #转换到频域
    #与matlab中centered参数不同，这里参数 center 默认为true，输入会在两侧pad，pad长度为n_fft // 2
    #pad的value不是0，默认pad_mode 为'reflect' ，即为反射，如果输入的信号为：1，2，3，4，5，6， 在两侧pad  n_fft // 2 = 3个，则pad之后的结果为：
    # 4，3，2，【1，2，3，4，5，6】，5，4，3  
    z = torch.fft.fftshift(z, dim=1) #[batch, fft_size, timeframe]

    z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
    return z
    