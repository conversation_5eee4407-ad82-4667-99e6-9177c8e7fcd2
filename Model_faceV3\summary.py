# =======================================================================================================================
#   Function    ：summary.py
#   Description : 网络结构参数打印
#                 
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-1-20
# =======================================================================================================================
import torch
from thop import clever_format, profile
from torchsummary import summary

from nets.arcface import Arcface
from usrlib.usrlib import *
import os
from usrlib.usrlib import compute_stft

if __name__ == "__main__":
    input_shape     = 512*46 
    backbone        = 'DroneSigNet'
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件      
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)  
    
    dummy_input     = torch.randn(1, input_shape, 2).to(device)
    dummy_input     = compute_stft(dummy_input)

    model = Arcface(num_classes=cls_count, backbone=backbone, mode="predict").to(device)
    summary(model, (dummy_input.shape[1], dummy_input.shape[2],dummy_input.shape[3]))
    
    flops, params   = profile(model.to(device), (dummy_input, ), verbose=False)
    #--------------------------------------------------------#
    #   flops * 2是因为profile没有将卷积作为两个operations
    #   有些论文将卷积算乘法、加法两个operations。此时乘2
    #   有些论文只考虑乘法的运算次数，忽略加法。此时不乘2
    #   本代码选择乘2，参考YOLOX。
    #--------------------------------------------------------#
    flops           = flops * 2
    flops, params   = clever_format([flops, params], "%.3f")
    print('Total GFLOPS: %s' % (flops))
    print('Total params: %s' % (params))