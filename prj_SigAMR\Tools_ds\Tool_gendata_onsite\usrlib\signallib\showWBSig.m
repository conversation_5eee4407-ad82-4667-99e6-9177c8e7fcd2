function []=showWBSig(sig,wb_fc,wb_fs,sfiguretitle, pos_ab, nb_fc)
%    function:    showWBSig
%    Description: 显示宽带信号（时域、频域）
%    Params： sig  ,    -- I/Q信号
%             wb_fc,    -- 中心频率
%             wb_fs,    -- 采样率
%

% 2. 显示信息信息，并对时域、频域做分析
% 2.1 时域信号显示

f1 = figure(10);
if exist("sfiguretitle","var")>0
    %figure('numbertitle','on','name',sfiguretitle);
    set(f1, 'name',sfiguretitle);
end


%% 绘图
subplot(411)
N_sig = length(sig);
signal_i = real(sig);               % 信号 I/Q值
signal_q = imag(sig);

plot(signal_i);
hold on
plot(signal_q);
subtitle('时域信号点值图')
xlabel('时域数据点'); ylabel('信号电压值');
if exist("pos_ab","var")>0
    % 标记起始和结束位置
    plot([pos_ab(1) pos_ab(1)], [min(signal_i), max(signal_i)], 'r--', 'LineWidth', 1.5);
    plot([pos_ab(2), pos_ab(2)], [min(signal_i), max(signal_i)], 'r--', 'LineWidth', 1.5);
    text(pos_ab(1),max(signal_i)*0.95,['起始: ', num2str(pos_ab(1)), ' 点'], 'HorizontalAlignment', 'center', 'BackgroundColor', 'white', 'EdgeColor', 'black');
    text(pos_ab(2),max(signal_i)*0.95,['结束: ', num2str(pos_ab(2)), ' 点'], 'HorizontalAlignment', 'center', 'BackgroundColor', 'white', 'EdgeColor', 'black');
end


hold off

subplot(412)
signal_time = (0:N_sig-1)*(1/wb_fs);
signal_time_ms = signal_time*1000;
plot(signal_time_ms, signal_i)
hold on
plot(signal_time_ms, signal_q)
subtitle('时域信号图')
xlabel('时间(ms)'); ylabel('V');
hold off

% 2.2 频域信号显示
sig_len = length(sig);
%fprintf('Duration time = %f ms\r\n', sig_len*1e3/wb_fs);
wb_samps = sig_len;
fftdata = fftshift(abs(fft(sig))/sig_len);                          %频谱变换
nb_freqs=wb_fc - wb_fs/2 +(0 : wb_samps - 1)*wb_fs/wb_samps;
nb_freqs=nb_freqs/1e6;%转换为M

subplot(413)
plot(nb_freqs,fftdata);
hold on
title(['频谱图 [fc= ' num2str(wb_fc/1000000) 'Mhz' '  fs= ' num2str(wb_fs/1000000) 'Mhz]']);
xlabel('频率(MHz)'); ylabel('频谱值');
if exist("nb_fc","var")>0
    % 标记起始和结束位置
    nb_fc_MHz = nb_fc/1e6;
    plot([nb_fc_MHz nb_fc_MHz], [0, max(fftdata)*1.5], 'r--', 'LineWidth', 0.5);
    text(nb_fc_MHz,max(fftdata)*1.5,['nb\_fc: ', num2str(nb_fc_MHz, '%.3f'), ' MHz'], 'HorizontalAlignment', 'center', 'BackgroundColor', 'white', 'EdgeColor', 'black');
    % 绘制标记点
    %plot(nb_fc_MHz, -0.4, '^', 'Color', 'red', 'MarkerSize', 5, 'MarkerFaceColor', 'red');
end
hold off
% 2.3 功率谱显示
if 0
    data = sig;
    ft_len = 2048;       %fft长度
    len = length(data);
    signal = data(1:floor(len/ft_len)*ft_len); %将数据长度变为ft_len的整倍数
    signal = reshape(signal,[ft_len,floor(len/ft_len)]);
    [row,col] = size(signal);
    ffta = abs(fft(signal));
    ffta = fftshift(ffta); %计算矩阵fft
    for n=1:col            %归一化
        max_d = 1;         %max(ffta(:,n));
        ffta(:,n) = ffta(:,n)/max_d;
    end
    wb_samps = ft_len;
    nb_freqs=wb_fc - wb_fs/2 +(0 : wb_samps - 1)/wb_samps*wb_fs;
    nb_freqs=nb_freqs/1e6;%转换为M
    result = sum(ffta,2);   %能量累积
    subplot(413)
    plot(nb_freqs,result);
end

% 2.3 瀑布图显示
window = 4096;
noverlap = window/2;
nfft = window;
a_channel = sig;
[S,F,T,P]=spectrogram(a_channel,window,noverlap,nfft,wb_fs,'yaxis');       %频谱 瀑布图
F = (-window/2:1:window/2-1)*wb_fs/window+wb_fc;%0.015=fs/4096/1e6;
F = F/1e6;%转换为M
subplot(414)
surf(T*1000,F,10*log10(circshift(P,2048)),'edgecolor','none'); axis tight;
view(0,90);
xlabel('Time (ms)'); ylabel('MHz');
title(['时频图 [fc= ' num2str(wb_fc/1000000) 'Mhz' '  fs= ' num2str(wb_fs/1000000) 'Mhz]']);
hold on
if exist("nb_fc","var")>0
    % 标记起始和结束位置
    nb_fc_MHz = nb_fc/1e6;
    % 选择要绘制横线的Y轴位置
    y_line = nb_fc_MHz;  % Y轴上的位置
    X = T*1000;
    Z = 10*log10(circshift(P,2048));
    % 获取曲面的Z轴范围
    z_min = min(min(Z));
    z_max = max(max(Z));

    % 获取X轴范围
    x_min = min(min(X));
    x_max = max(max(X));

    % 在Y轴上绘制横线
    plot3([x_min, x_max], [y_line, y_line], [z_min, z_min], 'b--', 'LineWidth', 0.02);
end
hold off
end
