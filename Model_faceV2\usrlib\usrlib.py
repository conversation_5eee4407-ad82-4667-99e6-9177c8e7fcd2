# =======================================================================================================================
#   Function    ：usrlib.py
#   Description : 用户库函数文件
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-1-20
# =======================================================================================================================
import os
import numpy as np
import torch
import h5py
import math
from functools import partial
import json

def AddArcVector(aVec, clsid, clsName, fpath, fs_value, bw_value):
    '''
    添加一个信号特征向量到数据库中
    '''
    fname = "./config/sig-vectordB.cfg"
    index = 1
    nDemention = 640
    nDemention = aVec.shape[0]
    #创建库文件
    if os.path.exists(fname)==False:
        h5f = h5py.File(fname, "w-") #build File object
        v1 = h5f.create_dataset("sigvector", (1,nDemention), maxshape=(None,nDemention), dtype =np.float32)# create dataset
        v2 = h5f.create_dataset("classid", (1,1), maxshape=(None,1), dtype =np.int32)# create dataset
        dtstr = h5py.special_dtype(vlen=str)
        v3 = h5f.create_dataset("classname", (1,1), maxshape=(None,1), dtype =dtstr)# create dataset    
        v4 = h5f.create_dataset("filepath", (1,1), maxshape=(None,1), dtype =dtstr)# create dataset   
        v5 = h5f.create_dataset("fs", (1,1), maxshape=(None,1), dtype =np.float32)
        v6 = h5f.create_dataset("bw", (1,1), maxshape=(None,1), dtype =np.float32)
        h5f.close()
        index = 0
    
    #添加数据
    h5f = h5py.File(fname, "a")
    curvector  = h5f["sigvector"]
    curclsid  = h5f["classid"]
    curclsname = h5f["classname"]
    curfpath = h5f["filepath"]
    curfs = h5f["fs"]
    curbw = h5f["bw"]

    if index!=0:#不为第1条数据
        index = curclsid.shape[0]
        curvector.resize(curvector.shape[0]+1, axis=0)
        curclsid.resize(curclsid.shape[0]+1, axis=0)
        curclsname.resize(curclsname.shape[0]+1, axis=0)
        curfpath.resize(curfpath.shape[0]+1, axis=0)
        curfs.resize(curfs.shape[0]+1, axis=0)
        curbw.resize(curbw.shape[0]+1, axis=0)

    curvector[index]  = aVec
    curclsid[index]   = clsid
    curclsname[index]  = clsName
    curfpath[index]    = fpath
    curfs[index] = fs_value
    curbw[index] = bw_value
    
    h5f.close()

def ViewArcVectorDB(fname = "./config/sig-vectordB.cfg"):
    '''
    查看数据库文件结构
    '''
    #创建库文件
    if os.path.exists(fname)==False:
        print("{0}:文件不存在".format(fname))
        return
    
    #添加数据
    h5f = h5py.File(fname, "r")
    vectors  = h5f["sigvector"][:]
    clsids  = h5f["classid"][:]
    clsnames = h5f["classname"][:]
    filepaths = h5f["filepath"][:]
    curfs = h5f["fs"][:]
    curbw = h5f["bw"][:]
    h5f.close()

    print('数据库文件：{0} 结构'.format(fname))
    for i in range(vectors.shape[0]):
        print("Rec:{0} clsid:{1} clsname:{2} filepath:{3} fs:{4} bw:{5} vector size:{6} ".format(i, clsids[i,:], clsnames[i,:], filepaths[i,:], curfs[i,:], curbw[i,:], vectors.shape[1]))

def GetArcVectorDB():
    '''
    查看数据库文件结构
    '''
    fname = "./config/sig-vectordB.cfg"
    #创建库文件
    if os.path.exists(fname)==False:
        print("{0}:文件不存在".format(fname))
        return
    
    #添加数据
    h5f = h5py.File(fname, "r")
    vectors  = h5f["sigvector"][:]
    clsids  = h5f["classid"][:]
    clsnames = h5f["classname"][:]
    filepaths = h5f["filepath"][:]
    curfs = h5f["fs"][:]
    curbw = h5f["bw"][:]
    h5f.close()

    return (vectors, clsids, clsnames, filepaths, curfs, curbw)


def Read_sigfile(file_path, clip_pos=[0, -1]):
    '''
    读取信号文件 bvsp/dat 格式
    file_path: 文件名称
    clip_pos : 切片位置, 如果位置为[0, -1], 则取整个长度  
    '''
    if os.path.exists(file_path)==False:
        print("{0}:文件不存在".format(file_path))
        return
    bySignal = np.fromfile(file_path, dtype=np.uint8,count=-1)
    magic_flag = bySignal[0] + bySignal[1]*256
    version = bySignal[2]
    if magic_flag != 20565:
        print("{0}:文件格式不正确,flog={1},version={2}".format(file_path,magic_flag, version))
        return

    header_length =  int.from_bytes(bySignal[4:6],byteorder='little') 
    total_length = np.frombuffer(bySignal[8:16],dtype=np.uint64)[0]
    bandwidth = np.frombuffer(bySignal[52:56],dtype=np.uint32)[0]
    rx_gain = np.frombuffer(bySignal[60],dtype=np.uint8)[0]
    samp_rate = int.from_bytes(bySignal[48:52],byteorder='little')
    center_freq = int.from_bytes(bySignal[56:60],byteorder='little')*1000

    data_vb = bySignal[header_length:]
    data_u16 = np.frombuffer(data_vb[0:],dtype=np.uint16)
    data_u16 = data_u16*pow(2,4)
    data_s16 = data_u16.astype(np.int16) # Remove 4 MSBits from raw data and convert to float
    data_s16 = data_s16/pow(2,4)
    data_s16 = data_s16.astype(np.float32)
    # realpart =  data_s16[0::2]
    # imagpart = data_s16[1::2]
    # vec = [realpart, imagpart]
    sigdata = np.reshape(data_s16,[1,-1,2])
    #sigdata = sigdata[:,range(clip_pos[0]-1,clip_pos[1]),:] #matlab 数组从1开始，为了对应点
    if clip_pos[1]==-1:
        clip_pos[1] = sigdata.shape[1]

    sigdata = sigdata[:,range(clip_pos[0], clip_pos[1]),:]
    return (sigdata, np.float32(samp_rate), np.float32(center_freq),  np.float32(bandwidth))
    

def get_classes(classes_path):
    '''
    获取类定义文件中的类别id及类名称
    '''
    with open(classes_path, encoding='utf-8') as f:
        class_names = f.readlines()
    class_ids = [c.strip().split(':')[0] for c in class_names]    
    class_names = [c.strip().split(':')[1] for c in class_names]
    return class_ids, class_names, len(class_names)

def get_dsdata(vec_ds_def):
    '''
    获取dataset定义文件中的数据行
    '''
    with open(vec_ds_def, encoding='utf-8') as f:
        ds_lines = f.readlines()
    file_paths = [c.strip().split(';')[0] for c in ds_lines]    
    posset = [c.strip().split(';')[1] for c in ds_lines]
    return file_paths, posset, len(file_paths)    
 


#---------------------------------------------------#
#   获得学习率
#---------------------------------------------------#
def get_lr(optimizer):
    for param_group in optimizer.param_groups:
        return param_group['lr']

def get_lr_scheduler(lr_decay_type, lr, min_lr, total_iters, warmup_iters_ratio = 0.05, warmup_lr_ratio = 0.1, no_aug_iter_ratio = 0.05, step_num = 10):
    def yolox_warm_cos_lr(lr, min_lr, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter, iters):
        if iters <= warmup_total_iters:
            # lr = (lr - warmup_lr_start) * iters / float(warmup_total_iters) + warmup_lr_start
            lr = (lr - warmup_lr_start) * pow(iters / float(warmup_total_iters), 2) + warmup_lr_start
        elif iters >= total_iters - no_aug_iter:
            lr = min_lr
        else:
            lr = min_lr + 0.5 * (lr - min_lr) * (
                1.0 + math.cos(math.pi* (iters - warmup_total_iters) / (total_iters - warmup_total_iters - no_aug_iter))
            )
        return lr

    def step_lr(lr, decay_rate, step_size, iters):
        if step_size < 1:
            raise ValueError("step_size must above 1.")
        n       = iters // step_size
        out_lr  = lr * decay_rate ** n
        return out_lr

    if lr_decay_type == "cos":
        warmup_total_iters  = min(max(warmup_iters_ratio * total_iters, 1), 3)
        warmup_lr_start     = max(warmup_lr_ratio * lr, 1e-6)
        no_aug_iter         = min(max(no_aug_iter_ratio * total_iters, 1), 15)
        func = partial(yolox_warm_cos_lr ,lr, min_lr, total_iters, warmup_total_iters, warmup_lr_start, no_aug_iter)
    else:
        decay_rate  = (min_lr / lr) ** (1 / (step_num - 1))
        step_size   = total_iters / step_num
        func = partial(step_lr, lr, decay_rate, step_size)

    return func

def set_optimizer_lr(optimizer, lr_scheduler_func, epoch):
    lr = lr_scheduler_func(epoch)
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr

def show_config(**kwargs):
    print('Configurations:')
    print('-' * 70)
    print('|%25s | %40s|' % ('keys', 'values'))
    print('-' * 70)
    for key, value in kwargs.items():
        print('|%25s | %40s|' % (str(key), str(value)))
    print('-' * 70)

def weights_init(net, init_type='normal', init_gain=0.02):
    def init_func(m):
        classname = m.__class__.__name__
        if hasattr(m, 'weight') and classname.find('Conv') != -1:
            if init_type == 'normal':
                torch.nn.init.normal_(m.weight.data, 0.0, init_gain)
            elif init_type == 'xavier':
                torch.nn.init.xavier_normal_(m.weight.data, gain=init_gain)
            elif init_type == 'kaiming':
                torch.nn.init.kaiming_normal_(m.weight.data, a=0, mode='fan_in')
            elif init_type == 'orthogonal':
                torch.nn.init.orthogonal_(m.weight.data, gain=init_gain)
            else:
                raise NotImplementedError('initialization method [%s] is not implemented' % init_type)
        elif classname.find('BatchNorm2d') != -1:
            torch.nn.init.normal_(m.weight.data, 1.0, 0.02)
            torch.nn.init.constant_(m.bias.data, 0.0)
    print('initialize network with %s type' % init_type)
    net.apply(init_func)
    
def Init_model(model, pretrained = False, model_path = "", local_rank = 0):

    device      = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    if model_path != "" and pretrained:
        if local_rank == 0:
            print('Load weights {}.'.format(model_path))
        
        #------------------------------------------------------#
        #   根据预训练权重的Key和模型的Key进行加载
        #------------------------------------------------------#
        model_dict      = model.state_dict()
        pretrained_dict = torch.load(model_path, map_location = device)
        load_key, no_load_key, temp_dict = [], [], {}
        for k, v in pretrained_dict.items():
            if k in model_dict.keys() and np.shape(model_dict[k]) == np.shape(v):
                temp_dict[k] = v
                load_key.append(k)
            else:
                no_load_key.append(k)
        model_dict.update(temp_dict)
        model.load_state_dict(model_dict)


        #------------------------------------------------------#
        #   显示没有匹配上的Key
        #------------------------------------------------------#
        if local_rank == 0:
            print("\nSuccessful Load Key:", str(load_key)[:500], "……\nSuccessful Load Key Num:", len(load_key))
            print("\nFail To Load Key:", str(no_load_key)[:500], "……\nFail To Load Key num:", len(no_load_key))
            #print("\n\033[1;33;44m温馨提示，head部分没有载入是正常现象，Backbone部分没有载入是错误的。\033[0m")
    else:
        weights_init(model)
         
    model = model.cuda()
    return model
        
def read_path_config():
    with open(r"pathsetting.json") as json_file:
        config = json.load(json_file)
    
    clsdef_dir = config["clsdef_dir"]                       #class_def 文件路径
    annotation_path_train = config["annotation_path_train"]  #Train_ds_gen.txt 训练文件路径
    annotation_path_val = config["annotation_path_val"]      #val_ds_gen.txt   验证文件路径
    annotation_path_test = config["annotation_path_test"]     #test_ds_gen.txt  验证文件路径
    server_path = config["server_path"]                     #server生成数据集路径
    linux_path = config["linux_path"]                        #linux本地数据集路径
    windows_path_local = config["windows_path_local"]        #windows本地数据集路径
    
    #dataset_dir = [annotation_path_train, annotation_path_val, windows_path, linux_path, windows_path_local]
    return clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, server_path, linux_path, windows_path_local       


def read_dataset_lines(annotation_path_train, folders):
    '''
        function  :  read_dataset_lines
                     获取数据文件列表
        parameters:
            annotation_path_train -- 标注训练文件
            folders -- 训练信号的文件夹列表
    '''
    # 基本数据集
    with open(annotation_path_train,"r") as f:
        lines_train = f.readlines()
        
    #train-noised等其它文件夹数据集
    len_folders = len(folders); 
    base_folder = folders[0]
    for i in range(1, len_folders):
        sName = folders[i]
        sfile_noised = annotation_path_train.replace(base_folder,sName)
        with open(sfile_noised,"r") as f:
            lines_train_noised = f.readlines()    
        lines_train.extend(lines_train_noised)  

    return lines_train

def windows_to_linux_path(windows_path, linx_path, lines_path):
    nCount = len(lines_path)
    for nRow in range(nCount):
        lines_path[nRow] = lines_path[nRow].replace(windows_path, linx_path).replace("\\","/")

def windows_to_local_path(windows_path, local_path, lines_path):
    nCount = len(lines_path)
    for nRow in range(nCount):
        lines_path[nRow] = lines_path[nRow].replace(windows_path, local_path)

def compute_stft(rx_signals):
    # 1. 计算stft
    # rx_signals格式 [batch, 1, seqlen, I/Q]
    N_fft = 1024 #self.subcarriers
    N_window = N_fft  
    N_overlap = math.floor(N_fft/2) 
    if len(rx_signals.shape)>3:
        y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
    else:
        y = rx_signals

    # 2.组合为复数     
    z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
    #hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
    #使用不同的窗，频域曲线的平滑程度，不同频率分量信息的清晰程度均有区别。
    # Blackman Window 的效果相对较好，频域曲线相对比较平滑并且不同频率分量的信息也比较清晰。
    # 从频域可以看到，主瓣宽度 6 格， 旁瓣最高值 -58 dB
    blackman_window = torch.blackman_window(N_fft, periodic=True, device=z.device)
    # 参考 https://blog.csdn.net/weixin_44618906/article/details/116356081
    #
    
    # 3.计算，不对信号补0
    z = torch.stft(input=z, n_fft=N_fft, window=blackman_window, hop_length=N_overlap, win_length=N_window, center=False, normalized=True) #转换到频域
    #与matlab中centered参数不同，这里参数 center 默认为true，输入会在两侧pad，pad长度为n_fft // 2
    #pad的value不是0，默认pad_mode 为‘reflect’ ，即为反射，如果输入的信号为：1，2，3，4，5，6， 在两侧pad  n_fft // 2 = 3个，则pad之后的结果为：
    # 4，3，2，【1，2，3，4，5，6】，5，4，3  
    z = torch.fft.fftshift(z, dim=1) #[batch, fft_size, timeframe]

    z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
    return z
    