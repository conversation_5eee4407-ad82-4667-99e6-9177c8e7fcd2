'''
这个文件实现从指定txt文件中读取数据
整合了两个文件中的功能：predictSigSimility.py和AddSigToDbByHdfs.py的功能
实现：
1. 直接读取test.txt中的目录
2. 首先整合得到每一个类别的test数据 然后每一个类别的第一个信号作为信号库 所有信号作为需要进行匹配的随机数据（存储方式参考 Dataset中的方式）
'''
import os
import sys
import time
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from usrlib.usrlib import read_path_config, get_classes, Init_model, windows_to_linux_path, windows_to_local_path
from usrlib.dataloader import DatasetFolder_eval, LoadHdfsDataset 
from usrlib.VectorToVectorSim import VectorSimilarity 
from nets.arcface import Arcface
from usrlib.GUIFeatureMapVisual import plot_match_feature
from utils.dataloader import SigNbDataset
def build_reference_db(txt_path):
    """构建参考信号数据库，选择每个类别的第一个信号
    Returns:
        reference_paths_list: 参考信号路径列表
        clsids: 实际出现的类别ID列表
        class_mapping: 原始类别ID到新索引的映射
        （这里需要记录一下映射 用于后面匹配预测的准确率运算，在计算相似度的时候是没有作用的）
    """
    reference_paths_list = []  # 用于后续筛选特征
    class_data = {}
    clsids = []
    class_mapping = {}  # 用于映射原始类别ID到新的连续索引
    # 首先获取所有出现的类别
    for line in txt_path:
        class_id = line.strip().split(';')[0]
        if class_id not in class_data:
            class_data[class_id] = []
            clsids.append(class_id)
            # 创建类别映射，新索引从0开始
            class_mapping[class_id] = len(clsids) - 1
            
    # 按类别收集数据
    for line in txt_path:
        class_id = line.strip().split(';')[0]
        class_data[class_id].append(line.strip())
        
    # 为每个类别选择参考信号
    for class_id in clsids:  # 使用clsids确保顺序一致
        if class_data[class_id]:
            ref_path = class_data[class_id][0]
            reference_paths_list.append(ref_path)
            
    return reference_paths_list, np.array(clsids), class_mapping

def extract_features(model, reference_paths_list, lines_test, device, class_mapping):
    """使用模型提取所有信号的特征，并确保标签使用新的类别映射"""
    print("Loading signal data...")
    print(f"Creating dataset for {len(lines_test)} signals...")
    
    # 修改数据集创建，传入类别映射
    reference_dataset = SigNbDataset(reference_paths_list)
    test_dataset = SigNbDataset(lines_test)
    
    test_loader = DataLoader(test_dataset, batch_size=len(test_dataset), shuffle=False)
    reference_loader = DataLoader(reference_dataset, batch_size=len(reference_dataset), shuffle=False)
    print("Dataset and DataLoader created.")
    
    model.eval()
    print("Extracting features...")
    
    # 提取测试特征
    for iteration, batch in enumerate(test_loader):
        images, labels = batch
        with torch.no_grad():
            images = images.to(device)
            labels = labels.to(device)
            test_features = model(images, labels)
            
    # 提取参考特征
    for iteration, batch in enumerate(reference_loader):
        images, labels = batch
        with torch.no_grad():
            images = images.to(device)
            labels = labels.to(device)
            reference_features = model(images, labels)

    return reference_features, test_features

if __name__ == '__main__':
    t_start = time.time()
    test_path = '/mnt/disk/lzg_doc/mydataset/2025_04_15/Test_ds_gen19_list.txt'
    model_path = '/mnt/disk/lzg_doc/projects/Model_face_d/logs/model1_epoch_80_lt-0.0000_lv-0.2846.pth'
    modeltype = 1
    similarity_fn = 'cosine_similarity'
    
    # 读取配置和类别定义(这里是读取的Train时的配置)
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('模型支持的无人机类别个数:', cls_count)
    
    # 读取测试数据
    with open(test_path, 'r', encoding='utf-8') as f:
        lines_test = f.readlines()

    if sys.platform.startswith('win'):
        print("当前系统是Windows系统")
    if windows_path!=windows_path_local: # 判断windows路径和本地windows路径是否相同
        windows_to_local_path(windows_path, windows_path_local, lines_test)
    elif sys.platform.startswith('linux'):
        print("当前系统是Linux系统") #路径转换
        windows_to_linux_path(windows_path, linux_path, lines_test)
    else:
        print("当前系统不是Windows也不是Linux系统") 
        
    # 构建参考库和获取实际类别信息
    reference_paths_list, clsids, class_mapping = build_reference_db(lines_test)
    print(f'测试数据中实际包含的类别数: {len(clsids)}')
    print(f'包含的类别ID: {clsids}')

    # 获取测试数据的真实标签（使用新的类别映射）
    clsid_gt = []
    for line in lines_test:
        original_class_id = line.split(';')[0]
        mapped_class_id = class_mapping[original_class_id]
        clsid_gt.append(mapped_class_id)
    clsid_gt = np.array(clsid_gt)   #这里的真实标签应该使用映射后的，方便后面的index与其进行映射

    # 加载模型
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode='predict', ModelType=modeltype)
    Init_model(Model, True, model_path)
    Model.to(device)
    print("Model loaded successfully.")
    
    # 特征提取
    reference_features, query_features = extract_features(Model, reference_paths_list, lines_test, device, class_mapping)
    
    # 相似度计算与匹配
    print(f"Performing similarity matching using '{similarity_fn}'...")
    VectorSim = VectorSimilarity(similarity_fn=similarity_fn, single=True)
    predicts = VectorSim.matching(query_features, reference_features)
    
    # 评估结果
    for i in range(3):
        Indexs = predicts['pred_label'][:,i]
        probs = predicts['pred_score'][:,i]
        probs = probs.cpu().numpy()
        Indexs = Indexs.cpu().numpy()
        print(f"Predict Top {i} Labels:\n{Indexs.reshape(1,-1)} \nProbs:\n{probs}")
        if i == 0:
            val_predict = Indexs
    
    # 计算准确率（使用映射后的类别ID）
    val_score = np.where(val_predict == clsid_gt, 1.0, 0.0)
    val_acc = np.mean(val_score)
    print('predict_result: acc= {:.2f}%'.format(val_acc*100))
    
    # 可视化
    plot_match_feature(reference_features, query_features, predicts, np.arange(len(clsids)), clsid_gt)
    
    t_end = time.time()
    print(f"\nTotal execution time: {t_end - t_start:.2f} seconds")
