function showWavById(signalDB_dir, lblfile, startID)
%
% function: showWavById
%           显示文件路径
%
% params: signalDB_dir -- 信号文件路径
%         lblfile      -- 标注文件路径
%         startID      -- 起始ID
%
%signalDB_dir = 'E:\ftproot\signalDB\';%信号路径
%lblfile = [signalDB_dir,'Train_ds.txt']; % 训练数据文件
if exist(lblfile,"file")>0
    Table_train = readtable(lblfile,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    %Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", lblfile);
end
iRow = startID;
sigfsubname          = char(Table_train(iRow,1).filename);%文件名称
%clsname          = split(sigfsubname,'\',1);       %文件类别名名称

sigfname         = [signalDB_dir, sigfsubname];    %文件路径
sigfname         = [signalDB_dir, sigfsubname];    %文件路径
nStartpos_t      = Table_train(iRow,2).startpos;      %起始点
nEndpos_t        = Table_train(iRow,3).endpos;      %结束点
fc               = Table_train(iRow,4).fc;      %中心频率
lenPoints        = Table_train(iRow,5).lenPoints;%长度
fprintf('记录号:%d,文件:%s,数据点:[%d,%d],中心频率=%.3f MHz\n',iRow, sigfsubname,nStartpos_t,nEndpos_t,fc/1e6);
[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
showWBSig(wb_signal,wb_fc,wb_fs);


end