function []=TrainRecords2CSV(fname_recs)
%  Function    ：TrainRecords2CSV
%  Description : 生成cvs分析文件
%  Parameter   : fname_recs    -- 源Train_records.txt路径
%
%  Author      : Liuzhiguo
%  Date        : 2025-6-20
sep = filesep; %系统分割符
fs = 61.44e6;
if exist(fname_recs,"file")>0
    fid = fopen(fname_recs, 'r');
    line1 = fgetl(fid);
    fclose(fid);

    % 计算分号数量判断格式
    semicolon_count1 = sum(line1 == ';');

    if semicolon_count1 == 2
        Table_train = readtable(fname_recs,'Format','%s [%d,%d] %f','Delimiter',';');
        Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];

    else
        Table_train = readtable(fname_recs, 'Format', '%s [%d,%d] %f %f', 'Delimiter', ';');
        Table_train.Properties.VariableNames = ["filename", "startpos", "endpos", "fc",'snr'];

    end
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    % Table_train.lenClipPoints = Table_train.lenPoints;
    %
    % msp = split(string(Table_train.filename),sep,1);
    % Table_train.clsname = msp(1,:)';

    Table_train.Duration_ms = (double(Table_train.lenPoints)*1e3/fs);%暂时赋值
    Table_train = sortrows(Table_train,'lenPoints');

    [filepath,name,ext] = fileparts(fname_recs);
    file_csv = fullfile(filepath,strcat(name,'_Analy.csv'));
    writetable(Table_train, file_csv);
    fprintf("生成文件:%s\n",file_csv);

    file_txt_sorted = fullfile(filepath,strcat(name,'_sorted.txt'));
    nRows = height(Table_train);
    fout = fopen(file_txt_sorted,"w");%覆盖方式写入
    for i=1:nRows
        slabel_clip = string(Table_train.filename(i));
        clip_start_pos = Table_train.startpos(i);
        clip_end_pos = Table_train.endpos(i);
        fc_nb = Table_train.fc(i);
        snr_db = Table_train.snr(i);
        slable = sprintf("%s;[%d,%d];%.3fe6;%.2f\n",slabel_clip,clip_start_pos, clip_end_pos,fc_nb/1e6,snr_db);
        fwrite(fout,slable);
    end
    fclose(fout);
    fprintf("生成文件:%s\n",file_txt_sorted);
else
    error("文件：%s 不存在！", fname_recs);
end

end