function [xx, fftdata] = showfreqD(sig,fs)
%  Function    ：showfreqD
%  Description : 利用fft函数计算频率，并显示图表
%  Parameter   : sig -- 复信号（如为实信号，则需要采用fftshift移动频率）
%                fs  -- 采样率
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-18

sig_len = length(sig);
fc = 0;
fprintf('Duration time = %f 秒\r\n', sig_len/fs);


wb_samps = sig_len;
fftdata = fftshift(abs(fft(sig))/sig_len);
%fftdata = abs(fft(sig))/sig_len;
xx=fc/1e6 - fs/1e6/2 +(0 : wb_samps - 1)/wb_samps*fs/1e6;
figure(333)
plot(xx,fftdata);
title(['fc= ' num2str(fc/1000000) 'Mhz' '  fs= ' num2str(fs/1000000) 'Mhz']);
end

