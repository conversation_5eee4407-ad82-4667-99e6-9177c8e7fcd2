# =======================================================================================================================
#   Function    ：ModelTrain.py
#   Description : Model训练
#                 读入hdf5文件，并对其进行分类识别、arc特征向量提取
# 
#   Parameter   : ../data/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2024-09-06
# =======================================================================================================================
import os
import numpy as np

from net.modelDesign import *
from usrlib.dataloader import *
from usrlib.usrlib import *
from usrlib.callbacks import *
from torch.utils.tensorboard import SummaryWriter

import torch.distributed as dist

import sys
print(sys.argv)
import getopt



# os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
# os.environ['TORCH_USE_CUDA_DSA'] = '1'
# =========================================================
# Parameters Setting
# ========================================================
EPOCHS = 1500*3#10000
bestLoss = 0.005
stopLoss = 0.0001
batch_size = 32

dataset_file = "" # 数据文件路径
modeltype =  0 #模型类别 0:分类模型 1:指纹特征模型
folders = [ 'E:\\project\\tool_dataset\\outdataset\\train\\base',
                'E:\\project\\tool_dataset\\outdataset\\train\\noised'  ]  
#folders = []

clsdef_dir = 'E:\\ftproot\\signalDB\\' #信号路径
clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
cls_ids, cls_names, cls_count = get_classes(clsdef_file)
print('无人机类别个数:',cls_count)

# 命令行方式获取训练文件 dataset_file
opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['help','datafilepath='])
for opt_name,opt_value in opts:
    if opt_name in ('-m','--modetype'):
        modeltype = int(opt_value)
    if opt_name in ('-p','--datafilepath'):
        dataset_file = opt_value
        print("The datafilepath is ", dataset_file)
        # do something

print("The model type is ", modeltype)
#exit() 调试用
if modeltype == 0:
    fname_model = "DroneSigCls.pth"
else:    
    fname_model = "DroneSigArc.pth"

if dataset_file=="":
    datasettype = 'train'
    subdir = 'base' # base,noised,multipath
    train_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)

    dataset_nb = "nb-dataset-S1.hdf5" #数据本身文件  
    dataset_file = os.path.join(train_dataset_dir, dataset_nb)

(curpath, curfilename) = os.path.split(dataset_file)

model_dir = './PretrainedModel/'
model_saved = os.path.join(model_dir, fname_model)
b_loadmodel = False
if os.path.exists(model_saved):
    b_loadmodel = True
# =========================================================    
# Data Loading
# ========================================================
print('===load data: {0} ==============='.format(dataset_file))

if len(folders) >0:
    rx_signal,class_id,class_name = LoadMultiHdfsDataset(folders)
else:
    rx_signal,class_id,class_name = LoadHdfsDataset(dataset_file)

# if len(folders) >0:
#     rx_signal_train,class_id_train,class_name_train = LoadSpecialMultiHdfsDataset(folders,'dataset-train')
#     rx_signal_val,class_id_val,class_name_val = LoadSpecialMultiHdfsDataset(folders,'dataset-val')
# else:
#     rx_signal,class_id,class_name = LoadHdfsDataset(dataset_file)


if batch_size > class_id.size: #如果数据量不足，重新确定适合的batchsize
        batch_size = class_id.size  

tb_writer = SummaryWriter(log_dir='logs') # tensorboard
# train_ratio = 0.99
# rx_signal_train = rx_signal[:int(rx_signal.shape[0] * train_ratio)]
# rx_signal_val = rx_signal[int(rx_signal.shape[0] * train_ratio):]
# class_id_train = class_id[:int(class_id.shape[0] * train_ratio)]
# class_id_val = class_id[int(class_id.shape[0] * train_ratio):]
# class_name_train = class_id[:int(class_name.shape[0] * train_ratio)]
# class_name_val = class_id[int(class_name.shape[0] * train_ratio):]
#

# 设置主进程的 IP 地址和端口号
os.environ['MASTER_ADDR'] = '127.0.0.1'
os.environ['MASTER_PORT'] = '29501'
torch.distributed.init_process_group('gloo',init_method="env://?use_libuv=False", rank=0, world_size=1)
# torch.distributed.is_available()
# torch.distributed.is_mpi_available()
# torch.distributed.is_nccl_available()
# torch.distributed.is_gloo_available()
# =========================================================
# Model Constructing
# ========================================================
Model = DroneSig_classifier(nc=cls_count, ModelType=modeltype)
Init_model(Model, b_loadmodel, model_saved)

criterion = nn.CrossEntropyLoss().cuda()
optimizer = torch.optim.Adam(Model.parameters(), lr=1e-4)
scheduler = torch.optim.lr_scheduler.MultiStepLR(optimizer, milestones=[5000,7000,9000], gamma=0.5)
# =========================================================
# Model Training and Saving
# =========================================================
for epoch in range(EPOCHS):
    (loss, acc, val_loss, val_acc) = Train_one_epoch(Model, optimizer, scheduler, criterion, batch_size, rx_signal, class_id, rx_signal, class_id)

    print('Epoch: [{0}/{1}]\t' 'F:{2}\t' 'Loss {loss:.5f}\t' 'Acc {acc:.5f}\t' 'val_loss {val_loss:.5f}\t' 'val_acc {val_acc:.5f}\t'.format(
                epoch, EPOCHS, curfilename, loss=loss.item(), acc=acc, val_loss=val_loss, val_acc=val_acc))
    
    # tb_writer.add_scalar('loss', loss, epoch)
    # tb_writer.add_scalar('val_loss', val_loss, epoch)
    tb_writer.add_scalars('multiloss', {'loss':loss, 'val_loss':val_loss}, epoch) #
    tb_writer.add_scalars('multiAcc', {'acc':acc, 'val_acc':val_acc}, epoch) #
    # tb_writer.flush()

    if val_loss < bestLoss:
        # Model saving
        bestLoss = val_loss
        if epoch > 0.01*EPOCHS:
            torch.save(Model.state_dict(), model_saved, _use_new_zipfile_serialization=False)
            print("Model: {0} saved, bestloss is {1:0.5f} ".format(model_saved, bestLoss))
    elif epoch+1==EPOCHS:
            torch.save(Model.state_dict(), model_saved)
            print("Model: {0} saved, bestloss is {1:0.5f} As Last Epoach".format(model_saved, bestLoss))        

    if val_loss < stopLoss:
        print("Loss = {0:0.5f} < StopLoss = {1:0.5f} Exit".format(val_loss, stopLoss))
        break  

#tb_writer.add_graph(Model, rgb)
tb_writer.flush()
tb_writer.close()
print('Training for {0} is finished! bestloss is {1:0.5f} '.format(model_saved, bestLoss))



#
# 设置路径，输入tensorboard --logdir=./logs
# http://localhost:6006/
#
