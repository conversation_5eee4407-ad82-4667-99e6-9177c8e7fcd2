import os
import numpy as np
import torch
import torch.utils.data as data
import h5py
import soundfile
from torch.utils.data import Dataset

#stft滑动窗口来看, 最后一个窗口的结束位置不能超过总数据点数 T，N为fft点数，第1个窗口末尾，m为窗口个数,k为overlap窗口大小， 即：N + (m-1)k <= T, 求得 m = (T-N)/k + 1
#gLenNorm = 187*128 # 4M采用率,6ms,数据点数:187*128 =23936 < 24000 (4M*6ms), 
gLenNorm = 93*256 # 4M采用率,6ms,数据点数:93*256 =23808 < 24000 (4M*6ms), 

def getLenNorm():
    '''
    获取归一化单条数据长度
    '''
    return gLenNorm

def LoadHdfsDataset(dataset_file):
    '''
    加载单个hdfs数据集
    '''
    LenNorm     = getLenNorm()

    if os.path.exists(dataset_file)==False:
        print("{0}:文件不存在".format(dataset_file))
        return
    
    f = h5py.File(dataset_file, 'r')
    rx_signals  = f['rx_signal'][:]
    class_ids   = f['class_id'][:]
    class_names = f['class_name'][:]
    fs_values = f['fs'][:]
    bw_values = f['bw'][:]
    f.close()

    # 裁剪数据集的size
    N_row =  rx_signals.shape[0]
    N_col =  rx_signals.shape[1]
    if N_col<LenNorm:
        rx_signals = np.pad(rx_signals,((0,0),(0,LenNorm-N_col),(0,0)), 'constant')
    else:
        rx_signals = np.resize(rx_signals,(N_row, LenNorm, 2))
    
    # print('rx_signal:', rx_signals.shape, rx_signals.dtype)
    # print('class_id:', class_ids.shape, class_ids.dtype)
    return  rx_signals, class_ids, class_names, fs_values, bw_values


class SigNbDataset(data.Dataset):
    def __init__(self, lines):
        self.lines       = lines
        self.LenNorm     = getLenNorm()

    def __len__(self):
        return len(self.lines)

    def __getitem__(self, index):
        annotation_path = self.lines[index].split(';')[1].split()[0]
        label               = int(self.lines[index].split(';')[0])
        
        #rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(annotation_path)
        rx_signals, fs_value = soundfile.read(annotation_path, dtype='float32')#(60000, 2)
        N_row =  rx_signals.shape[0]
        if N_row<self.LenNorm:
            rx_signals = np.pad(rx_signals,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signals = np.resize(rx_signals,(self.LenNorm, 2))

        #rx_signal = rx_signal[0:self.LenNorm,:]
        if rx_signals.shape[0]!=self.LenNorm or rx_signals.shape[1]!=2 or len(rx_signals.shape)!=2: #(1, 59392, 2)
             print(fs_value)

        # if label != class_id:
        #     print(fs_value)    
        return rx_signals, label

def SigNbDataset_collate(batch):
    rx_signals  = []
    targets = []
    for rx_signal, label in batch:
        rx_signals.append(rx_signal)
        targets.append(label)

    rx_signals  = torch.tensor(np.array(rx_signals))#torch.from_numpy(np.array(rx_signals))#.type(torch.FloatTensor)
    targets = torch.tensor(np.array(targets)).long()#torch.from_numpy(np.array(targets)).long()

    #targets = torch.cat(targets, 0)
    return rx_signals, targets
    
def dataset_collate(batch):
    images  = []
    targets = []
    for image, y in batch:
        images.append(image)
        targets.append(y)
    images  = torch.from_numpy(np.array(images)).type(torch.FloatTensor)
    targets = torch.from_numpy(np.array(targets)).long()
    return images, targets

#labeled signal wild
class LSWDataset(data.Dataset):
    def __init__(self, lines_lsw):
        self.lines_lsw = lines_lsw 
        self.LenNorm     = getLenNorm()

    def __getitem__(self, index):
        line    = self.lines_lsw[index].replace("\n", "")#移除换行符
        (id1, path_1, id2, path_2) = line.split(';')
        issame = (id1==id2)
        #rx_signal1, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_1)
        #rx_signal2, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_2)
        rx_signal1, fs_value = soundfile.read(path_1, dtype='float32')
        rx_signal2, fs_value = soundfile.read(path_2, dtype='float32')
        #LenNorm = 1024*58+512
        N_row =  rx_signal1.shape[0]
        if N_row<self.LenNorm:
            rx_signal1 = np.pad(rx_signal1,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signal1 = np.resize(rx_signal1,(self.LenNorm, 2))
        
        N_row =  rx_signal2.shape[0]
        if N_row<self.LenNorm:
            rx_signal2 = np.pad(rx_signal2,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signal2 = np.resize(rx_signal2,(self.LenNorm, 2))

        # rx_signal1 = rx_signal1[0:self.LenNorm,:]
        # rx_signal2 = rx_signal2[0:self.LenNorm,:]

        return rx_signal1, rx_signal2, issame

    def __len__(self):
        return len(self.lines_lsw)
    
class MDataHandler(Dataset):
    def __init__(self, rx_signal,class_id,class_name,fs_value, bw_value):
        self.rx_signal = rx_signal
        self.class_id = class_id
        self.class_name = class_name
        self.fs_value = fs_value        
        self.bw_value = bw_value

    def __getitem__(self, index):
        return torch.tensor(self.rx_signal[index]), torch.tensor(self.fs_value[index]), torch.tensor(self.bw_value[index]),torch.tensor(self.class_id[index])

    def __len__(self):
        return len(self.class_id)

class Epoch_params():
    def __init__(self, epoch, total_epoch, num_train,num_val,batch_size):
        self.epoch = epoch
        self.total_epoch = total_epoch
        self.batch_size = batch_size
        self.epoch_step = num_train // batch_size
        self.epoch_step_val = num_val // batch_size


# Data Loader Class Defining
class DatasetFolder_eval(Dataset):
    def __init__(self, y, fs_value, bw_value):
        self.y = y.astype(np.float32)
        self.fs_value = fs_value.astype(np.float32)
        self.bw_value = bw_value.astype(np.float32)

    def __getitem__(self, index):
        return torch.tensor(self.y[index]), torch.tensor(self.fs_value[index]), torch.tensor(self.bw_value[index])

    def __len__(self):
        return len(self.y)
        
# 无人机信号数据集        
class DatasetDroneSig(Dataset):
    def __init__(self, y):
        self.y = y.astype(np.float32)
        self.datalen = 160000
        
    def __getitem__(self, index):        
        return torch.tensor(self.y[index])

    def __len__(self):
        return len(self.y)

def LoadHdfsDataset(dataset_file):
    '''
    加载单个hdfs数据集
    '''
    LenNorm = getLenNorm()
    if os.path.exists(dataset_file)==False:
        print("{0}:文件不存在".format(dataset_file))
        return

    f = h5py.File(dataset_file, 'r')
    rx_signals  = f['rx_signal'][:]
    class_ids   = f['class_id'][:]
    class_names = f['class_name'][:]
    fs_values = f['fs'][:]
    bw_values = f['bw'][:]
    f.close()

    # 裁剪数据集的size
    N_row =  rx_signals.shape[0]
    N_col =  rx_signals.shape[1]
    if N_col<LenNorm:
        rx_signals = np.pad(rx_signals,((0,0),(0,LenNorm-N_col),(0,0)), 'constant')
    else:
        rx_signals = np.resize(rx_signals,(N_row, LenNorm, 2))
    
    

    print('rx_signal:', rx_signals.shape, rx_signals.dtype)
    print('class_id:', class_ids.shape, class_ids.dtype)
    return  rx_signals, class_ids, class_names, fs_values, bw_values

def LoadMultiHdfsDataset(folders:list[str]):
    '''
    加载多个数据集
    '''
    dataset_files = []
    ext_name = 'hdf5'
    for folder in folders:
        for file in os.listdir( folder ):
            if file.endswith( ext_name ):# os.path.splitext(name)[1] == suffix:
                dataset_file = os.path.join(folder, file)
                dataset_files.append(dataset_file) #数据集
    
    rx_signals = []
    class_ids = []
    class_names = []
    fs_values = []
    bw_values = []
    for dataset_file in dataset_files:
        rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file) # rx_signal: nRows, length, I/Q
        rx_signals.append(rx_signal)
        class_ids.append(class_id)
        class_names.append(class_name)
        fs_values.append(fs_value)
        bw_values.append(bw_value)        

    rx_signals = np.concatenate(rx_signals,axis=0)
    class_ids = np.concatenate(class_ids,axis=0)
    class_names = np.concatenate(class_names,axis=0)
    fs_values = np.concatenate(fs_values,axis=0)
    bw_values = np.concatenate(bw_values,axis=0)

    return rx_signals, class_ids, class_names, fs_values, bw_values

def LoadSpecialMultiHdfsDataset(folders:list[str], sProp):
    '''
    加载多个数据集
    '''
    dataset_files = []
    ext_name = 'hdf5'
    for folder in folders:
        for file in os.listdir( folder ):
            if file.endswith( ext_name ) and sProp in file:# os.path.splitext(name)[1] == suffix:
                dataset_file = os.path.join(folder, file)
                dataset_files.append(dataset_file) #数据集
    
    rx_signals = []
    class_ids = []
    class_names = []
    fs_values = []
    bw_values = []
    for dataset_file in dataset_files:
        rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file) # rx_signal: nRows, length, I/Q
        rx_signals.append(rx_signal)
        class_ids.append(class_id)
        class_names.append(class_name)
        fs_values.append(fs_value)
        bw_values.append(bw_value)     

    rx_signals = np.concatenate(rx_signals,axis=0)
    class_ids = np.concatenate(class_ids,axis=0)
    class_names = np.concatenate(class_names,axis=0)
    fs_values = np.concatenate(fs_values,axis=0)
    bw_values = np.concatenate(bw_values,axis=0)

    return rx_signals, class_ids, class_names, fs_values, bw_values