function [corrvar_array] = getselfcorrvals(signal_seq,corrseq_len)
%  Function    ：getselfcorrvals
%  Description : 计算自相关值
%  Parameter   : signal_seq   -- 输入数据   (I/Q序列)
%                corrseq_len  -- 相关长度   (要计算的序列长度)
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-28
%
%相关峰曲线判别法
%采用整体序列自相关，会出现两个相关峰，利用第2个相关峰 确定FFT长度
N_all_len = length(signal_seq);
corrvar_array = zeros(corrseq_len,1);
for i=1:corrseq_len                       % i表示当前位置索引
    N_seqlen = (N_all_len-i)+1;              % 计算序列长度
    seqM = signal_seq(1:N_seqlen);           % 开始位置序列,1 可能有问题，未必是开始位置
    seqWindow = signal_seq(i:end);        % 滑动序列
    corval = seqWindow'*seqM;             % 计算相关值
    corrvar_array(i) = abs(real(corval)); % 取实部
end

end

