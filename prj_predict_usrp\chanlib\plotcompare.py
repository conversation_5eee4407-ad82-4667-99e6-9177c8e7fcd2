"""
对比分析文件：同时计算传统方法和边缘检测方法的标签，并绘制对比图像
---calRegionByCounter.py（边缘检测方法）
---calregions.py（传统方法）
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import torch
import time
from chanlib.func_scansig import proc_wbsig
from chanlib.calRegionByCounter import EdgeDetection, plot_contour_analysis_with_real_coords
from chanlib.usrp_samp import multi_usrp_rx, init_args
import uhd
import cv2


class LabelComparisonAnalyzer:
    def __init__(self, fc=2475e6, fs=15.36e6, bw=12e6, signal_duration=0.01):
        self.fc = fc
        self.fs = fs
        self.bw = bw
        self.signal_duration = signal_duration

        # 初始化边缘检测器
        self.edge_detector = EdgeDetection(
            method='Prewitt',
            n_fft=4096,
            fs=fs,
            signal_duration=signal_duration
        )

        # 初始化USRP参数
        self.args = init_args(fc=fc, fs=fs, bw=bw)

    def get_traditional_labels(self, samples):
        """使用传统方法获取标签"""
        try:

            label = proc_wbsig(samples, self.fs, self.fc, self.bw)

        except Exception as e:
            print(f"传统方法处理错误: {e}")
            return []

        return label

    def get_edge_detection_labels(self, samples):
        """使用边缘检测方法获取标签"""
        try:
            # 将信号转换为张量格式
            signals_tensor = torch.from_numpy(
                np.stack([np.real(samples), np.imag(samples)], axis=-1)
            ).unsqueeze(0)

            # 生成时频图
            spectrum = self.edge_detector.signaltoSpectrum(signals_tensor)

            # 处理时频图
            original_spectrum, processed_spectrum = self.edge_detector.processSpectrum(spectrum[0])

            # 边缘检测
            edge_map, gradient_magnitude = self.edge_detector.detect_edges(processed_spectrum)

            # 查找轮廓
            result_image, contours = self.edge_detector.findCounters(edge_map, self.fc)

            # 转换为标签格式
            edge_labels = []
            for contour in contours:
                label = self.create_label_from_contour(contour, samples)
                if self.validate_label(label, samples):
                    edge_labels.append(label)

            return edge_labels, original_spectrum, result_image, contours

        except Exception as e:
            print(f"边缘检测方法处理错误: {e}")
            return [], None, None, []

    def create_label_from_contour(self, contour, samples):
        """从轮廓信息创建标签"""

        class Label:
            def __init__(self):
                self.left = 0
                self.right = 0
                self.up = 0
                self.down = 0
                self.valid = 1
                self.centor_freq = 0
                self.start_idx = 0
                self.stop_idx = 0
                self.bw = 0
                self.method = "edge_detection"

        label = Label()

        # 时间信息转换
        label.start_idx = int(contour['real_time_start_ms'] * 1e-3 * self.fs)
        label.stop_idx = int(contour['real_time_end_ms'] * 1e-3 * self.fs)

        # 频率信息
        label.centor_freq = contour['real_center_freq_mhz'] * 1e6
        label.bw = contour['real_freq_bandwidth_mhz'] * 1e6
        # 位置信息
        x, y, w, h = contour['pixel_bounding_box']
        label.left = x
        label.right = x + w
        label.up = y
        label.down = y + h

        return label

    def validate_label(self, label, samples):
        """验证标签有效性"""
        return (label.bw > 0 and
                label.stop_idx > label.start_idx and
                label.start_idx >= 0 and
                label.stop_idx <= len(samples) and
                label.bw <= self.bw)

    def plot_comparison(self, traditional_labels, edge_labels, original_spectrum, contours):
        """
        绘制1x2的对比分析图
        左图：原始时频图
        右图：两种方法的检测结果叠加显示
        """
        try:
            if isinstance(original_spectrum, torch.Tensor):
                spectrum_np = original_spectrum.cpu().numpy()
            else:
                spectrum_np = original_spectrum

            freq_bins, time_frames = spectrum_np.shape
            time_axis = np.linspace(0, self.signal_duration * 1000, time_frames)  # 转换为ms
            
            # 计算频率轴
            freq_center_bin = freq_bins // 2
            freq_resolution = self.fs / freq_bins
            freq_offset = np.arange(freq_bins) - freq_center_bin
            freq_axis = (self.fc + freq_offset * freq_resolution) / 1e6  # 转换为MHz

            # 创建1x2的子图
            fig, axes = plt.subplots(1, 2, figsize=(15, 6))
            fig.suptitle(f'信号检测结果对比 (中心频率: {self.fc/1e6:.2f} MHz)', fontsize=16)

            # 1. 左图：原始时频图
            im1 = axes[0].imshow(spectrum_np, aspect='auto', origin='lower', cmap='jet',
                                extent=[time_axis[0], time_axis[-1], freq_axis[0], freq_axis[-1]])
            axes[0].set_title('原始时频图')
            axes[0].set_xlabel('时间 (ms)')
            axes[0].set_ylabel('频率 (MHz)')
            fig.colorbar(im1, ax=axes[0], label='幅度')

            # 2. 右图：检测结果叠加显示
            im2 = axes[1].imshow(spectrum_np, aspect='auto', origin='lower', cmap='jet',
                                extent=[time_axis[0], time_axis[-1], freq_axis[0], freq_axis[-1]])
            
            # 添加传统方法的检测框（红色）
            for label in traditional_labels:
                start_time = label.start_idx / self.fs * 1000  # 转换为ms
                stop_time = label.stop_idx / self.fs * 1000
                freq_start = (label.centor_freq - label.bw/2) / 1e6  # 转换为MHz
                freq_stop = (label.centor_freq + label.bw/2) / 1e6
                
                rect = patches.Rectangle(
                    (start_time, freq_start),
                    stop_time - start_time,
                    freq_stop - freq_start,
                    linewidth=2,
                    edgecolor='r',
                    facecolor='none',
                    label='传统方法' if label == traditional_labels[0] else None
                )
                axes[1].add_patch(rect)

            # 添加边缘检测方法的检测框（绿色）
            for label in edge_labels:
                start_time = label.start_idx / self.fs * 1000
                stop_time = label.stop_idx / self.fs * 1000
                freq_start = (label.centor_freq - label.bw/2) / 1e6
                freq_stop = (label.centor_freq + label.bw/2) / 1e6
                
                rect = patches.Rectangle(
                    (start_time, freq_start),
                    stop_time - start_time,
                    freq_stop - freq_start,
                    linewidth=2,
                    edgecolor='g',
                    facecolor='none',
                    label='边缘检测' if label == edge_labels[0] else None
                )
                axes[1].add_patch(rect)

            axes[1].set_title('检测结果对比')
            axes[1].set_xlabel('时间 (ms)')
            axes[1].set_ylabel('频率 (MHz)')
            if len(traditional_labels) > 0 or len(edge_labels) > 0:
                axes[1].legend()
            fig.colorbar(im2, ax=axes[1], label='幅度')

            plt.tight_layout()
            plt.show()

            # 输出文字结果
            print("\n检测结果统计:")
            print("="*50)
            print(f"传统方法检测到信号数量: {len(traditional_labels)}")
            for i, label in enumerate(traditional_labels):
                duration = (label.stop_idx - label.start_idx) / self.fs * 1000
                print(f"\n传统方法信号 #{i+1}:")
                print(f"  时间范围: {label.start_idx/self.fs*1000:.2f} - {label.stop_idx/self.fs*1000:.2f} ms")
                print(f"  持续时间: {duration:.2f} ms")
                print(f"  中心频率: {label.centor_freq/1e6:.2f} MHz")
                print(f"  信号带宽: {label.bw/1e6:.2f} MHz")

            print("\n" + "="*50)
            print(f"边缘检测方法检测到信号数量: {len(edge_labels)}")
            for i, label in enumerate(edge_labels):
                duration = (label.stop_idx - label.start_idx) / self.fs * 1000
                print(f"\n边缘检测信号 #{i+1}:")
                print(f"  时间范围: {label.start_idx/self.fs*1000:.2f} - {label.stop_idx/self.fs*1000:.2f} ms")
                print(f"  持续时间: {duration:.2f} ms")
                print(f"  中心频率: {label.centor_freq/1e6:.2f} MHz")
                print(f"  信号带宽: {label.bw/1e6:.2f} MHz")

        except Exception as e:
            print(f"绘图过程中出错: {e}")
            plt.close('all')

def real_time_comparison():
    # 初始化分析器
    analyzer = LabelComparisonAnalyzer(fc=2435e6, fs=15.36e6, bw=12e6)

    # 初始化USRP
    try:
        usrp = uhd.usrp.MultiUSRP(analyzer.args.args)
    except Exception as e:
        print(f"USRP 初始化错误: {e}")
        return

    chan_id = 0

    while True:
        try:
            print("正在采集信号...")

            # 采集信号
            rxDatas = multi_usrp_rx(usrp, analyzer.args)
            samples = rxDatas[chan_id, :]
            samples = samples * (2 ** 15 - 1)

            print(f"信号长度: {len(samples)}")

            # 获取传统方法标签
            print("使用传统方法分析...")
            traditional_labels = analyzer.get_traditional_labels(samples)

            # 获取边缘检测方法标签
            print("使用边缘检测方法分析...")
            edge_labels, original_spectrum, result_image, contours = analyzer.get_edge_detection_labels(samples)
            if len(traditional_labels) or len(contours):
                analyzer.plot_comparison(traditional_labels, edge_labels, original_spectrum, contours)
            # 等待用户输入继续或退出
            user_input = input("按 Enter 继续，输入 'q' 退出: ")
            if user_input.lower() == 'q':
                break

        except Exception as e:
            print(f"处理错误: {e}")
            continue

    print("程序结束")




if __name__ == '__main__':
    real_time_comparison()