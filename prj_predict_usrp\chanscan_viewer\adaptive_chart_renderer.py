#!/usr/bin/env python3
"""
自适应图表渲染器模块

提供高性能、非阻塞的图表渲染功能，优化大型数据集的处理和显示
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import spectrogram
from concurrent.futures import ThreadPoolExecutor, Future
import threading
import time
import traceback

class AdaptiveChartRenderer:
    """
    自适应图表渲染器类
    
    根据数据大小和可用资源自适应地渲染图表，提供异步渲染和进度报告
    """
    
    def __init__(self, max_workers=4):
        """
        初始化渲染器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.current_tasks = {}  # 当前任务字典 {task_id: Future}
        self.task_counter = 0  # 任务计数器，用于生成唯一ID
        self.lock = threading.Lock()  # 线程锁，用于保护共享资源
        
        # GPU加速支持检测
        self.gpu_available = False
        self.gpu_backend = None
        try:
            import cupy as cp
            self.cp = cp
            self.gpu_available = True
            self.gpu_backend = 'cupy'
            print("GPU加速可用 (CuPy)")
        except ImportError:
            try:
                import torch
                if torch.cuda.is_available():
                    self.torch = torch
                    self.gpu_available = True
                    self.gpu_backend = 'torch'
                    print("GPU加速可用 (PyTorch)")
                else:
                    print("GPU不可用，使用CPU计算")
            except ImportError:
                print("未安装GPU加速库，使用CPU计算")
        except Exception as e:
            print(f"GPU加速检查失败: {e}")
    
    def generate_task_id(self):
        """生成唯一的任务ID"""
        with self.lock:
            self.task_counter += 1
            return f"task_{self.task_counter}"
    
    def cancel_task(self, task_id):
        """
        取消指定的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        with self.lock:
            if task_id in self.current_tasks:
                future = self.current_tasks[task_id]
                cancelled = future.cancel()
                if cancelled or future.done():
                    self.current_tasks.pop(task_id, None)
                return cancelled
            return False
    
    def cancel_all_tasks(self):
        """
        取消所有任务
        
        Returns:
            int: 取消的任务数量
        """
        with self.lock:
            task_ids = list(self.current_tasks.keys())
            
        cancelled_count = 0
        for task_id in task_ids:
            if self.cancel_task(task_id):
                cancelled_count += 1
        
        return cancelled_count
    
    def render_time_domain_points(self, signal_data, axes, canvas, progress_callback=None):
        """
        渲染时域信号点值图
        
        Args:
            signal_data: 信号数据
            axes: matplotlib轴对象
            canvas: matplotlib画布对象
            progress_callback: 进度回调函数，接受三个参数：进度值(0-100)、状态消息、任务ID
            
        Returns:
            str: 任务ID
        """
        task_id = self.generate_task_id()
        
        def _render():
            try:
                if progress_callback:
                    progress_callback(0, "准备绘制时域信号点值图...", task_id)
                
                # 清除轴
                axes.clear()
                
                # 获取信号的实部和虚部
                signal_i = np.real(signal_data)
                signal_q = np.imag(signal_data)
                
                # 智能抽样
                N_sig = len(signal_data)
                if N_sig > 100000:  # 超过10万点时进行抽样
                    step = max(1, N_sig // 100000)
                    indices = np.arange(0, N_sig, step)
                    signal_i_display = signal_i[indices]
                    signal_q_display = signal_q[indices]
                    if progress_callback:
                        progress_callback(10, f"抽样完成: {len(indices)}/{N_sig} 点", task_id)
                else:
                    indices = np.arange(N_sig)
                    signal_i_display = signal_i
                    signal_q_display = signal_q
                    if progress_callback:
                        progress_callback(10, "无需抽样", task_id)
                
                # X轴转换为1e5单位
                indices_1e5 = indices / 1e5
                
                if progress_callback:
                    progress_callback(30, "绘制I路信号...", task_id)
                
                # 绘制I路信号
                axes.plot(indices_1e5, signal_i_display, 'b-', linewidth=0.5, rasterized=True)
                
                if progress_callback:
                    progress_callback(60, "绘制Q路信号...", task_id)
                
                # 绘制Q路信号
                axes.plot(indices_1e5, signal_q_display, 'r-', linewidth=0.5, rasterized=True)
                
                # 设置标题和标签
                axes.set_title("时域信号点值图")
                axes.set_xlabel("时间点 (×10^5)")
                axes.set_ylabel("信号电压")
                axes.grid(True, alpha=0.3)
                
                # 设置y轴范围
                y_min = min(np.min(signal_i_display), np.min(signal_q_display))
                y_max = max(np.max(signal_i_display), np.max(signal_q_display))
                margin = (y_max - y_min) * 0.05  # 5%的边距
                axes.set_ylim(y_min - margin, y_max + margin)
                
                # 设置X轴范围和刻度
                x_max_1e5 = (N_sig - 1) / 1e5
                axes.set_xlim(0, x_max_1e5)
                
                # 设置刻度
                tick_step = 0.5
                x_ticks = np.arange(0, x_max_1e5 + tick_step, tick_step)
                axes.set_xticks(x_ticks)
                axes.set_xticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in x_ticks])
                
                if progress_callback:
                    progress_callback(90, "更新画布...", task_id)
                
                # 更新画布
                canvas.draw_idle()
                
                if progress_callback:
                    progress_callback(100, "时域信号点值图绘制完成", task_id)
                
                return True
            except Exception as e:
                print(f"时域信号点值图绘制失败: {e}")
                traceback.print_exc()
                if progress_callback:
                    progress_callback(-1, f"绘制失败: {str(e)}", task_id)
                return False
        
        # 提交任务到线程池
        future = self.thread_pool.submit(_render)
        with self.lock:
            self.current_tasks[task_id] = future
        
        return task_id
    
    def render_time_domain_signal(self, signal_data, fs, axes, canvas, progress_callback=None):
        """
        渲染时域信号图
        
        Args:
            signal_data: 信号数据
            fs: 采样率 (Hz)
            axes: matplotlib轴对象
            canvas: matplotlib画布对象
            progress_callback: 进度回调函数，接受三个参数：进度值(0-100)、状态消息、任务ID
            
        Returns:
            str: 任务ID
        """
        task_id = self.generate_task_id()
        
        def _render():
            try:
                if progress_callback:
                    progress_callback(0, "准备绘制时域信号图...", task_id)
                
                # 清除轴
                axes.clear()
                
                # 获取信号的实部和虚部
                signal_i = np.real(signal_data)
                signal_q = np.imag(signal_data)
                
                # 智能抽样
                N_sig = len(signal_data)
                if N_sig > 100000:  # 超过10万点时进行抽样
                    step = max(1, N_sig // 100000)
                    indices = np.arange(0, N_sig, step)
                    signal_i_display = signal_i[indices]
                    signal_q_display = signal_q[indices]
                    if progress_callback:
                        progress_callback(10, f"抽样完成: {len(indices)}/{N_sig} 点", task_id)
                else:
                    indices = np.arange(N_sig)
                    signal_i_display = signal_i
                    signal_q_display = signal_q
                    if progress_callback:
                        progress_callback(10, "无需抽样", task_id)
                
                # 计算时间轴
                signal_time_start = 0
                signal_time_end = (N_sig - 1) / fs * 1000  # 完整数据的时间范围(ms)
                signal_time_display = indices / fs * 1000  # 显示点对应的实际时间
                
                if progress_callback:
                    progress_callback(30, "绘制I路信号...", task_id)
                
                # 绘制I路信号
                axes.plot(signal_time_display, signal_i_display, 'b-', linewidth=0.5)
                
                if progress_callback:
                    progress_callback(60, "绘制Q路信号...", task_id)
                
                # 绘制Q路信号
                axes.plot(signal_time_display, signal_q_display, 'r-', linewidth=0.5)
                
                # 设置标题和标签
                axes.set_title("时域信号图")
                axes.set_xlabel("时间(ms)")
                axes.set_ylabel("电压(V)")
                axes.grid(True, alpha=0.3)
                
                # 设置y轴范围
                y_min = min(np.min(signal_i_display), np.min(signal_q_display))
                y_max = max(np.max(signal_i_display), np.max(signal_q_display))
                margin = (y_max - y_min) * 0.05  # 5%的边距
                axes.set_ylim(y_min - margin, y_max + margin)
                
                # 设置X轴范围
                axes.set_xlim(signal_time_start, signal_time_end)
                
                # 设置时域信号图的x轴刻度
                if signal_time_end > 0:
                    # 根据时间范围设置合适的刻度间隔
                    if signal_time_end <= 5:
                        time_tick_step = 1  # 0-5ms：每1ms一个刻度
                    elif signal_time_end <= 20:
                        time_tick_step = 2  # 5-20ms：每2ms一个刻度
                    elif signal_time_end <= 50:
                        time_tick_step = 5  # 20-50ms：每5ms一个刻度
                    else:
                        time_tick_step = 10  # >50ms：每10ms一个刻度
                    
                    time_ticks = np.arange(0, signal_time_end + time_tick_step, time_tick_step)
                    axes.set_xticks(time_ticks)
                    axes.set_xticklabels([f'{int(tick)}' for tick in time_ticks])
                
                if progress_callback:
                    progress_callback(90, "更新画布...", task_id)
                
                # 更新画布
                canvas.draw_idle()
                
                if progress_callback:
                    progress_callback(100, "时域信号图绘制完成", task_id)
                
                return True
            except Exception as e:
                print(f"时域信号图绘制失败: {e}")
                traceback.print_exc()
                if progress_callback:
                    progress_callback(-1, f"绘制失败: {str(e)}", task_id)
                return False
        
        # 提交任务到线程池
        future = self.thread_pool.submit(_render)
        with self.lock:
            self.current_tasks[task_id] = future
        
        return task_id
    
    def render_spectrum(self, signal_data, fs, fc, axes, canvas, progress_callback=None):
        """
        渲染频谱图
        
        Args:
            signal_data: 信号数据
            fs: 采样率 (Hz)
            fc: 中心频率 (Hz)
            axes: matplotlib轴对象
            canvas: matplotlib画布对象
            progress_callback: 进度回调函数，接受三个参数：进度值(0-100)、状态消息、任务ID
            
        Returns:
            str: 任务ID
        """
        task_id = self.generate_task_id()
        
        def _render():
            try:
                if progress_callback:
                    progress_callback(0, "准备计算频谱...", task_id)
                
                # 清除轴
                axes.clear()
                
                # 计算频谱
                sig_len = len(signal_data)
                
                # 根据信号长度选择最优处理方式
                if sig_len <= 1048576:  # <=100万点：直接FFT
                    if progress_callback:
                        progress_callback(10, "使用直接FFT计算...", task_id)
                    
                    # 尝试GPU加速
                    if self.gpu_available and self.gpu_backend == 'cupy':
                        try:
                            if progress_callback:
                                progress_callback(20, "使用GPU加速FFT (CuPy)...", task_id)
                            sig_gpu = self.cp.asarray(signal_data)
                            fftdata = self.cp.fft.fftshift(self.cp.abs(self.cp.fft.fft(sig_gpu)) / sig_len)
                            fftdata = self.cp.asnumpy(fftdata)  # 转回CPU
                        except Exception as e:
                            if progress_callback:
                                progress_callback(20, f"GPU FFT失败，回退到CPU: {e}", task_id)
                            fftdata = np.fft.fftshift(np.abs(np.fft.fft(signal_data)) / sig_len)
                    elif self.gpu_available and self.gpu_backend == 'torch':
                        try:
                            if progress_callback:
                                progress_callback(20, "使用GPU加速FFT (PyTorch)...", task_id)
                            import torch
                            sig_tensor = torch.from_numpy(signal_data).cuda()
                            fft_result = torch.fft.fft(sig_tensor)
                            fftdata = torch.fft.fftshift(torch.abs(fft_result) / sig_len)
                            fftdata = fftdata.cpu().numpy()
                        except Exception as e:
                            if progress_callback:
                                progress_callback(20, f"GPU FFT失败，回退到CPU: {e}", task_id)
                            fftdata = np.fft.fftshift(np.abs(np.fft.fft(signal_data)) / sig_len)
                    else:
                        if progress_callback:
                            progress_callback(20, "使用CPU计算FFT...", task_id)
                        fftdata = np.fft.fftshift(np.abs(np.fft.fft(signal_data)) / sig_len)
                    
                    # 生成频率轴
                    freq_indices = np.arange(sig_len, dtype=np.float64)
                    nb_freqs = fc - fs/2.0 + freq_indices * (fs / sig_len)
                    freqs_mhz = nb_freqs / 1e6
                
                elif sig_len <= 4194304:  # 100万-400万点：智能抽样FFT
                    if progress_callback:
                        progress_callback(10, "使用智能抽样FFT...", task_id)
                    
                    # 智能抽样
                    target_len = 1048576  # 目标100万点
                    step = max(1, sig_len // target_len)
                    sampled_sig = signal_data[::step]
                    
                    if progress_callback:
                        progress_callback(20, f"抽样完成: {len(sampled_sig)}/{sig_len} 点，步长={step}", task_id)
                    
                    # 对抽样信号进行FFT
                    sampled_len = len(sampled_sig)
                    fftdata = np.fft.fftshift(np.abs(np.fft.fft(sampled_sig)) / sampled_len)
                    
                    # 频率轴计算（基于原始采样率）
                    freq_indices = np.arange(sampled_len, dtype=np.float64)
                    nb_freqs = fc - fs/2.0 + freq_indices * (fs / sampled_len)
                    freqs_mhz = nb_freqs / 1e6
                
                else:  # >400万点：分段平均FFT（MATLAB方式）
                    if progress_callback:
                        progress_callback(10, "使用分段平均FFT (MATLAB风格)...", task_id)
                    
                    # MATLAB风格的分段参数
                    fft_len = 65536  # 64K FFT长度，平衡分辨率和计算量
                    overlap = fft_len // 2  # 50%重叠
                    window = np.blackman(fft_len)  # 与MATLAB一致的窗函数
                    
                    # 计算分段数量
                    step = fft_len - overlap
                    num_segments = (sig_len - overlap) // step
                    
                    if progress_callback:
                        progress_callback(20, f"分段参数: FFT长度={fft_len}，重叠={overlap}，分段数={num_segments}", task_id)
                    
                    if num_segments <= 0:
                        # 回退到直接FFT
                        if progress_callback:
                            progress_callback(25, "分段数量不足，回退到直接FFT", task_id)
                        fftdata = np.fft.fftshift(np.abs(np.fft.fft(signal_data)) / sig_len)
                        freq_indices = np.arange(sig_len, dtype=np.float64)
                        nb_freqs = fc - fs/2.0 + freq_indices * (fs / sig_len)
                        freqs_mhz = nb_freqs / 1e6
                    else:
                        # 分段FFT并平均
                        accumulated_fft = np.zeros(fft_len, dtype=np.complex128)
                        valid_segments = 0
                        
                        for i in range(num_segments):
                            if progress_callback and i % 10 == 0:
                                progress = 30 + int(50 * i / num_segments)
                                progress_callback(progress, f"处理分段 {i+1}/{num_segments}...", task_id)
                            
                            start_idx = i * step
                            end_idx = start_idx + fft_len
                            
                            if end_idx <= sig_len:
                                # 提取分段并加窗
                                segment = signal_data[start_idx:end_idx] * window
                                # FFT计算
                                segment_fft = np.fft.fft(segment)
                                accumulated_fft += segment_fft
                                valid_segments += 1
                        
                        # 平均并归一化
                        if valid_segments > 0:
                            averaged_fft = accumulated_fft / valid_segments
                            fftdata = np.fft.fftshift(np.abs(averaged_fft) / fft_len)
                        else:
                            # 回退处理
                            if progress_callback:
                                progress_callback(80, "有效分段数量为0，回退到智能抽样FFT", task_id)
                            
                            # 智能抽样
                            target_len = 1048576  # 目标100万点
                            step = max(1, sig_len // target_len)
                            sampled_sig = signal_data[::step]
                            
                            # 对抽样信号进行FFT
                            sampled_len = len(sampled_sig)
                            fftdata = np.fft.fftshift(np.abs(np.fft.fft(sampled_sig)) / sampled_len)
                            
                            # 频率轴计算（基于原始采样率）
                            freq_indices = np.arange(sampled_len, dtype=np.float64)
                            nb_freqs = fc - fs/2.0 + freq_indices * (fs / sampled_len)
                            freqs_mhz = nb_freqs / 1e6
                            
                            return
                        
                        # 频率轴计算
                        freq_indices = np.arange(fft_len, dtype=np.float64) 
                        nb_freqs = fc - fs/2.0 + freq_indices * (fs / fft_len)
                        freqs_mhz = nb_freqs / 1e6
                
                if progress_callback:
                    progress_callback(80, "绘制频谱图...", task_id)
                
                # 绘制频谱图
                axes.plot(freqs_mhz, fftdata, 'g-', linewidth=0.8)
                axes.set_title(f'频谱图 [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                axes.set_xlabel("频率(MHz)")
                axes.set_ylabel("频谱值")
                axes.grid(True, alpha=0.3)
                
                # 消除X轴留白：精确设置频率范围
                axes.set_xlim(freqs_mhz[0], freqs_mhz[-1])
                
                # 自适应设置X轴刻度
                freq_min = freqs_mhz[0]
                freq_max = freqs_mhz[-1]
                freq_span = freq_max - freq_min
                
                if freq_span > 0:
                    # 根据频率范围选择合适的刻度间隔
                    if freq_span <= 10:
                        tick_step = 1  # 小于10MHz范围，每1MHz一个刻度
                    elif freq_span <= 50:
                        tick_step = 5  # 10-50MHz范围，每5MHz一个刻度
                    elif freq_span <= 100:
                        tick_step = 10  # 50-100MHz范围，每10MHz一个刻度
                    else:
                        tick_step = 20  # 大于100MHz范围，每20MHz一个刻度
                    
                    # 计算刻度位置
                    start_tick = np.ceil(freq_min / tick_step) * tick_step
                    end_tick = np.floor(freq_max / tick_step) * tick_step
                    ticks = np.arange(start_tick, end_tick + tick_step, tick_step)
                    
                    # 设置刻度
                    axes.set_xticks(ticks)
                    axes.set_xticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in ticks])
                
                if progress_callback:
                    progress_callback(90, "更新画布...", task_id)
                
                # 更新画布
                canvas.draw_idle()
                
                if progress_callback:
                    progress_callback(100, "频谱图绘制完成", task_id)
                
                return True
            except Exception as e:
                print(f"频谱图绘制失败: {e}")
                traceback.print_exc()
                if progress_callback:
                    progress_callback(-1, f"绘制失败: {str(e)}", task_id)
                return False
        
        # 提交任务到线程池
        future = self.thread_pool.submit(_render)
        with self.lock:
            self.current_tasks[task_id] = future
        
        return task_id
    
    def render_time_frequency(self, signal_data, fs, fc, axes, canvas, progress_callback=None):
        """
        渲染时频图
        
        Args:
            signal_data: 信号数据
            fs: 采样率 (Hz)
            fc: 中心频率 (Hz)
            axes: matplotlib轴对象
            canvas: matplotlib画布对象
            progress_callback: 进度回调函数，接受三个参数：进度值(0-100)、状态消息、任务ID
            
        Returns:
            str: 任务ID
        """
        task_id = self.generate_task_id()
        
        def _render():
            try:
                if progress_callback:
                    progress_callback(0, "准备计算时频图...", task_id)
                
                # 清除轴
                axes.clear()
                
                # 时频图数据优化：保持质量的前提下适度优化
                stft_sig = signal_data
                sig_len = len(signal_data)
                
                if sig_len > 8000000:  # 超过800万点，适度抽样
                    step = sig_len // 2000000  # 目标约200万点，保持更多细节
                    stft_sig = signal_data[::step]
                    if progress_callback:
                        progress_callback(10, f"适度抽样至 {len(stft_sig)}/{sig_len} 点，步长={step}", task_id)
                elif sig_len > 4000000:  # 超过400万点时进行轻度优化
                    step = sig_len // 1500000  # 目标约150万点
                    stft_sig = signal_data[::step]
                    if progress_callback:
                        progress_callback(10, f"轻度抽样至 {len(stft_sig)}/{sig_len} 点，步长={step}", task_id)
                else:
                    if progress_callback:
                        progress_callback(10, "无需抽样", task_id)
                
                # 时频图计算参数 - 保持质量优先
                # 根据信号长度动态调整参数，但保持50%重叠以确保质量
                stft_len = len(stft_sig)
                if stft_len > 1000000:  # 超过100万点
                    window = 2048
                    noverlap = window // 2  # 保持50%重叠确保质量
                elif stft_len > 500000:  # 超过50万点
                    window = 1024
                    noverlap = window // 2
                elif stft_len > 100000:  # 超过10万点
                    window = 512
                    noverlap = window // 2
                else:
                    window = 256
                    noverlap = window // 2

                nfft = window

                # 数据长度检查，如果数据太少则进一步调整窗口大小
                if stft_len < window * 2:
                    if stft_len > 1024:
                        window = 512
                    elif stft_len > 512:
                        window = 256
                    else:
                        window = 128
                    noverlap = window // 4
                    nfft = window
                
                if progress_callback:
                    progress_callback(20, f"时频图参数: 窗口={window}, 重叠={noverlap}, 信号长度={stft_len}", task_id)
                
                try:
                    if progress_callback:
                        progress_callback(30, "计算时频图...", task_id)
                    
                    # 使用scipy的spectrogram函数，对应MATLAB的spectrogram
                    f_spec, t_spec, Pxx = spectrogram(stft_sig, fs=fs, window='hann', 
                                                     nperseg=window, noverlap=noverlap, 
                                                     nfft=nfft, return_onesided=False)
                    
                    if progress_callback:
                        progress_callback(60, "处理时频图数据...", task_id)
                    
                    # MATLAB中重新计算频率轴
                    F = (np.arange(-window//2, window//2) * fs / window + fc) / 1e6  # MHz
                    
                    # 时间轴需要根据原始信号长度进行调整
                    if len(stft_sig) != len(signal_data):
                        # 如果使用了抽样，需要调整时间轴到原始信号的时间范围
                        time_scale = len(signal_data) / len(stft_sig)
                        T = t_spec * 1000 * time_scale  # 调整时间轴
                    else:
                        T = t_spec * 1000  # ms
                    
                    # MATLAB功率计算：10*log10(circshift(P,window/2))
                    Pxx_shifted = np.roll(Pxx, window//2, axis=0)  # 频域移位
                    Pxx_db = 10 * np.log10(Pxx_shifted + 1e-10)    # dB转换
                    
                    if progress_callback:
                        progress_callback(80, "绘制时频图...", task_id)
                    
                    # 绘制时频图 - 严格按照MATLAB的方式
                    # 设置显示范围：[X轴最小值, X轴最大值, Y轴最小值, Y轴最大值]
                    # 对应：[时间最小值, 时间最大值, 频率最小值, 频率最大值]
                    extent = [T[0], T[-1], F[0], F[-1]]
                    
                    # 使用imshow显示时频图 - 保持原始质量
                    # 注意：Pxx_db的维度应该是[频率bins, 时间frames]，正好对应imshow的[行,列]
                    im = axes.imshow(Pxx_db, aspect='auto', cmap='viridis',
                                   extent=extent, origin='lower',
                                   interpolation='bilinear')  # 保持原始双线性插值以确保图像质量
                    
                    axes.set_title(f'时频图 [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                    axes.set_xlabel("时间(ms)")  # X轴：时间(ms)
                    axes.set_ylabel("频率(MHz)")  # Y轴：频率(MHz)
                    
                    # 设置轴范围 - 时间轴对应完整数据范围
                    if len(T) > 1:
                        axes.set_xlim(T[0], T[-1])
                    
                    # 频率轴范围
                    if len(F) > 1:
                        axes.set_ylim(F[0], F[-1])
                    
                    # 设置自定义Y轴刻度
                    freq_min = F[0]
                    freq_max = F[-1]
                    
                    # 根据频率范围选择合适的刻度间隔
                    if freq_max - freq_min <= 10:
                        tick_step = 1  # 小于10MHz范围，每1MHz一个刻度
                    elif freq_max - freq_min <= 50:
                        tick_step = 5  # 10-50MHz范围，每5MHz一个刻度
                    elif freq_max - freq_min <= 100:
                        tick_step = 10  # 50-100MHz范围，每10MHz一个刻度
                    else:
                        tick_step = 20  # 大于100MHz范围，每20MHz一个刻度
                    
                    # 计算刻度位置
                    start_tick = np.ceil(freq_min / tick_step) * tick_step
                    end_tick = np.floor(freq_max / tick_step) * tick_step
                    ticks = np.arange(start_tick, end_tick + tick_step, tick_step)
                    
                    # 设置刻度
                    axes.set_yticks(ticks)
                    axes.set_yticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in ticks])
                    
                except Exception as e:
                    if progress_callback:
                        progress_callback(80, f"时频图计算失败: {e}，绘制简化图表", task_id)
                    
                    # 绘制简化的幅度图
                    axes.plot(np.abs(stft_sig), 'purple', linewidth=0.5)
                    axes.set_title('信号幅度 (时频图计算失败)')
                    axes.set_xlabel('采样点')
                    axes.set_ylabel('幅度')
                    axes.grid(True, alpha=0.3)
                    
                    # 消除X轴留白
                    if len(stft_sig) > 1:
                        axes.set_xlim(0, len(stft_sig) - 1)
                
                if progress_callback:
                    progress_callback(90, "更新画布...", task_id)
                
                # 更新画布
                canvas.draw_idle()
                
                if progress_callback:
                    progress_callback(100, "时频图绘制完成", task_id)
                
                return True
            except Exception as e:
                print(f"时频图绘制失败: {e}")
                traceback.print_exc()
                if progress_callback:
                    progress_callback(-1, f"绘制失败: {str(e)}", task_id)
                return False
        
        # 提交任务到线程池
        future = self.thread_pool.submit(_render)
        with self.lock:
            self.current_tasks[task_id] = future
        
        return task_id