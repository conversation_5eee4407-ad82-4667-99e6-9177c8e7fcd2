%    function   : cmdlabelRecByManual
%    Description: 信号标注提取工具
%    author     : 刘智国
%    主要步骤：1 修改信号文件
%             2 查找确定信号 起始点, 结束点,中心频率
%             3 添加标注信息如:nb_FF-433\3.bvsp;[647273:1601700];445.742e6
close all;
IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数
%% 加载文件
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_433M\P21.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_Jouav\65.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_rc_Graupner\5.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_rc_Microzone_6cmini\2.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_Keweitai_x6lm\5.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_Glider\3.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_rc_Upair\12.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_yaboot\7.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\noise_env\noise_Jouav21.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_FF-433\3.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_RFD900X\15.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_Skydroid\1.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_flyskyfsst8\9.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_frskyx9d\11.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_microhard_840\52.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_Frsky_x9dse\35.dat');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_RFD900X\16.dat');
% [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_tw_433\4.bvsp');
%[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_HITEC_FLAH8_2430\7.bvsp');
[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig('E:\ftproot\signalDB\nb_TFMODEL_TF8G\9.bvsp');

%% 标注过程
%(1)输入命令：寻找起始点, 结束点
showWBSig(wb_signal, wb_fc, wb_fs);%显示宽带信号 - 时频域
%(2)命令行输入，验证标注：wb_signal(起始点, 结束点),如下所示，并确定中心频率
%showWBSig(wb_signal(916586:1021530),wb_fc,wb_fs);


%GenDataset_lbls('E:\software\nxtool\packages\samDb\class_def_test.txt',80)
%GenDataset_lbls('E:\ftproot\signalDB\class_def.txt',20)
%GenDSlbls_all('E:\ftproot\signalDB\class_def.txt');
%[Table_train]=getTrainTable('.\Train_ds_all.txt');
%[Table_train]=getTrainTable('E:\ftproot\signalDB\nb_433M\Train_records.txt');