# =======================================================================================================================
#   Function    ：calregions.py
#   Description : 计算联通区域
#
#   Parameter   :
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import numpy as np
from scipy import ndimage
from chanlib.findAccuractPos import findAccuractPos
import matplotlib.pyplot as plt
from chanlib.findAccuracteBW import findAccuracteBW
from chanlib.ExtractNBSig import ExtractNBSig, getFreqIndex, getfftData, IsValidNarrowBW
from chanlib.wr_train_sig import WrTrainSig
from skimage import measure


def calunionregion(psd_value, wb_signal, t, f, delta_f, fft_overlap, wb_fc, bshowdgraph):
    # 从二值图像 BW 中删除少于 P 个像素的所有连通分量（对象）
    P = 200 * 1
    # labeled_image, num_features = measure.label(psd_value, connectivity=2, return_num=True)
    mystructure = np.ones((3, 3))
    # mystructure[0,0]=0
    # mystructure[2,0]=0
    # mystructure[0,2]=0
    # mystructure[2,2]=0

    psd_value = ndimage.binary_opening(psd_value, structure=mystructure, iterations=1)
    psd_value = psd_value.astype(int)
    # 根据联通区域，标识各个区域的标签
    L, NUM = ndimage.label(psd_value)

    # 计算每个连通分量的像素数量
    sizes = ndimage.sum(psd_value, L, range(1, NUM + 1))

    # 找出像素数量少于 200 的连通分量的标签
    small_components = np.where(sizes < P)[0] + 1

    # 删除这些连通分量
    for component in small_components:
        psd_value[L == component] = 0
        # L[L == component] = 0

    # 重新计算通联区域
    psd_value = ndimage.binary_opening(psd_value, structure=np.ones((3, 3)), iterations=1)
    psd_value = psd_value.astype(int)
    # 根据联通区域，标识各个区域的标签
    L, NUM = ndimage.label(psd_value)

    class Label:
        def __init__(self):
            self.left = 1e6
            self.right = 0
            self.up = 1e6
            self.down = 0
            self.valid = 1
            self.centor_freq = 0
            self.start_idx = 0
            self.stop_idx = 0
            self.bw = 0

    label = [Label() for _ in range(NUM)]
    # label = []

    # 1 对于行列图，取不为0的行列，标记其具体位置信息，根据联通区域NUM，对应到标签label

    # 获取第一个标识区域的所有像素点的坐标
    for n in range(0, len(label)):
        rows, cols = np.where(L == n + 1)
        # 计算最左、最右、最上及最下像素点的行列值
        label[n].left = np.min(cols)
        label[n].right = np.max(cols)
        label[n].up = np.min(rows)
        label[n].down = np.max(rows)

    limit_val = 0.6 # 区域中1的比例门槛，如500个点，大于80%的点为1，则判定为联通区域
    for n in range(0, len(label)):
        up_flg = 0
        #for m in range(label[n].up, (label[n].down - label[n].up + 1) // 2 + label[n].up + 1):  # 为了round，所以多加1 （会遗漏一些可能的信号）
        for m in range(label[n].up, label[n].down): #扩展其行(频域)搜索区域
            mean_l = np.mean(L[m, label[n].left:label[n].right]) / (n + 1)  # 除以每行标号，从1开始
            if mean_l > limit_val:
                label[n].up = m
                up_flg = 1
                break
        if up_flg == 0:
            label[n].valid = 0

        down_flg = 0
        #for m in range(label[n].down, label[n].down - (label[n].down - label[n].up + 1) // 2 - 1, -1):  # 为了round，所以多加1 （会遗漏一些可能的信号）
        for m in range(label[n].down, label[n].up, -1):#扩展其行(频域)搜索区域
            mean_l = np.mean(L[m, label[n].left:label[n].right]) / (n + 1)
            if mean_l > limit_val:
                label[n].down = m
                down_flg = 1
                break
        if down_flg == 0:
            label[n].valid = 0

    for n in range(0, len(label)):
        left_flg = 0
        #for m in range(label[n].left, (label[n].right - label[n].left + 1) // 2 + label[n].left + 1):
        for m in range(label[n].left, label[n].right): #扩展其列(时域)搜索区域
            mean_l = np.mean(L[label[n].up:label[n].down, m]) / (n + 1)
            if mean_l > limit_val:
                label[n].left = m
                left_flg = 1
                break
        if left_flg == 0:
            label[n].valid = 0

        right_flg = 0
        #for m in range(label[n].right, label[n].right - (label[n].right - label[n].left + 1) // 2 - 1, -1):
        for m in range(label[n].right, label[n].left, -1): #扩展其列(时域)搜索区域
            mean_l = np.mean(L[label[n].up:label[n].down, m]) / (n + 1)
            if mean_l > limit_val:
                label[n].right = m
                right_flg = 1
                break
        if right_flg == 0:
            label[n].valid = 0

    # 重新给扩大部分标记标号(1，2) +1...，显示边框，便于观察
    for n in range(0, len(label)):
        if label[n].valid:
            L[label[n].up, label[n].left:label[n].right] = len(label) + 10
            L[label[n].down, label[n].left:label[n].right] = len(label) + 10
            L[label[n].up:label[n].down, label[n].left] = len(label) + 10
            L[label[n].up:label[n].down, label[n].right] = len(label) + 10

    # 计算中心频率及带宽
    for n in range(0, len(label)):
        y = (label[n].down - label[n].up) // 2 + label[n].up
        x = (label[n].right - label[n].left) // 2 + label[n].left + 1
        L[y - 1:y + 2, x - 1:x + 2] = len(label) + 20  # 标记中心位置

        label[n].bw = (label[n].down - label[n].up) * delta_f  # 带宽估计
        label[n].centor_freq = (y - len(f) / 2) * delta_f + wb_fc  # 中心频率估计
        label[n].start_idx = (label[n].left + 2 ) * fft_overlap - fft_overlap - 1
        if label[n].start_idx - fft_overlap < 0:
            label[n].start_idx = 0
        label[n].stop_idx = (label[n].right + 1) * fft_overlap + fft_overlap - 1
        if label[n].stop_idx > len(wb_signal):
            label[n].stop_idx = len(wb_signal)

    if bshowdgraph:
        # fig = plt.figure()
        # ax = fig.add_subplot(212, projection='3d')
        # X, Y = np.meshgrid(t, f)
        # ax.plot_surface(X, Y, L, edgecolor='none')
        # ax.set_title('联通图处理后的功率谱密度 3D')
        # ax.view_init(0, 90)
        # plt.show()

        # 显示原始图像和开运算后的图像
        ShowBinImage(psd_value, L)

    return label


def ShowBinImage(psd_value, L):
    # 显示原始图像和开运算后的图像
    fig = plt.figure()
    axe = fig.add_subplot(1, 2, 1)
    axe.imshow(psd_value, cmap='gray')
    axe.set_title('Original Image')
    axe = fig.add_subplot(1, 2, 2)
    axe.imshow(L, cmap='gray')
    axe.set_title('Opened Image')



def cal_noisepower(label, wb_signal):
    total_signal_power = 0.0
    total_clip_len = 0
    noise_power = np.sum(np.abs(wb_signal) ** 2)
    for n in range(len(label)):
        clip_signal = wb_signal[label[n].start_idx: label[n].stop_idx]
        clip_signal_power = np.sum(np.abs(clip_signal) ** 2)
        total_signal_power += clip_signal_power
        clip_signal_len = label[n].stop_idx - label[n].start_idx + 1
        total_clip_len += clip_signal_len
    mean_noise_power = ((noise_power - total_signal_power) ) / (len(wb_signal) - total_clip_len)
    return mean_noise_power

def cal_clipsignalSNR(wb_signal, lbl_startarray, lbl_endarray, noise_mean_power):
    # 提取当前信号段
    segment_signal = wb_signal[lbl_startarray - 1:lbl_endarray]  # Python索引从0开始
    # 计算信号段的总功率（能量）
    signal_noise_power = np.sum(np.abs(segment_signal) ** 2)
    # 计算噪声功率估计
    noise_power = noise_mean_power * (lbl_endarray - lbl_startarray + 1)
    # 计算信噪比（dB）
    SNR = 10 * np.log10((abs(signal_noise_power - noise_power)) / noise_power)
    return SNR


# 该函数是核心函数，用于根据label提取待检测信号，并将其保存为HDF5格式的文件
def extract_and_save_signals(filename_in, label, wb_signal, curstartPosOffset, wb_fc,  wb_fs, clip_ms, const_ms, wb_bw, fft_len, cls_id, fname_dataset, bshowdgraph, nb_bw_def=None):
    # 从完整文件名中提取最后一部分作为文件名
    sigfsubname = filename_in.split('\\')[-1]
    # 初始化索引指针，用于标记信号的序号
    indexPointer = 0

    # 计算频域功率
    fftdata, nb_freqs = getfftData(wb_signal,wb_fc,wb_fs)
    # 计算FFT数据的平均功率
    avg_fftpower = np.mean(fftdata)
    # 设置功率阈值（为平均功率的4倍）
    powerthreshold_fc = avg_fftpower * 4

    # 计算最小功率，认为是噪声功率
    min_clippower = findMinPower(wb_signal)  #功率阈值
    powerthreshold_region = min_clippower*2.1 #

    # 遍历label列表中的每个元素
    for n in range(len(label)):
        # 检查当前label是否有效
        if label[n].valid==0:
            continue #滤除无效记录   

        # 如果信号的起始索引为在[0,10] 终止索引距end[0, 10]内，label.valid = 0
        if  (label[n].start_idx <= 10) or (label[n].stop_idx >= len(wb_signal) - 10 ):
            print(f"文件:{sigfsubname} [{label[n].start_idx}, {label[n].stop_idx}] 信号被截断,跳过该条")
            continue  #

        # 计算信号的起始位置
        lbl_startpos = label[n].start_idx  + curstartPosOffset
        # 确保起始位置不小于0
        if lbl_startpos < 0:
            lbl_startpos = 0
        # 计算信号的结束位置
        lbl_endpos = label[n].stop_idx - curstartPosOffset

        # 调用自定义函数修正起始位置
        if curstartPosOffset==0:#起始位置为默认值, 可修正
            lbl_startpos = findAccuractPos(wb_signal, lbl_startpos, 2 * fft_len)
            #重新标定起始点,结束点,对于信号叠加同频干扰时，单纯幅度判别，会引起误判,所以注释掉起始位置修正代码！！！
            # lbl_startpos = findTheStartPos(wb_signal, lbl_startpos, lbl_endpos)
            # if lbl_startpos <= 10:#开始位置前未能找到上升沿，截断信号
            #     print(f"文件:{sigfsubname} [{label[n].start_idx}, {label[n].stop_idx}] 信号被截断,跳过该条")
            #     continue
            # lbl_endpos = findTheEndPos(wb_signal, lbl_startpos, lbl_endpos)
            # if lbl_endpos >= len(wb_signal) - 10:#结束位置后未能找到上升沿，截断信号
            #     print(f"文件:{sigfsubname} [{label[n].start_idx}, {label[n].stop_idx}] 信号被截断,跳过该条")
            #     continue


        # 计算裁剪长度
        clip_len = int(np.floor(wb_fs * clip_ms / 1000))
        # 计算最大长度
        max_len = int(np.floor(wb_fs * const_ms / 1000))

        # 如果结束位置超过最大长度，将其设置为最大长度
        if lbl_endpos > lbl_startpos + max_len:
            lbl_endpos = lbl_startpos + max_len
        # 如果结束位置小于起始位置加上裁剪长度的一半，跳过当前循环并输出提示信息
        elif lbl_endpos < lbl_startpos + clip_len // 2:
            print(f"[chanscan] 序号:{n} \t 采集文件:{sigfsubname} [{lbl_startpos},{lbl_endpos}] \t 持续时间为{(lbl_endpos - lbl_startpos) * 1000 / wb_fs:.2f} ms << clip_ms={clip_ms / 2:.2f} ms, 长度太短, 不处理")
            continue

        # 根据结束位置截取宽带信号
        if lbl_endpos < len(wb_signal):
            signal = wb_signal[lbl_startpos:lbl_endpos]
        else:
            signal = wb_signal[lbl_startpos:]

        avgpower_cursig = np.sqrt(np.mean(np.abs(signal)**2))  # 平均功率
        if avgpower_cursig < powerthreshold_region:
            print(f"文件:{sigfsubname} [{lbl_startpos}, {lbl_endpos}] 切片功率弱于2.1平均信号RMS,跳过该条")
            continue

        # 获取信号的中心频率
        nb_fc = label[n].centor_freq
        # 获取信号的带宽
        nb_bw = label[n].bw
        # 设置窄带信号的采样率
        nb_fs = 4e6
        
        # 3.5 带宽及中心频率合法性判断
        # (1) 带宽判断
        ret = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs)  # 判断带宽范围，防止越界
        if ret != 1:
            print(f"无效带宽输入：{sigfsubname}")
            continue

        # (2) 中心频率判断
        nIndex_fc = getFreqIndex(nb_freqs, nb_fc)
        if nIndex_fc == 0:
            print(f'警告:中心 {nb_fc/1e6} MHz 不在频率列表以内,略过此频率')
            continue

        # 3.6 fc功率大小阈值判断
        power_fc = fftdata[nIndex_fc]  # 中心频率对应的功率值
        if power_fc < powerthreshold_fc:  # 小于阈值，误报，滤除
            print(f"{sigfsubname}:rec_pos={lbl_startpos} \t fc={nb_fc/1e6:.3f} M, power={power_fc:.4f}<threshold={powerthreshold_fc:.4f}, skipped!")
            continue

        # 判断信号带宽是否超越限制
        if nb_bw>3e6:#nb_bw>1.2e6:
            print(f"文件:{sigfsubname} [{lbl_startpos}, {lbl_endpos}] 切片带宽:{nb_bw/1e6}MHz 大于1M,跳过该条")
            continue            
        # 调整带宽
        nb_bw = nb_bw * 0.8
        # 确保带宽不超过4MHz
        if nb_bw > 4e6:
            nb_bw = 4e6
        # 保存调整前的带宽
        nb_bw_prev = nb_bw
        # 如果提供了默认带宽，使用默认带宽
        if nb_bw_def is not None:
            nb_bw = nb_bw_def


        # 调用自定义函数修正中心频率和带宽 ( 对于不同信号可能会有较大差异)
        if nb_bw_def is None:#无设置时才启动重估
            nb_fc, nb_bw = findAccuracteBW(signal, nb_fc, nb_bw, wb_fc, wb_fs, fft_len)


        # 调用自定义函数提取窄带信号
        ret, nb_signal = ExtractNBSig(signal, wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs)
        if ret!=1: #不合乎信号带宽范围
            continue
        # 窄带信号的有效采样率
        nb_fs_effct = nb_fs
        # 窄带信号的长度
        nb_len_set = len(nb_signal)

        # 计算信噪比(SNR)的dB值
        snr_dB = 10 * np.log10(max((avgpower_cursig**2 - min_clippower**2) / min_clippower**2, 1e-10))

        # 索引指针加1
        indexPointer += 1
        duration_ms = nb_len_set * 1000 / nb_fs_effct
        # 输出信号的相关信息
        print(f"[chanscan] 序号:{n} (id={indexPointer}) \t 采集文件:{sigfsubname} \t [{lbl_startpos},{lbl_endpos}]\t fs_wb={wb_fs / 1e6:.2f} M fc={nb_fc / 1e6:.2f} MHz \t fs_nb={nb_fs_effct / 1e6:.2f} M \t BW={nb_bw / 1e3:.2f}({nb_bw_prev / 1e3:.2f}) K \t 分类ID:{cls_id} \t 数据点数:{nb_len_set} \t 持续时间为{duration_ms:.2f} ms snr= {snr_dB:.2f} dB")

        # 调用保存函数将窄带信号及其相关信息保存到HDF5文件中
        WrTrainSig(fname_dataset, nb_signal, cls_id, sigfsubname, nb_fc, nb_fs_effct, nb_bw, indexPointer-1, lbl_startpos, lbl_endpos, duration_ms, snr_dB)


def findMinPower(wave_data):
    """
    以切片方式寻找最小rms片段功率值
    """
    window_size = 1000*2  # 窗口采样点数
    num_windows = len(wave_data) // window_size  # 窗口个数
    min_clippower = 0
    
    for i in range(num_windows):
        start_idx = i * window_size
        end_idx = (i + 1) * window_size
        window = wave_data[start_idx:end_idx]
        rms_clippower = np.sqrt(np.mean(np.abs(window)**2))  # RMS
        
        if i == 0:
            min_clippower = rms_clippower
        else:
            if rms_clippower < min_clippower:
                min_clippower = rms_clippower
    
    return min_clippower

def isOverThresholdM(clip_signal):
    """
    第二重保障--统计切片信号中越界点数，暂时以越界点数超过1/3信号长度来判断数据越界
    是否大多数越界
    """
    signal_real = np.real(clip_signal)
    signal_imag = np.imag(clip_signal)
    flag = 0
    count_real = 0
    count_imag = 0
    signal_real_value = np.abs(signal_real)
    signal_imag_value = np.abs(signal_imag)
    
    for value in signal_real_value:
        if value > 2000:
            count_real += 1
    
    for value in signal_imag_value:
        if value > 2000:
            count_imag += 1
    
    # 有1/8点越界
    percent = 1/8
    if (count_real >= len(signal_real) * percent or 
        count_imag >= len(signal_real_value) * percent or 
        (count_imag + count_real) > 200):
        flag = 1
    
    return flag

def findTheEndPos(wave_data, clip_start_pos, clip_end_pos):
    """
    求最终的结束位置
    """
    # 参数设置
    threshold_ratio = 0.5  # 阈值比例 (RMS的10%)
    
    # 计算幅度阈值
    wav_clip = wave_data[clip_start_pos:clip_end_pos]
    rms_value = np.sqrt(np.mean(np.abs(wav_clip)**2))
    threshold = rms_value * threshold_ratio
    
    # 滑动窗口检测
    window_size = 100  # 窗口采样点数
    clip_end_pos_new = clip_end_pos - ((clip_end_pos-clip_start_pos)//10)
    num_windows = (len(wave_data) - clip_end_pos_new) // window_size - 2  # 留有余量
    sigwins_count = 0
    end_position = -1
    
    for i in range(num_windows):
        start_idx = clip_end_pos_new + i * window_size
        end_idx = clip_end_pos_new + (i + 1) * window_size
        window = wave_data[start_idx:end_idx]
        
        # 计算窗口RMS
        window_rms = np.sqrt(np.mean(np.abs(window)**2))
        
        # 判断是否为信号窗口
        if window_rms > threshold:
            sigwins_count += 1
        else:
            end_position = end_idx  # 已发现下降沿
            break
    
    # 输出结果
    if end_position < 0:
        end_position = 0
    
    return end_position

def findTheRisingEdge(wave_data, clip_start_pos, clip_end_pos):
    #
    #  寻找上升沿
    #

    # 参数设置
    threshold_ratio = 0.10  # 阈值比例 (RMS的10%)
    
    # 计算幅度阈值
    wav_clip = wave_data[clip_start_pos:clip_end_pos]
    rms_value = np.sqrt(np.mean(np.abs(wav_clip)**2))
    threshold = rms_value * threshold_ratio
    # 滑动窗口检测
    window_size = 100  # 窗口采样点数
    num_windows = clip_start_pos // (4*window_size)  # 搜索1/4长度
    sigwins_count = 0
    start_position = -1
    i = 0
    for i in range(num_windows):
        start_idx = clip_start_pos + i * window_size
        end_idx = clip_start_pos + (i + 1) * window_size
        window = wave_data[start_idx:end_idx]
        
        # 计算窗口RMS
        window_rms = np.sqrt(np.mean(np.abs(window)**2))
        
        # 判断是否为信号窗口
        if window_rms > threshold:
            start_position = start_idx
            break
        else:
            sigwins_count += 1

    if start_position < 0:# 未能找到起始位置
        start_position = clip_start_pos # + (i+10)*window_size

    return start_position

def findTheStartPos(wave_data, clip_start_pos, clip_end_pos):
    """
    求最终的起始位置
    """
    # 参数设置
    threshold_ratio = 0.43  # 阈值比例 (RMS的10%)
    
    # 计算幅度阈值
    wav_clip = wave_data[clip_start_pos:clip_end_pos]
    rms_value = np.sqrt(np.mean(np.abs(wav_clip)**2))
    threshold = rms_value * threshold_ratio
    
    # 滑动窗口检测
    window_size = 100  # 窗口采样点数
    forwardoffset = (clip_end_pos-clip_start_pos)//10
    clip_start_pos_new = clip_start_pos + forwardoffset
    num_windows = clip_start_pos_new // window_size - 2  # 留有余量
    sigwins_count = 0
    start_position = -1
    
    for i in range(num_windows):
        start_idx = clip_start_pos_new - (i + 1) * window_size  # 向前滑动
        end_idx = clip_start_pos_new - i * window_size
        window = wave_data[start_idx:end_idx]
        
        # 计算窗口RMS
        window_rms = np.sqrt(np.mean(np.abs(window)**2))
        
        # 判断是否为信号窗口
        if window_rms > threshold:
            sigwins_count += 1
        else:
            start_position = start_idx  # 已发现上升沿
            break
    
    # 输出结果
    if start_position < 0:# 位置已经超前了
        start_position = 0
    # elif start_position==start_idx:    
        #start_position = findTheRisingEdge(wave_data, clip_start_pos, clip_end_pos)

    
    return start_position