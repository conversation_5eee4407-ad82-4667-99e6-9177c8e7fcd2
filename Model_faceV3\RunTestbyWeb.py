# =======================================================================================================================
#   Function    ：RunTestbyWeb.py
#   Description : 通过web方式测试模型
#                 分类模型测试: http://127.0.0.1:5000/test?fid=2
#                 匹配模型测试: http://127.0.0.1:5000/match?fid=2
# 
#   Parameter   : hdf5文件路径
#   Author      : Liuzhiguo
#   Date        : 2025-01-20
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from usrlib.usrlib import *
from usrlib.dataloader import *
from flask import Flask, render_template, request, redirect,session
from usrlib.VectorToVectorSim import *
from nets.arcface import Arcface
from usrlib.usrlib import compute_stft

#1.实例化得到对象
app = Flask(__name__)

clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件

def getdspath(testcase1):
    if testcase1==0:
        dataset_name ='nb-gendataset-S1.hdf5'
        datasettype = 'test' # train, test
        subdir = 'gendataset' # base,noised,multipath
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)
    elif testcase1==1:
        dataset_name ='nb-test433-odoor-S1.hdf5'
        datasettype = 'test' # train, test
        subdir = 'sam' # base,noised,multipath
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)    
    elif testcase1==2:
        dataset_name ='fchanscan-S1.hdf5'
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\test\\sam' 
    elif testcase1==3:
        dataset_name ='nb-dataset-val-S1.hdf5'
        datasettype = 'train' # train, test
        subdir = 'base' # base,noised,multipath
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)              
    elif testcase1==4:
        dataset_name ='nb-dataset-val-dbw-S1.hdf5'
        datasettype = 'train' # train, test
        subdir = 'augment' # base,noised,multipath
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir) 
    elif testcase1==5:
        dataset_name ='nb-dataset-val-dfc-S1.hdf5'
        datasettype = 'train' # train, test
        subdir = 'augment' # base,noised,multipath
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir) 
    elif testcase1==6:
        dataset_name ='nb-dataset-val-dpos-S1.hdf5'
        datasettype = 'train' # train, test
        subdir = 'augment' # base,noised,multipath
        predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir) 
    
    return predict_dataset_dir, dataset_name
    

#2.注册路由，写视图函数
@app.route('/')  # 根路径
def index():
    return 'hello world<BR>'

#http://127.0.0.1:5000/match?fid=2
@app.route('/match',methods=['GET','POST'])
def runmatch():
    modeltype = 1 #模型类别 0:分类 1:角向量
    dataset_file = ""
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:', cls_count)

    testcase1 = 0
    if request.method == 'GET':
        fid = request.args.get('fid',type=int,default=0)
        testcase1 = fid
    else:
        fid = request.form.get('fid',type=int,default=0)
        testcase1 = fid

    #1. 路径及模型参数
    predict_dataset_dir, dataset_name = getdspath(testcase1)
    dataset_file = os.path.join(predict_dataset_dir, dataset_name)
    model_path = "logs/Mtype1-ep087-loss0.000-val_loss0.391.pth" #05-27 匹配模型

    batch_size = 32 #256  

    #2. 读取数据文件
    t0 = time.time()
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    LenNorm         = 512*46
    rx_signal,classid_gt,class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file, LenNorm)

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  

    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))

    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            y = compute_stft(y)
            modelOutput = Model(y)
            output_all.append(modelOutput.softmax(1))
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    (vectors, clsids, clsnames, filepaths, curfs, curbw) = GetArcVectorDB()

    VectorSim = VectorSimilarity()
    if len(output_all.shape) == 1:
        avec = output_all.unsqueeze(0)
    else:
        avec = output_all

    vectors_tensor = torch.from_numpy(vectors)
    vectors_tensor = vectors_tensor.cuda()
    predicts = VectorSim.matching(avec, vectors_tensor) 
    #print(predicts)

    print("DB Labels List:\n{0}".format(clsids.reshape(1,-1)))

    sOut = '<html>'
    sOut += '<head><title>匹配结果显示</title></head>'
    sOut += '<body>'
    sOut += '<B><H2>AI模型匹配结果展示 </H2></B> <H3>[输入信号个数:{0}]</H3><BR>'.format(len(output_all))
    sOut += "True Labels:<BR>{0}<BR>".format(classid_gt.reshape(1,-1))
    #信号实际值

    # val_label = torch.Tensor(clsids).cuda()
    # val_label=val_label.squeeze()
    for i in range(3):
        Indexs = predicts['pred_label'][:,i]
        probs = predicts['pred_score'][:,i]
        probs = probs.cpu().numpy()
        Indexs = Indexs.cpu().numpy()
        sOut += "<BR>Predict Top {0} Labels:<BR> {1} <BR> Probs:<BR>{2}".format( i, clsids[Indexs].reshape(1,-1), probs) #预测标签
        if i == 0:
            val_predict = clsids[Indexs]


    val_score = np.where(val_predict == classid_gt, 1.0, 0.0)
    val_acc = np.mean(val_score)
    sOut += '<BR><B>predict_result: acc= {:.2f}%</B><BR>'.format(val_acc*100)
    sOut += '<BR><BR><B>读入文件:{0}</B>'.format(dataset_file)    
    sOut += "</body></html>"

    t4 = time.time()
    print("count score：{:.2f}s".format(t4 - t3))
    return sOut


#http://127.0.0.1:5000/classify?fid=2
@app.route('/classify',methods=['GET','POST'])
def runpredict():
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    #1. 路径及模型参数
    #model_path = "logs/ep064-loss0.095-val_loss0.211.pth"
    model_path = "logs/Mtype0-ep077-loss0.006-val_loss0.005.pth" #05-22定长度版本
    model_path = "logs/Mtype0-ep100-loss0.006-val_loss0.030.pth" #05-23变长度版本
    model_path = "logs/Mtype0-ep071-loss0.006-val_loss0.023.pth" #05-26变长度版本，增加数据量
    

    testcase1 = 0
    if request.method == 'GET':
        fid = request.args.get('fid',type=int,default=0)
        testcase1 = fid
    else:
        fid = request.form.get('fid',type=int,default=0)
        testcase1 = fid


    predict_dataset_dir, dataset_name = getdspath(testcase1)
    dataset_file = os.path.join(predict_dataset_dir, dataset_name)

    batch_size = 16 #256  

    if os.path.exists(dataset_file)==False:
        sOut = "<html><body>{0}:文件不存在</body></html>".format(dataset_file)
        return sOut
    #2. 读取数据文件
    t0 = time.time()
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    LenNorm         = 512*46
    rx_signal,classid_gt,class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file, LenNorm)

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  

    t1 = time.time()

    print('load data：{:.2f}s'.format(t1 - t0))
    

    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=0)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            y = compute_stft(y)
            modelOutput = Model(y) #, afs_value, abw_value
            output_all.append(modelOutput.softmax(dim=1))
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    sOut = '<html>'
    sOut += '<head><title>识别结果显示</title></head>'
    sOut += '<body>'
    sOut += '<B><H2>AI模型识别结果展示 </H2></B> <H3>[输入信号个数:{0}]</H3><BR>'.format(len(output_all))
    sOut += "True Labels:<BR>{0}<BR>".format(classid_gt.reshape(1,-1))

    val_predict = torch.argmax(output_all,dim=1)
    val_predict1 = torch.argsort(output_all,dim=1,descending=True)
    sOut += "The sorted predictions:<BR>{0}<BR>".format(val_predict.reshape(1,-1).cpu().numpy())

    val_label = torch.Tensor(classid_gt).cuda()
    val_label = val_label.squeeze()
    val_score = torch.where(val_predict == val_label, 1.0, 0.0)
    val_acc = torch.mean(val_score)
    val_acc = val_acc.cpu().numpy()
    t4 = time.time()
    print("count score：{:.2f}s".format(t4 - t3))
    sOut +='<BR><B>predict_result: acc= {:.2f}%</B><BR>'.format(val_acc*100)
    nCount = val_predict.shape[0]
    if nCount>10:
        nCount=10

    print('list the top predict results:')
    for i in np.arange(nCount):
        sOut +="predict val: {0} {1} validity: {2:.2f}%<BR>".format(val_predict[i].item(),cls_names[val_predict[i]],output_all[i,val_predict[i]]*100)
    sOut += '<BR><BR><B>读入文件:{0}</B>'.format(dataset_file)    
    sOut += "</body></html>"
    total_params_deep = "{:,}".format(sum(p.numel() for p in Model.parameters()))
    print(f"Model parameters: {total_params_deep}")
    return sOut

if __name__ == '__main__':
    # 运行app，默认运行在5000
    # 默认是host='127.0.0.1', port=5000端口
    app.run()
    #运行测试：访问这个地址 http://127.0.0.1:5000，即可看到hello，world了
