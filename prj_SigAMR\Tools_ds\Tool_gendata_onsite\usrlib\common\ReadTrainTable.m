function [Table_train]=ReadTrainTable(fname_label)
%  Function    ：ReadTrainTable
%  Description : 读取标注文件，并以数据表的方式返回
%
%  Parameter   : fname_label       -- 标注文件名称
%  Return      : Table_train       -- 数据表
%
%  Author      : Liuzhiguo
%  Date        : 2025-01-27
if exist(fname_label,"file")>0
    Table_train = readtable(fname_label,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    Table_train.lenClipPoints = Table_train.lenPoints;
    Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", fname_label);
end