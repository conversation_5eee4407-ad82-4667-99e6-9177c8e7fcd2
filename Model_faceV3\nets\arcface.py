# =======================================================================================================================
#   Function    ：arcface.py
#   Description : 模型总体集成类
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-1-20
# =======================================================================================================================
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Module, Parameter

from nets.iresnet import (iresnet18, iresnet34, iresnet50, iresnet100,
                          iresnet200)
from nets.mobilefacenet import get_mbf
from nets.mobilenet import get_mobilenet
from nets.DroneSigNet import DroneSigNet 
from nets.swin_transformer import swin_transformer_tiny
from nets.Module_attentions import SE_Row, SE_RowV1

class Arcface_Head(Module):
    def __init__(self, embedding_size=128, num_classes=10575, s=64., m=0.5):
        super(Arcface_Head, self).__init__()
        self.s = s
        self.m = m
        self.weight = Parameter(torch.FloatTensor(num_classes, embedding_size))
        nn.init.xavier_uniform_(self.weight)

        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, input, label):
        # 存在问题:Softmax loss 的改造，相当于在分子分母上同时减小一个值，破坏了 Softmax 总和为1的特性
        # 角度距离
        cosine  = F.linear(input, F.normalize(self.weight, dim=1)) # (C, N) 类内每个特征维度归一化
        sine    = torch.sqrt((1.0 - torch.pow(cosine, 2)).clamp(0, 1))
        phi     = cosine * self.cos_m - sine * self.sin_m
        phi     = torch.where(cosine.float() > self.th, phi.float(), cosine.float() - self.mm)

        one_hot = torch.zeros(cosine.size()).type_as(phi).long()
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        output  = (one_hot * phi) + ((1.0 - one_hot) * cosine) 
        output  *= self.s
        return output

class Arcface(nn.Module):
    def __init__(self, num_classes=None, backbone="mobilefacenet", pretrained=False, mode="train", ModelType = 0):
        super(Arcface, self).__init__()
        self.in_ch = 8
        #self.weights = torch.tensor([0.5, 0.4, 0.1], requires_grad=True)
        self.weights = [0.2, 0.4, 0.3, 0.1]
        #self.max_pooling_layer = nn.MaxPool2d(kernel_size=(self.in_ch, 1), stride=(self.in_ch, 1))
        #self.max_pooling_layer = nn.AdaptiveMaxPool2d([128, 116])
        self.conv_pooling_layer = nn.Conv2d(1, 1, kernel_size=[8,1], stride=[8, 1], bias=False)
        self.conv_emb = nn.Conv2d(self.in_ch, self.in_ch, kernel_size=[3,3], padding=1) #向量嵌入
        #self.se_row_layer = SE_Row(in_chnls=3, ratio=1)  # 平均值加权
        self.se_row_layer = SE_RowV1(in_chnls=3, ratio=1) # 平均值和最大值加权
        
        if backbone=="mobilefacenet":
            embedding_size  = 128
            s               = 32
            self.arcface    = get_mbf(embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="mobilenetv1":
            embedding_size  = 512
            s               = 64
            self.arcface    = get_mobilenet(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet18":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet18(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet34":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet34(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet50":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet50(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet100":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet100(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet200":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet200(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)
        elif backbone=="DroneSigNet":
            if ModelType==0:
                embedding_size  = 512#96#512
            else:
                embedding_size  = 96#96#512
                
            s               = 64
            self.arcface    = DroneSigNet(embedding_size=embedding_size) 
        elif backbone=="swin_transformer_tiny":
            embedding_size  = 512
            s               = 64
            self.arcface    = swin_transformer_tiny(input_shape=[1024, 45],pretrained=False,num_classes=embedding_size)           
        else:
            raise ValueError('Unsupported backbone - `{}`, Use mobilefacenet, mobilenetv1.'.format(backbone))

        self.mode = mode
        #if mode == "train":#匹配头
        self.ArcHead = Arcface_Head(embedding_size=embedding_size, num_classes=num_classes, s=s)
        #self.layer_norm = nn.LayerNorm(embedding_size)
        #分类头 (分类用)
        self.ClsHead = nn.Sequential(
            nn.Dropout(p=0),#0.2
            nn.Linear(embedding_size, num_classes),
        )

        self.HeaderType = ModelType

    def preProcess(self, rx_signals):
        # 区别: 权重为预设值，不进行修改
        #  1. 计算stft 
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,center=False,normalized=True) #转换到频域  
        z = torch.fft.fftshift(z, dim=1) #[batch, fft_size, timeframe]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]

        # 缩小化抽取
        #z_sub = F.interpolate(z,size=[nCarriers//8, nTimesym],mode='bilinear')
        z_sub = self.conv_pooling_layer(z)

        # 2 在时间维度，数据分为4块，分别设置不同权重
        chunks = torch.chunk(z, 4, dim=-1)
        #信号分成3段，分别赋予不同权重
        # 为每块赋予权重
        weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 重新组合
        z = torch.cat(weighted_chunks, dim=-1)

        nbatch, _ , nCarriers, nTimesym = z.shape
        n_subcount = math.floor(N_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym))
        z = self.conv_emb(z)             # word embedding
        z = torch.cat((z, z_sub), dim=1) # 增加一个全局维度

        return z

    def preProcessExt(self, rx_signals):
        #
        # 区别: 权重为注意力机制学习得到的值，利用timeframe方向注意力机制实现
        #
        # 1 交换维度， stft已经在输入数据前的预处理部分实现
        # 输入格式为 [batch, fft_size, timeframe, abs(I/Q)] 
        nbatch , nCarriers, nTimesym, _ = rx_signals.shape
        z = rx_signals.permute(0, 3, 1, 2)       # 输出格式为 [batch, abs(I/Q), fft_size, timeframe]


        # 2 缩小化抽取
        z_reduced = self.conv_pooling_layer(z)   # size=[batch, abs(I/Q), fft_size//8, nTimesym]

        # 3 在时间维度，数据分为3块，分别设置不同权重
        # 3.1 信号分成3段，分别赋予不同权重
        chunks = torch.chunk(z, 3, dim=-1)       # [batch, abs(I/Q), fft_size, timeframe/3]
        chunks = torch.cat(chunks, dim=1)        # 第1个维度拼接 [batch, 3, fft_size, timeframe/3]
        
        # 3.2 为每块应用注意力机制
        weighted_chunks = self.se_row_layer(chunks)               # 统一运用 注意力
        weighted_chunks = torch.chunk(weighted_chunks, 3, dim=1)  # 拆分
        #weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 3.3 重新组合
        z = torch.cat(weighted_chunks, dim=-1)
        
        # 4 重新分解为多个channel
        #n_subcount = np.floor(nCarriers/self.in_ch)
        #n_subcount = n_subcount.numel()
        #n_subcount = n_subcount.astype(np.int32)
        n_fft = 1024
        n_subcount = math.floor(n_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym)) # reshape为多个channel，主要目的为：节省计算量

        z = self.conv_emb(z)             # word embedding

        # 5 加入全局信息
        z = torch.cat((z, z_reduced), dim=1)                            # 防止丢失全局信息，增加一个全局维度channel(抽样缩小版本)
        # z = z + z_reduced # 论文Resource Efficient Perception for Vision Systems 表述， 全局块特征向量与其它块的特征融合采用相加的方式效果更佳
        # 个人测试效果一般
        return z
    
    def preProcessBig(self, rx_signals):
        #
        # 区别: 大图模式，1024不切分多个channel
        #
        # 1. 计算stft
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]
        z_last4col = z[:,:,:,-12:]
        z = torch.cat((z,z_last4col),dim=3)# torch.Size([64, 1024, 128, 1])

        return z

    def forward(self, x, y = None, mode = "predict"):
        # x：信号 格式为： [batch, fft_size, timeframe, 1] # 输入格式最后一维为 abs(I/Q)
        # y：标签值，只有在匹配模型训练时才会用到
        x = self.preProcessExt(x) # （batch, 1024, 45, 1）#basic improved
        
        x = self.arcface(x) # 网络处理
        x = x.view(x.size()[0], -1)

        if self.HeaderType == 0:# 分类模型
            x = F.normalize(x, dim=1)
            x = self.ClsHead(x) # [batch self.nclass] 
        else:                   # 匹配模型
            if mode == "predict": # 推理时用
                return x
            else:                 # 训练时用
                x = F.normalize(x, dim=1)
                #x = self.layer_norm(x)
                x = self.ArcHead(x, y) # 向量角距离递归，所以训练时也需要输入 y
        return x
