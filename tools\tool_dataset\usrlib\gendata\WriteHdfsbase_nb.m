function []=WriteHdfsbase_nb(fname_label, outpath, clip_ms, outname)
%  Function    ：WriteHdfsbase_nb
%  Description : 写入测试数据(采集得到) NB数据
%  Parameter   : fname_label       -- 标签文件名称
%                outpath           -- 输出hdfs文件路径
%                outname           -- 输出hdfs文件名称
%                bMultifiles       -- 多文件格式
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-27

% 1. 初始化命令
[signalDB_dir,~,~] = fileparts(fname_label);
% fname_class = fullfile(signalDB_dir,"class_def.txt");% 类别文件
InitMyParams;
fname_class = myconfig.fpath_classdef;
%fname_class = fullfile('E:\ftproot\signalDB',"class_def.txt");% 类别文件

if exist("clip_ms","var")<=0
    clip_ms = 3;%1.73;%nb433
end

if  clip_ms > 10
    clip_ms = 10;
end

% 2. 文件读入
% 2.1 文件目录读取

if exist(fname_label,"file")>0
    Table_train = readtable(fname_label,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    Table_train.lenClipPoints = Table_train.lenPoints;

    msp = split(string(Table_train.filename),'\',1);
    Table_train.clsname = msp(1,:)';

    Table_train.recindex = Table_train.lenPoints;%暂时赋值
    %Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", fname_label);
end

if exist(fname_class,"file")>0
    Table_cls = readtable(fname_class,'Format','%d %s %f %f');
    Table_cls.Properties.VariableNames = ["clsid","clsname","BW","Tms"];
else
    error("文件：%s 不存在！", fname_class);
end

indexPointer = 0;
cls_size = '';

%clip为相同长度,为了识别需要
curclsname = ' ';
lenclip = 0;
recindex = 1;
maxlen = 61.44e3*clip_ms;
for iRow = 1 : height(Table_train)
    clsname = Table_train(iRow,7).clsname;
    if clsname ~= curclsname %下一个类，重新计算长度
        recindex = 1;
        lenclip = Table_train(iRow,5).lenPoints;
        curclsname = clsname;       
    end
    Table_train(iRow,8).recindex = recindex;
    recindex = recindex+1;
    if lenclip > maxlen %大于clip_ms，最多取clip_ms
        lenclip = maxlen;
    end

    if Table_train(iRow,6).lenClipPoints > lenclip
        Table_train(iRow,6).lenClipPoints = lenclip;
    end
end
%Table_train = sortrows(Table_train,'clsname');%'lenClipPoints');
[~,fname,~] = fileparts(fname_label);
sOutfilename_list =  fullfile(outpath,strcat(fname,'_list.txt'));
%2.2 训练数据文件读取
for iRow = 1 : height(Table_train)
    sigfsubname  = char(Table_train(iRow,1).filename);%文件名称
    [parentFolder, subname, subext] = fileparts(sigfsubname);
    if isempty(parentFolder) %linux system
        sigfsubname = replace(sigfsubname,'\','/');
        [parentFolder, subname, subext] = fileparts(sigfsubname);
    end
    clsname = parentFolder;
    sfilename = strcat(subname, subext);

    cls_id = Table_cls(strcmp(Table_cls.clsname,clsname),:).clsid;% 读取信号所属类别
    cls_BW = Table_cls(strcmp(Table_cls.clsname,clsname),:).BW;
    cls_Tms = Table_cls(strcmp(Table_cls.clsname,clsname),:).Tms;%持续时间 ms

    if isempty(cls_id) == true
        error("cls_id为空，请检查classdef文件:%s\n", fname_class);
    end

    sigfname         = fullfile(signalDB_dir, sigfsubname);    %文件路径
    nStartpos_t      = Table_train(iRow,2).startpos;      %起始点
    nEndpos_t        = Table_train(iRow,3).endpos;      %结束点
    fc               = Table_train(iRow,4).fc;      %中心频率
    lenClipPoints    = Table_train(iRow,6).lenClipPoints;%clip长度
    

    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
    lenPointsClass = floor(wb_fs*cls_Tms/1e3);%这类的持续点数
    if lenClipPoints > lenPointsClass %不能超过该类的点数
        lenClipPoints = lenPointsClass;
    end

    %showWBSig(wb_signal, wb_fc, wb_fs);
    %wb_rxSig_clip = wb_signal(nStartpos_t:nEndpos_t); %数据切片
    wb_rxSig_clip = wb_signal(nStartpos_t:nStartpos_t+lenClipPoints-1); %数据切片

    if mod(length(wb_rxSig_clip),2)>0
        wb_rxSig_clip = wb_rxSig_clip(1:end-1);
    end
    nb_fc = fc;
    nb_bw = cls_BW;      % 从分类中读取窄带带宽
    % (1) 方式1: 固定长度
    % nb_fs   = 2*nb_bw;   % 窄带信号的采样率
    % nb_len_set = 6e4;
    % [nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs, nb_len_set);%信道化后数据
    % (2) 方式2: 固定采样率
    nb_fs   = 4e6;
    [ret] = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs);
    if ret ~= 1 %带宽参数无效
        warning("无效带宽输入：%s\n",sigfname);
        continue;
    end
    [nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs);%信道化后数据

    %showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号
    len_clip_nb = length(nb_signal);
    if len_clip_nb==0
        warning("invalid len_clip_nb=%d\n", len_clip_nb);
        continue;
    end
    len_clip_wb = length(wb_rxSig_clip);
    nb_fs_effct = len_clip_nb*(wb_fs/len_clip_wb);%有效采样率
    % 信号采样率
    fprintf("文件:%s 截取窄带化后信号: 数据点数为%d，带宽:%.2f kHz,持续时间为%.2f ms\n", sigfsubname, len_clip_nb, nb_bw/1e3, length(wb_rxSig_clip)*1e3/wb_fs);

    [cls_size, indexPointer] = getFileSizeClass(len_clip_nb, cls_size, indexPointer);
    if exist("outname","var")>0 % 输出方式
        if outname ~= ""
            fname_dataset = strcat(outpath,outname,'-',cls_size,'.hdf5');
            WrTrainSig(fname_dataset, nb_signal, cls_id, sigfsubname, fc, nb_fs_effct, nb_bw, indexPointer);  % 生成数据集，hdf文件格式
        end
    else
        subpath = strcat(outpath, clsname);%合成目的路径
        curfileindex     = Table_train(iRow,8).recindex;
        fname_dataset = strcat(subpath,'\',subname,'-',int2str(curfileindex),'-',cls_size,'.wav');
        if exist(subpath,"dir")<=0
            mkdir(subpath)
        end
        indexPointer = 1;%每次只生成1条
        scomment = sprintf("nb_bw=%d,fc=%d",nb_bw,fc);%写入audio文件
        audiowrite(fname_dataset, [real(nb_signal) imag(nb_signal)],floor(nb_fs_effct),'BitsPerSample',32,'Title',sigfsubname,'Artist',string(cls_id),'Comment',scomment);
    end

    current_dir = pwd;%转换到绝对路径
    absolute_path = fullfile(current_dir, fname_dataset);
    strline = strcat(int2str(cls_id),";",absolute_path);
    if iRow==1
        writelines(strline, sOutfilename_list,'WriteMode','overwrite');
    else
        writelines(strline, sOutfilename_list,'WriteMode','append');
    end
end

%其它格式试验
        % filename = 'handel.wav';
        % scomment = sprintf("nb_bw=%d,fc=%d",nb_bw,fc);
        % audiowrite(filename, [real(nb_signal) imag(nb_signal)],floor(nb_fs_effct),'BitsPerSample',32,'Title',sigfsubname,'Artist',string(cls_id),'Comment',scomment),...
        % 'nb_bw',nb_bw,'cls_id',cls_id,'sigfsubname',sigfsubname,'fc', fc);
        %输入 应与以下值之一匹配:'BitsPerSample', 'BitRate', 'Quality', 'Title', 'Artist', 'Comment'
        % 用wav文件附加参数的方式好像行不通

% varPoints = Table_train(:,3).endpos-Table_train(:,2).startpos;
% varNames  = Table_train(:,1).filename;
% selpart = {varNames; varPoints};
%3 读取hdf文件信息（测试用）
DispDatasetRecByChart(fname_dataset,1,"原始");
end


function [clsName,indexout]=getFileSizeClass(len,prev_clsName,index)
%
%
%分割成5个区间:[0,5e4],[5e4,10e4],[10e4,20e4],[20e4,40e4],[40e4,60e4],[60e4, -]
%             对应:S1-S5,S6
%
if len < 6.1e4
    clsName = 'S1';
elseif len < 10e4
    clsName = 'S2';
elseif len < 20e4
    clsName = 'S3';
elseif len < 40e4
    clsName = 'S4';
elseif len < 60e4
    clsName = 'S5';
else
    clsName = 'S6';
end
%新文件，更换index
if index==0%第1个，没有prev_clsName
    prev_clsName = clsName;
end
if prev_clsName==clsName
    indexout = index+1;
else
    indexout = 1;
end
end
