                    数据集生成及相关工具说明
1 DivOriginalData.m           ---   数据集划分，将所有分类目录(如02-LabeledData/2025-05-26/nb_433M/)划分数据集txt，输入为：每个Train_records.txt，输出为：相应目录下的augment.txt、noised.txt、base.txt，方便生成数据集
    DivEffectData.m             ---  辅助整理数据集用，将将所有分类目录中不在Train_records.txt中的bvsp,meta文件分离出来，放到子文件夹下。（看情况使用）
2 cmdGenDatasetPar.m     ---   并行方式生成数据集，包括基本数据集、增强数据集，如输入为02-LabeledData/2025-05-26下数据集划分augment.txt、noised.txt、base.txt文件，输出为03-GenDataset/2025-05-26的base,base_augment,base_noised,augment,noised
3 cmdGenDataset_lbls.m   ---  合并txt标注文件,如输入03-GenDataset/2025-05-26/base中的数据集txt，输出生成base下Train_ds_gen.txt、Val_ds_gen.txt、Test_ds_gen.txt
4 cmdGenLSW_pair.m        ---  生成匹配模型测试文件，如输入为03-GenDataset/2025-05-26/base/Test_ds_gen.txt，输出为.\output\lsw_pair.txt
说明：配置路径等参数在InitMyParams中定义；引入路径在IncludedPaths中定义；工具支持linux/windows双系统
          生成数据集可以按步骤1-4依次操作，注意相关路径定义。
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
5 cmdRuncapture.m          ---  matlab采集程序及实时测试模型工具， (需要连接网口采集板，并在Model工程中开启RunTestbyWeb.py)
6 TestModelbyWeb.m      ---  手动测试模型工具， (需要连接网口采集板，并在Model工程中开启RunTestbyWeb.py)
7 cmdDemodGfsk.m          ---  解调GFSK程序，可解调gfsk/lora采集文件，方便查找问题用
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
8 cmdGenDataset.m        ---  数据集生成工具，用于单个数据集生成单个分类的数据集，如基于原始数据02-LabeledData/nb_433M/Train_records.txt，生成03-GenDataset/2025-05-26/base/nb_433M中的wav文件、列表文件wfile_list.txt
9 cmdGenAugData.m      ---  数据增强工具，用于生成单个分类数据的增强，如基于原始数据02-LabeledData/nb_433M/Train_records.txt，生成噪声数据集、数据增强集
----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
常用函数说明：
(1)图形化查看wav文件 (时序图和频谱图)
DispDatasetRecByChart('E:\lz_signaldB\datafiles\03-GenDataset\2025-06-30\base\nb_frsky_td_fsk\CJ0_1M_950-6.wav',1,"")
(2)图形化查看hdf文件 (时序图和频谱图)
DispDatasetRecByChart(".\outdataset\train\base\nb-dataset-val-S1.hdf5",1,"原始")
(3)图形化查看dat或文件 (时序图和频谱图)
showSingleWave('.\sam_evo\433\ch9364Capture433-5.dat')

                                                                                                                                        刘智国
                                                                                                                                  2025.05.30