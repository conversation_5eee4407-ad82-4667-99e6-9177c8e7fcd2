import torch
from typing import Callable, List, Optional, Union
import torch.nn.functional as F

class VectorSimilarity():
    def __init__(self,similarity_fn: Union[str, Callable] = 'correlation',
                 topk: int = -1) -> None:
        self.similarity = similarity_fn
        self.topk = topk
    
    @property
    def similarity_fn(self):
        """Returns a function that calculates the similarity."""
        # If self.similarity_way is callable, return it directly
        if isinstance(self.similarity, Callable):
            return self.similarity

        if self.similarity == 'cosine_similarity':
            # a is a tensor with shape (N, C)
            # b is a tensor with shape (M, C)
            # "cosine_similarity" will get the matrix of similarity
            # with shape (N, M).
            # The higher the score is, the more similar is
            # 因为余弦值的范围是 [-1,+1] ，相似度计算时一般需要把值归一化到 [0,1]
            # ，一般通过如下方式：sim=0.5*(1+cosa)
            return lambda a, b: 0.5*(1+torch.cosine_similarity(
                a.unsqueeze(1), b.unsqueeze(0), dim=-1))
        elif self.similarity == 'euclidean_distance':
            # a is a tensor with shape (N, C)
            # b is a tensor with shape (M, C)
            # "euclidean_distance" will get the matrix of similarity
            # with shape (N, M).
            # The higher the score is, the more similar is
            # 扩展 A 和 B 的维度以进行广播
            # a (N, C)-->(N, 1, C)
            # b (M, C)-->(1, M, C)
            # 计算差值的平方
            # 沿最后一个维度求和
            # 取平方根得到欧氏距离
            # 与cos相似度的关系: euc = sqrt(2-2*cosa) 
            return lambda a, b:  torch.sqrt(torch.sum((F.normalize(a).unsqueeze(1)-F.normalize(b).unsqueeze(0))**2, dim=-1))
            # return lambda a, b: torch.cosine_similarity(
            #     a.unsqueeze(1), b.unsqueeze(0), dim=-1)
        elif self.similarity == 'corr_distance':    
            # 皮尔逊相关系数衡量的是两个变量之间的线性相关程度，其取值范围在 -1 到 1 之间，
            # 值越接近 1 表示正线性相关越强，越接近 -1 表示负线性相关越强，接近 0 表示线性相关性较弱。
            #
            # 计算均值
            # mean_a = torch.mean(a)
            # mean_b = torch.mean(b)

            # # 计算协方差
            # cov_ab = torch.sum((a - mean_a) * (b - mean_b)) / (len(a) - 1)

            # # 计算标准差
            # std_a = torch.std(a, unbiased=True)
            # std_b = torch.std(b, unbiased=True)

            # # 计算皮尔逊相关系数
            # pearson_corr = cov_ab / (std_a * std_b)
            return lambda a, b:  torch.sum((a.unsqueeze(1) - torch.mean(a.unsqueeze(1),dim=-1)) * (b.unsqueeze(0) - torch.mean(b.unsqueeze(0),dim=-1)).T) / \
                                           ((a.shape[1] - 1) * torch.std(a.unsqueeze(1) ,dim=-1 , unbiased=True)* \
                                            torch.std(b.unsqueeze(0), dim=-1, unbiased=True).T)
        
        elif self.similarity == 'correlation':
            return lambda a, b:  self.cal_correlation(a, b) 
        else:
            raise RuntimeError(f'Invalid function "{self.similarity_fn}".')
        
    def cal_correlation(self, a, b):
        # 初始化一个空的结果张量
        correlations = torch.zeros(a.shape[0], b.shape[0])

        # 遍历 a 的每一行
        for i in range(a.shape[0]):
            # 遍历 b 的每一行
            for j in range(b.shape[0]):
                # 检查是否存在nan值
                if torch.isnan(a[i]).any() or torch.isnan(b[j]).any():
                    # 如果存在nan值，使用不包含nan的元素计算相关性
                    valid_indices_a = ~torch.isnan(a[i])
                    valid_indices_b = ~torch.isnan(b[j])
                    valid_indices = valid_indices_a & valid_indices_b
                    
                    if valid_indices.sum() > 0:  # 确保有足够的有效数据点
                        # 使用有效数据计算相关性
                        mean_a = torch.mean(a[i][valid_indices])
                        mean_b = torch.mean(b[j][valid_indices])
                        cov_ab = torch.sum((a[i][valid_indices] - mean_a) * (b[j][valid_indices] - mean_b)) / (valid_indices.sum() - 1)
                        std_a = torch.std(a[i][valid_indices], unbiased=True)
                        std_b = torch.std(b[j][valid_indices], unbiased=True)
                        
                        if std_a > 0 and std_b > 0:  # 避免除以零
                            pearson_corr = cov_ab / (std_a * std_b)
                        else:
                            pearson_corr = 0.0
                    else:
                        pearson_corr = 0.0  # 如果没有有效数据，设置为默认值
                else:
                    # 原始计算方法
                    mean_a = torch.mean(a[i])
                    mean_b = torch.mean(b[j])
                    cov_ab = torch.sum((a[i] - mean_a) * (b[j] - mean_b)) / (a.shape[1] - 1)
                    std_a = torch.std(a[i], unbiased=True)
                    std_b = torch.std(b[j], unbiased=True)
                    
                    if std_a > 0 and std_b > 0:  # 避免除以零
                        pearson_corr = cov_ab / (std_a * std_b)
                    else:
                        pearson_corr = 0.0
                        
                correlations[i, j] = pearson_corr
        return correlations

    def matching(self, inputs: torch.Tensor, prototype_vecs: torch.Tensor):
        """Compare the prototype and calculate the similarity.

        Args:
            inputs (torch.Tensor): The input tensor with shape (N, C).
        Returns:
            dict: a dictionary of score and prediction label based on fn.
        """
        sim = self.similarity_fn(inputs, prototype_vecs)
        descending = False
        if self.similarity == 'cosine_similarity':
            descending = True
        elif self.similarity == 'euclidean_distance':
            descending = False
        elif self.similarity == 'correlation':
            descending = True

        sorted_sim, indices = torch.sort(sim, descending=descending, dim=-1)
        predictions = dict(
            score=sim, pred_label=indices, pred_score=sorted_sim)
        return predictions    