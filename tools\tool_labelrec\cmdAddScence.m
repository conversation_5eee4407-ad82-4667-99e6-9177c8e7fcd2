%
%    function   : cmdAddScence
%    Description: 信号添加场景工具，如2730.bvsp 添加场景后--> CJ0_1M_2730.bvsp
%    CJ 表示 场景, 1M表示1米距离
%    author     : 刘智国
%       
%

IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数

% 定义文件夹路径
folderPath = "E:\software\nxtool\packages\nb433\CJ0~1M";
% 定义场景名称
scenceName = 'CJ0~1M';

% 替换过程
extName    = '*.bvsp';
ChangeFolderFiles(folderPath, scenceName, extName);
extName    = '*.meta';
ChangeFolderFiles(folderPath, scenceName, extName);

function []=ChangeFolderFiles(folderPath, scenceName, extName)
%  Function    ：ChangeFolderFiles
%  Description : 修改文件夹下的名称前面加场景标记
%  Parameter   : folderPath       -- 路径名称
%                scenceName       -- 场景名称
%                extName          -- 扩展名
%                
%  Return      :
%
%  Author      : Liuzhiguo
%  Date        : 2025-03-17

% 检查文件夹是否存在
if ~exist(folderPath, 'dir')
    error('指定的文件夹不存在。');
end

% 获取文件夹下所有扩展名为 bvsp 的文件
filePattern = fullfile(folderPath, extName);
fileList = dir(filePattern);

% 遍历每个文件
for i = 1:length(fileList)
    % 获取原文件名和路径
    originalFileName = fileList(i).name;
    originalFilePath = fullfile(folderPath, originalFileName);
    
    % 生成新的文件名
    if contains(originalFileName,scenceName) %如已包含场景前缀，则
        continue;
    end

    newFileName = [scenceName, '~', originalFileName];
    newFilePath = fullfile(folderPath, newFileName);
    
    % 重命名文件
    movefile(originalFilePath, newFilePath);
end
disp('文件重命名完成!');
end