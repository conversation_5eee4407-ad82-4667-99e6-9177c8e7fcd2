import os
import numpy as np
import torch
import torch.utils.data as data
import h5py
import soundfile


def LoadHdfsDataset(dataset_file, LenNorm):
    '''
    加载单个hdfs数据集
    '''
    if os.path.exists(dataset_file)==False:
        print("{0}:文件不存在".format(dataset_file))
        return
    
    f = h5py.File(dataset_file, 'r')
    rx_signals  = f['rx_signal'][:]
    class_ids   = f['class_id'][:]
    class_names = f['class_name'][:]
    fs_values = f['fs'][:]
    bw_values = f['bw'][:]
    f.close()

    # 裁剪数据集的size
    N_row =  rx_signals.shape[0]
    N_col =  rx_signals.shape[1]
    if N_col<LenNorm:
        rx_signals = np.pad(rx_signals,((0,0),(0,LenNorm-N_col),(0,0)), 'constant')
    else:
        rx_signals = np.resize(rx_signals,(N_row, <PERSON>Norm, 2))
    
    # print('rx_signal:', rx_signals.shape, rx_signals.dtype)
    # print('class_id:', class_ids.shape, class_ids.dtype)
    return  rx_signals, class_ids, class_names, fs_values, bw_values


class SigNbDataset(data.Dataset):
    def __init__(self, lines, LenNorm):
        self.lines       = lines
        self.LenNorm     = LenNorm

    def __len__(self):
        return len(self.lines)

    def __getitem__(self, index):
        annotation_path = self.lines[index].split(';')[1].split()[0]
        label               = int(self.lines[index].split(';')[0])
        
        #rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(annotation_path)
        rx_signals, fs_value = soundfile.read(annotation_path, dtype='float32')#(60000, 2)
        N_row =  rx_signals.shape[0]
        if N_row<self.LenNorm:
            rx_signals = np.pad(rx_signals,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signals = np.resize(rx_signals,(self.LenNorm, 2))

        #rx_signal = rx_signal[0:self.LenNorm,:]
        if rx_signals.shape[0]!=self.LenNorm or rx_signals.shape[1]!=2 or len(rx_signals.shape)!=2: #(1, 59392, 2)
             print(fs_value)

        # if label != class_id:
        #     print(fs_value)    
        return rx_signals, label

def SigNbDataset_collate(batch):
    rx_signals  = []
    targets = []
    for rx_signal, label in batch:
        rx_signals.append(rx_signal)
        targets.append(label)

    rx_signals  = torch.tensor(np.array(rx_signals))#torch.from_numpy(np.array(rx_signals))#.type(torch.FloatTensor)
    targets = torch.tensor(np.array(targets)).long()#torch.from_numpy(np.array(targets)).long()

    #targets = torch.cat(targets, 0)
    return rx_signals, targets
    
def dataset_collate(batch):
    images  = []
    targets = []
    for image, y in batch:
        images.append(image)
        targets.append(y)
    images  = torch.from_numpy(np.array(images)).type(torch.FloatTensor)
    targets = torch.from_numpy(np.array(targets)).long()
    return images, targets

#labeled signal wild
class LSWDataset(data.Dataset):
    def __init__(self, lines_lsw, LenNorm):
        self.lines_lsw = lines_lsw 
        self.LenNorm     = LenNorm

    def __getitem__(self, index):
        line    = self.lines_lsw[index].replace("\n", "")#移除换行符
        (id1, path_1, id2, path_2) = line.split(';')
        issame = (id1==id2)
        #rx_signal1, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_1)
        #rx_signal2, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_2)
        rx_signal1, fs_value = soundfile.read(path_1, dtype='float32')
        rx_signal2, fs_value = soundfile.read(path_2, dtype='float32')
        #LenNorm = 1024*58+512
        N_row =  rx_signal1.shape[0]
        if N_row<self.LenNorm:
            rx_signal1 = np.pad(rx_signal1,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signal1 = np.resize(rx_signal1,(self.LenNorm, 2))
        
        N_row =  rx_signal2.shape[0]
        if N_row<self.LenNorm:
            rx_signal2 = np.pad(rx_signal2,((0,self.LenNorm-N_row),(0,0)), 'constant')
        else:
            rx_signal2 = np.resize(rx_signal2,(self.LenNorm, 2))

        # rx_signal1 = rx_signal1[0:self.LenNorm,:]
        # rx_signal2 = rx_signal2[0:self.LenNorm,:]

        return rx_signal1, rx_signal2, issame

    def __len__(self):
        return len(self.lines_lsw)