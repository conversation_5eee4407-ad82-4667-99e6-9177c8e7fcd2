function [binData_out] = decode_PN9(binData_in)
%  Function    ：decode_PN9
%  Description : 解调PN序列，The PN9 sequence is reset to all 1’s.
%                参考文献《Ti CC2500 Datasheet》, P29, 15.1 Data Whitening
%                《Data Whitening and Random TX Mode, Design Note DN509》
%
%  Parameter   : binData_in     -- 输入数据    (0/1 序列)
%                binData_out    -- 解调PN序列后的数据 (0/1 序列)
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-20
nRound = floor(length(binData_in)/8);
outbyData = zeros(nRound,1);
PnSeq = hex2dec('1FF');
for i=1:nRound
    startPos = (i-1)*8+1;
    endPos = i*8;
    % 从数据取bin 8 bit
    byValue = bit2int(binData_in(startPos:endPos),8);
    % 从pin序列取LSB 8 bit
    PnSeq_LSB = bitand(PnSeq, hex2dec('FF'));
    outbyData(i) = bitxor(PnSeq_LSB, byValue);
    %fprintf('ID=%d PN9=', i);disp(int2bit(PnSeq,9)');

    % PN序列重新移位，每次8bit
    for j=1:8
        bit5 = bitget(PnSeq,6);
        bit0 = bitget(PnSeq,1);%从0开始
        bit9 = bitxor(bit0, bit5);
        PnSeq = bitshift(PnSeq,-1);   %右移1bit
        PnSeq = bitset(PnSeq,9,bit9); %设置第9bit
    end
end

binData_out = int2bit(outbyData, 8, true);%字节转换bit
n_len = length(outbyData);
binData_out = reshape(binData_out,[n_len*8,1]);
end