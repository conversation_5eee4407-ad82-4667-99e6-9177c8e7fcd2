function []=AutogenLblsByMeta(foldername, meta_subtype, clsname, duration_ms, forwardoffset)
%    function: AutogenLblsByMeta
%              根据meta文件生成标注文件
%              充分考虑了3种情况：(1)该频点上没有频谱数据，误报 用频点数据>3*平均功率滤除
%                               (2)同一条数据，标注2个频点情况，channelscanner的问题
%                               (3)该频点上有频谱数据,但该切片上没有数据
%
%    Params： foldername    -- meta文件路径
%             typename      -- 类型名称
%    return:
%             train_records.txt
%   examples：AutogenLblsByMeta('E:\software\nxtool\packages',"nb_433M")
%
%  Author      : Liuzhiguo
%  Date        : 2024-12-31
%
if exist('duration_ms','var')<=0
    duration_ms = 6;
end

% if timeds_ms > 6 %限制最大时长为6ms
%     timeds_ms = 6;
% end

if exist('forwardoffset','var')<=0
    offset_pos = 0;
    %offset_pos = 0;
else
    offset_pos = forwardoffset;
end

if exist(foldername,"dir")<=0
    warning('文件夹：%s不存在\n',foldername);
    return;
end

metafilelist = dir(fullfile(foldername,'*.meta'));
flblfile = fullfile(foldername,'Train_records.txt');
if exist(flblfile,"file")>0 %如果存在，则备份数据文件
    flblfile_backup = fullfile(foldername,'Train_records_backup.txt');
    movefile(flblfile, flblfile_backup);
end
fout = fopen(flblfile,"w");%覆盖方式写入
len = length(metafilelist);
for i = 1:len % 也可以采用parfor，但效果不明显
    filename_meta=metafilelist(i).name;        % meta file

    jsonStr = fileread(fullfile(foldername,filename_meta));
    jsonData = jsondecode(jsonStr);

    filename_bvsp = strrep(filename_meta,"meta","bvsp");
    filepath_bvsp = fullfile(foldername, filename_bvsp);
    % 1 判断bvsp文件是否存在
    if exist(filepath_bvsp,"file")<=0
        fprintf('meta文件对应的%s不存在\n',filepath_bvsp);
        continue;
    end
    % 2 加载bvsp文件
    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filepath_bvsp);
    nSig_len = length(wb_signal);
    [fftdata, nb_freqs]=getfftData(wb_signal,wb_fc,wb_fs);
    %[fftdata, nb_freqs]=getconstfftData(wb_signal,wb_fc,wb_fs);
    avg_fftpower = mean(fftdata);
    powerthreshold_fc = avg_fftpower*4;% 阈值功率

    % avg_sigpower = sqrt(mean(abs(wb_signal).^2));%RMS
    % powerthreshold_region = avg_sigpower*1.1;
    min_clippower=findpowerthredhold(wb_signal);
    powerthreshold_region = min_clippower*2.1;

    % 3 遍历数据点切片描述
    nDesc_SigCips = length(jsonData.description);
    prevRecPos = zeros(1,nDesc_SigCips);%用于存放历史起始位置数组
    for j = 1: nDesc_SigCips
        if nDesc_SigCips==1 % 第1条记录
            start_ns_file=jsonData.description.meta_info.pkt_recd_ns;%文件的信号头
            rec_ns_clip=jsonData.description.radio_tap.tstp_ns;
            fs_nb=jsonData.description.radio_tap.samp_rate;
            nb_bw=jsonData.description.radio_tap.bandwidth;
            fc_nb=jsonData.description.radio_tap.chan_freq;
            d_subtype = jsonData.description.d_subtype;
        else
            start_ns_file=jsonData.description(j).meta_info.pkt_recd_ns;
            rec_ns_clip=jsonData.description(j).radio_tap.tstp_ns;
            fs_nb=jsonData.description(j).radio_tap.samp_rate;
            nb_bw=jsonData.description(j).radio_tap.bandwidth;
            fc_nb=jsonData.description(j).radio_tap.chan_freq;
            d_subtype = jsonData.description(j).d_subtype;
        end

        if strcmp(d_subtype, meta_subtype) == 0 %判断信号类型，如不相等略过
            fprintf("%s != %s,skipped!\n",d_subtype, meta_subtype);
            continue;
        end

        % 3.0 判断切片功率
        clip_rec_ms = (rec_ns_clip-start_ns_file)/1e6;%信号起始点ms
        clip_start_pos =  floor(clip_rec_ms*wb_fs/1e3); %转换为数据点个数
        % 3.1 由于meta文件标记偏后，需要调整起始位置
        slabel_clip = fullfile(clsname,filename_bvsp);
        if clip_start_pos > abs(offset_pos)+10 %向前调整一些，以防错过头
            clip_start_pos = clip_start_pos + offset_pos;% 向前调整
        else
            if clip_start_pos < 10 % 舍弃开头截断情况
                continue;
            end
        end

        % 3.2 计算结束位置
        clip_end_pos = clip_start_pos+floor(wb_fs*duration_ms*1e-3);%计算结束位置
        if clip_end_pos > nSig_len %计算截取末尾位置大于数据长度, 取消截取
            %end_pos = nSig_len;
            fprintf("end_pos=%d > nSig_len=%d skipped\n", clip_end_pos, nSig_len);
            continue;
        end
        if offset_pos == 0 %只有传入forwardoffset为0时，才自动调整起始、结束位置
            % 因为初始的信号只是根据时间估算的值，所以需要调整到精确的结束位置
            clip_start_pos = findTheStartPos(wb_signal, clip_start_pos, clip_end_pos);%头部位置
            if clip_start_pos == 0 %找到文件的头位置，未能发现上升沿
                fprintf("clip_start_pos=%d 未能发现上升沿 skipped\n", clip_start_pos);                
                continue;
            end

            clip_end_pos = clip_start_pos+floor(wb_fs*duration_ms*1e-3);%重新计算结束位置
            if clip_end_pos > nSig_len %计算截取末尾位置大于数据长度, 取消截取
                %end_pos = nSig_len;
                fprintf("end_pos=%d > nSig_len=%d skipped\n", clip_end_pos, nSig_len);
                continue;
            end
            clip_end_pos = findTheEndPos(wb_signal, clip_start_pos, clip_end_pos);%搜索结束位置

            if clip_end_pos == 0 %找到文件的尾位置，未能发现下降降沿
                fprintf("clip_end_pos=%d 未能发现上升沿 skipped\n", clip_end_pos);                
                continue;
            end
        end

        %3.3 判断是否重复位置
        if isRepeatedPos(clip_start_pos, prevRecPos)
            fprintf("%s:rec_pos=%d \t fc=%.3f M \t BW=%.2f K \t fs=%.2f M ,same pos,skipped\n",filename_meta,clip_start_pos, fc_nb/1e6,nb_bw/1e3,fs_nb/1e6);
            continue;
        end

        %3.4 判断是否越界
        myclip_sig = wb_signal(clip_start_pos:clip_end_pos);
        if isOverThresholdM(myclip_sig)
            fprintf("文件:%s [%d, %d] 数据点值超过2047,跳过该条\n", slabel_clip, clip_start_pos, clip_end_pos);
            continue;
        end

        avgpower_cursig = sqrt(mean(abs(myclip_sig).^2));%平均功率
        
        snr_db = 10*log10(max((avgpower_cursig^2-min_clippower^2)/min_clippower^2, 1e-10));

        if avgpower_cursig < powerthreshold_region
            fprintf("文件:%s [%d, %d] 切片功率弱于1.1平均信号RMS,跳过该条\n", slabel_clip, clip_start_pos, clip_end_pos);
            continue;      
        end

        % 3.5 带宽及中心频率合法性判断
        %(1) 带宽判断
        ret = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, fc_nb, nb_bw, fs_nb);%判断带宽范围，防止越界
        if ret ~= 1
            fprintf("无效带宽输入：%s\n", filepath_bvsp);
            continue;
        end
        %(2) 中心频率判断
        [nIndex_fc]=getFreqIndex(nb_freqs,fc_nb);
        if nIndex_fc ==0
            fprintf('警告:中心 %f MHz 不在频率列表以内,略过此频率\n',fc_nb/1e6)
            continue
        end

        % 3.6 fc功率大小阈值判断
        power_fc = fftdata(nIndex_fc);%中心频率对应的功率值        
        if power_fc < powerthreshold_fc% 小于2倍平均功率，误报，滤除
            fprintf("%s:rec_pos=%d \t fc=%.3f M, power=%.4f<threshold=%.4f, skipped!\n",filename_meta, clip_start_pos, fc_nb/1e6, power_fc,powerthreshold_fc);
            continue;
        end


        %3.7 截取相应信号，判断是否真的包含有信号,发现某些情况错误标识
        [fftdata_clip, nb_freqs_clip]=getfftData(wb_signal(clip_start_pos:clip_end_pos),wb_fc,wb_fs);
        %[fftdata_clip, nb_freqs_clip]=getconstfftData(wb_signal(rec_pos:end_pos),wb_fc,wb_fs);
        [nIndex_fc]=getFreqIndex(nb_freqs_clip, fc_nb);
        if nIndex_fc ==0
            fprintf('错误：实际情况 频率 %d MHz 并不处于频率范围内\n ',fc_nb/1e6);
            continue;
        end
        
        power_fc = fftdata_clip(nIndex_fc);
        if power_fc<powerthreshold_fc % 小于2倍平均功率，误报，滤除
            fprintf("%s:rec_pos=%d \t fc=%.3f M, actural power=%.4f<threshold=%.4f, skipped!\n",filename_meta, clip_start_pos, fc_nb/1e6, power_fc,powerthreshold_fc);
            continue;
        end
        %3.8 频域检测信号是否存在起始位置干扰
        % bOK = DetectSigSpread(wb_signal(clip_start_pos:clip_end_pos), wb_fs, wb_fc, fc_nb);
        % if bOK~=true
        %     fprintf("%s:rec_pos=%d \t fc=%.3f M, 频域信号存在起始位置干扰, 跳过!\n",filename_meta, clip_start_pos, fc_nb/1e6);
        %     continue;
        % end

        fprintf("%s:rec_pos=%d \t fc=%.3f M \t BW=%.2f K \t fs=%.2f M\n",filename_meta,clip_start_pos, fc_nb/1e6,nb_bw/1e3,fs_nb/1e6);

        %未发现任何问题，写入标签
        slable = sprintf("%s;[%d,%d];%.3fe6;%.2f\n",slabel_clip,clip_start_pos, clip_end_pos,fc_nb/1e6,snr_db);
        fwrite(fout,slable);
        prevRecPos(j) = clip_start_pos;%

    end
end
fclose(fout);
end


%整个信号长度的fft变换，确定频率序列及fft点值
function [fftdata, nb_freqs]=getfftData(wb_signal,wb_fc,wb_fs)
        nSig_len = length(wb_signal);
        fftdata = fftshift(abs(fft(wb_signal))/nSig_len);  %频谱变换
        wb_samps = nSig_len;
        nb_freqs= wb_fc - wb_fs/2 +(0 : wb_samps - 1)*wb_fs/wb_samps;

end

%以nFFTLen长度为基础的fft变换，将信号以能量累加方式统计,对于小信号会比较友好
function [fftdata, nb_freqs] = getconstfftData(wb_signal,wb_fc,wb_fs)
        nSig_len = length(wb_signal);
        nFFTLen = 8192;
        nSpans = floor(nSig_len / nFFTLen); 
        fftdata = zeros(nFFTLen,1);
        for n =1:nSpans
            startPos = 1+(n-1)*nFFTLen;
            fftdata = fftdata+fftshift(abs(fft(wb_signal(startPos:startPos+nFFTLen-1)))/nFFTLen);  %频谱变换
        end
        nb_freqs= wb_fc - wb_fs/2 +(0 : nFFTLen - 1)*wb_fs/nFFTLen;

end

%搜索频率范围索引
function [nIndex]=getFreqIndex(nb_freqs,fc_nb)
        nIndex = 0;
        for n=1:length(nb_freqs) %搜索频率索引
            if nb_freqs(n) > fc_nb
                nIndex = n;
                break;
            end
        end
end

%是否有越界现象 
%第一重保障--超阈值就判定为越界，从信号的幅值是否超过2000进行越界判定
%flag_first = isHaveOverThresholdPoint(clip_signal_real,clip_signal_imag);
function flag = isHaveOverThresholdPoint(signal_real,signal_imag)
       % 切片时域信号，用其幅值判定越界现象，如果越界进行警告 by li ai ping
       %11位信号，2^11-1,将阈值设定为2000
    flag = 0;
    signal_real_value = abs(signal_real);
    signal_imag_value = abs(signal_imag);
    for n = 1:length(signal_imag_value)
        if signal_real_value(n) > 2000 || signal_imag_value(n) > 2000
            flag = 1;
            break
        end
    end
    
end

function [isRepeated] = isRepeatedPos(clip_start_pos, prevRecPos)
% 判定重复切片位置上报--目前存在第二个位置会覆盖第一个位置的问题 by li ai ping
% 两种情况：起始点与历史值一致；与历史值相似
% 当前起始点未出现过，但需要判断与历史值是否相似,阈值设定为150
flag_pos = ismember(clip_start_pos, prevRecPos); %判断当前位置是否出现过
distance_pos =  abs(prevRecPos-double(clip_start_pos));
% flag_similarity_pos = all(similarity_pos(similarity_pos ~= 0)  <= 150);
flag_similarity_pos = find(distance_pos < 150, 1);%判断是否存在相似起始点
% 当前起始点重复出现或者存在相似起始点
isRepeated =  flag_pos || ~isempty(flag_similarity_pos);
end

%第二重保障--统计切片信号中越界点数，暂时以越界点数超过1/3信号长度来判断数据越界
%是否大多数越界
function flag = isOverThresholdM(clip_signal)
signal_real = real(clip_signal);
signal_imag = imag(clip_signal);
flag = 0;
count_real = 0;
count_imag = 0;
signal_real_value = abs(signal_real);
signal_imag_value = abs(signal_imag);
for n_real = 1 :length(signal_real_value)
    if signal_real_value(n_real) > 2000
        count_real = count_real + 1;
    end
end
% disp(count_real)
for n_imag = 1 :length(signal_imag_value)
    if signal_imag_value(n_imag) > 2000
        count_imag = count_imag + 1;
    end
end
%有1/8点越界
percent = 1/8;
if count_real >= length(signal_real)*percent || count_imag >= length(signal_real_value)*percent || (count_imag+count_real)>200
    flag = 1;
end
end
