%  Function    ：cmdTrainRecords2CSV
%  Description : 生成csv文件，方便查看各个Train_records.txt记录
%
%  Author      : Liuzhiguo
%  Date        : 2025-6-20

% 1. 初始化命令
clc
clear
close all

IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数

if exist(myconfig.fpath_labeldef,"file")>0
    fprintf("[启动csv分析文件创建] 输出路径为:%s\n", myconfig.folder_labeleddata);
else
    error("文件：%s 不存在！", myconfig.fpath_labeldef);
end

Table_lbldef = readtable(myconfig.fpath_labeldef,'Format','%s %s %s %f %d','Delimiter',';');
Table_lbldef.Properties.VariableNames = ["foldername","meta_subtype","clsname","duration_ms","forwardoffset"];

nRows = height(Table_lbldef);

foldernames = Table_lbldef(:,1).foldername;
meta_subtypes = Table_lbldef(:,2).meta_subtype;
clsnames = Table_lbldef(:,3).clsname;
duration_mss = Table_lbldef(:,4).duration_ms;
forwardoffsets = Table_lbldef(:,5).forwardoffset;

for iRow = 1 : nRows
    srcFolder = fullfile(myconfig.folder_labeleddata, foldernames(iRow));
    fname_recs1 = fullfile(srcFolder, 'Train_records.txt');
    TrainRecords2CSV(fname_recs1);
end
% 可单独生成
%TrainRecords2CSV('E:\lz_signaldB\datafiles\02-LabeledData\2025-05-30\nb_crossfire_lora\Train_records.txt')

