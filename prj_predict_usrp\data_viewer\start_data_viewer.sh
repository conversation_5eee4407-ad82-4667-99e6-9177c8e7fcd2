#!/bin/bash

# 脚本说明: 用于启动Data Viewer应用的自动化脚本

# 检查Conda是否已初始化
if [ -f "$HOME/miniconda3/bin/conda" ]; then
    CONDA_BASE=$(conda info --base)
elif [ -f "$HOME/anaconda3/bin/conda" ]; then
    CONDA_BASE=$(conda info --base)
else
    echo "错误: 找不到Conda安装路径。"
    exit 1
fi

echo "Conda base 路径: $CONDA_BASE"
source "$CONDA_BASE/etc/profile.d/conda.sh"

# 激活目标Conda环境
ENV_NAME="env_cnr"
echo "正在激活Conda环境: $ENV_NAME"
conda activate $ENV_NAME

# 检查环境是否激活成功
if [ "$CONDA_DEFAULT_ENV" != "$ENV_NAME" ]; then
    echo "错误: 激活Conda环境 '$ENV_NAME' 失败。"
    exit 1
fi

echo "环境激活成功，当前环境: $CONDA_DEFAULT_ENV"

# 获取脚本所在的目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
echo "脚本目录: $SCRIPT_DIR"

# 切换到脚本所在目录并运行Python程序
cd "$SCRIPT_DIR"
echo "正在启动 data_viewer.py..."
python data_viewer.py

# 程序退出后停用环境
echo "程序已关闭，正在停用Conda环境..."
conda deactivate 