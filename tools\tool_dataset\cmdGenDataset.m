%    function    : cmdGenDataset.m
%    description : 生成数据集文件

clear;
close all;

%可放在命令行中执行
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量
%% 基本数据集
maxclip_ms = 6;%好像与3ms差距不大
% WriteHdfsbase_nb("E:\ftproot\signalDB\Train_ds_gen.txt",".\outdataset\train\base\",clip_ms,"nb-dataset-train");
% WriteHdfsbase_nb("E:\ftproot\signalDB\Val_ds_gen.txt",".\outdataset\train\base\",clip_ms,"nb-dataset-val");
%
%DispDatasetRecByChart(".\outdataset\train\base\nb-dataset-val-S1.hdf5",1,"原始")
%
%% 噪声增强
% WriteHdfsnoised;
% % %数据增强
% WriteHdfsAugment_nb("E:\ftproot\signalDB\Train_ds_gen.txt",".\outdataset\train\augment\",clip_ms,"nb-dataset-train");
% WriteHdfsAugment_nb("E:\ftproot\signalDB\Val_ds_gen.txt",".\outdataset\train\augment\",clip_ms,"nb-dataset-val");

%% 2 分散文件数据集 （基本数据集）
%% 2.1 没有路径参数outname的情况下，为分散文件形式
maxclip_ms = 6;%好像与3ms差距不大
% WriteHdfsbase_nb("E:\ftproot\signalDB\Train_ds_gen.txt",".\outdataset\train-files\base\",clip_ms);%分散文件格式
% WriteHdfsbase_nb("E:\ftproot\signalDB\Val_ds_gen.txt",".\outdataset\train-files\base\",clip_ms);%分散文件格式


%% 2.2 噪声增强
% sinFile_list = "E:\project\tool_dataset\outdataset\train-files\base\Train_ds_gen_list.txt";
% outpath = ".\outdataset\train-files\noised\";
% gennoisedDS_bylist(sinFile_list,outpath);
% 
% sinFile_list = "E:\project\tool_dataset\outdataset\train-files\base\Val_ds_gen_list.txt";
% gennoisedDS_bylist(sinFile_list,outpath);


%% 2.3 数据dp，dw，df增强
%WriteHdfsAugment_nb("E:\ftproot\signalDB\ds_bug_issue.txt",".\outdataset\train-files\augment\",clip_ms);

% WriteHdfsAugment_nb("E:\ftproot\signalDB\Train_ds_gen.txt",".\outdataset\train-files\augment\",clip_ms);
% WriteHdfsAugment_nb("E:\ftproot\signalDB\Val_ds_gen.txt",".\outdataset\train-files\augment\",clip_ms);

%DispDatasetRecByChart('E:\ftproot\sampleData\nb_frsky_r9m_fsk\CJ0_1M_1-1-S1.wav',1,"")

% 3 分散文件数据集（test附加数据集）
% 3.1 没有路径参数outname的情况下，为分散文件形式
% WriteHdfsbase_nb("E:\software\nxtool\packages\samDb\Train_rec_pl18.txt",".\outdataset\train-files\test\",clip_ms);%分散文件格式
% WriteHdfsbase_nb("E:\software\nxtool\packages\samDb\Val_ds_gen.txt",".\outdataset\train-files\test\",clip_ms);%分散文件格式
% 3.2 噪声增强
%依赖于test文件夹, 生成，所以输入路径为base文件夹路径
% gennoisedDS_bylist("E:\project\tool_dataset\outdataset\train-files\test\Train_ds_gen_list.txt",".\outdataset\train-files\test_noised\");
% gennoisedDS_bylist("E:\project\tool_dataset\outdataset\train-files\test\Val_ds_gen_list.txt",".\outdataset\train-files\test_noised\");
% 3.3 数据dp，dw，df增强
% WriteHdfsAugment_nb("E:\software\nxtool\packages\samDb\Train_ds_gen.txt",".\outdataset\train-files\test_augment\",clip_ms);
% WriteHdfsAugment_nb("E:\software\nxtool\packages\samDb\Val_ds_gen.txt",".\outdataset\train-files\test_augment\",clip_ms);

