# =======================================================================================================================
#   Function    ：RunAnalyzeModel.py
#   Description : 模型PCA及confusion分类矩阵分析测试
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-07-17
# =======================================================================================================================
from usrlib.usrlib import read_path_config
from utils.eval.ConfusionMatrix import Analyzer
import os
import numpy as np
from usrlib.usrlib import *
import getopt
import sys

def main():
    ModeType_dir, path_train_file, path_val_file, path_test_file, server_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    clsdef_file = os.path.join(ModeType_dir, 'ModType_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('调制类别个数:', cls_count)
    dataset_file = ""

    # 命令行方式获取测试文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['modelname=','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modelname'):
            fname_model = opt_value
            print("The modelname is ", fname_model)
        elif opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something


    #1. 路径及模型参数
    model_path = './logs/history-0716/ModelAMR-ep100-loss0.010-val_loss0.005.pth'

    # 结果保存目录
    save_dir = "./confusion_analysis_results/"
    print(f"模型路径: {model_path}")
    print(f"测试数据: {path_test_file}")
    print(f"分析结果保存至: {save_dir}")
    print("="*60)
    # note：分类模型输出混淆矩阵时设置为modeltype=0；分类模型获得PCA的时候设置modeltype=1(可以这样设置的原因是分类和匹配的主干网络维度全部一致）
    # 1. 混淆矩阵任务
    analyzer_consufiosn = Analyzer(model_path=model_path, clsdef_file=clsdef_file, task='CM',file_test=path_test_file)
    analyzer_consufiosn.run_comfusionmatrix_analysis(save_dir)
    # 2. PCA任务和DBI计算
    analyzer_pca = Analyzer(model_path=model_path, clsdef_file=clsdef_file,task='PCA',file_test=path_test_file)
    analyzer_pca.run_plotpca(save_dir)

if __name__ == "__main__":
    main()