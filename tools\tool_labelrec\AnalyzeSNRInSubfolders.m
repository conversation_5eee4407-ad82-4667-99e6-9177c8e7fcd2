%    function    : AnalyzeSNRInSubfolders.m
%    description : 分析数据类型

% 初始化命令
clc;
clear;
close all;
IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数
% 获取class_def文件所在目录
ClassDefPath = myconfig.fpath_classdef;
% 图片保存目录
saveFolder = fullfile(myconfig.folder_labeleddata,'DataAnalysis');

if exist(ClassDefPath, "file") > 0 
    fprintf("[启动数据集创建] 输出路径为:%s\n", ClassDefPath);
else
    error("文件：%s 不存在！", ClassDefPath);a
end

Table_lbldef = readtable(ClassDefPath, 'Format', '%s %s %f %f', 'Delimiter', ':');
Table_lbldef.Properties.VariableNames = ["classid", "clsname", "nb_w", "duration_ms"];

% 获取class_def文件所在目录作为父目录
parentDir = fileparts(ClassDefPath);

% 初始化snrFolders结构体（新增classid字段）
snrFolders = struct('classid', {}, 'path', {}, 'name', {}, 'snr_counts', {}, 'total_snr', {}, ...
                    'prefixes', {}, 'prefix_totals', {}, 'duration_counts', {}, 'duration_total', {});

% 提取类别信息
foldernames = Table_lbldef(:, 1).classid;       % 信号ID（classid）
meta_subtypes = Table_lbldef(:, 2).clsname;     % 信号类型（clsname）
clsnames = Table_lbldef(:, 3).nb_w;
duration_mss = Table_lbldef(:, 4).duration_ms;

% 生成base文件夹
nRows = height(Table_lbldef);
tic; % 启动计时器

% 定义SNR区间
snr_bins = [5, 9, 12, 15, inf];  
snr_labels = {'5-9 dB', '9-12 dB', '12-15 dB', '>15 dB'};  

% 定义时长计算参数
fs = 61.44e6; 
max_duration_ms = 18; 

% 收集所有唯一前缀
all_prefixes = {};

% 处理每个类别文件夹
for iRow = 1 : nRows
    % 构建源文件夹路径（使用信号类型名称，即clsname）
    srcFolder = fullfile(parentDir, meta_subtypes{iRow});
    
    % 构建snr_records.txt文件路径
    fname_recs1 = fullfile(srcFolder, "Train_records.txt");
    analyzeDatawithPhoto(fname_recs1,61.44e6,saveFolder);
    fprintf('正在处理目录: %s\n', fname_recs1);
    
    % 检查文件是否存在
    if ~exist(fname_recs1, 'file')
        warning('文件不存在: %s', fname_recs1);
        continue;
    end
    
    % 初始化当前文件夹的统计数据（包含classid）
    folder = struct('classid', foldernames{iRow}, 'path', srcFolder, 'name', meta_subtypes{iRow});  
    
    try
        % 读取数据文件
        data = readtable(fname_recs1, 'Delimiter', ';', 'ReadVariableNames', false);
        filenames = data.Var1;
        
        if isempty(filenames)
            warning('文件 %s 不包含数据！', fname_recs1);
            continue;
        end
        
        % 提取SNR值
        snr = data.Var4;
        if iscell(snr)
            snr = cellfun(@str2double, snr);
        end
        
        % 提取文件名前缀
        file_prefixes = cell(size(filenames));
        for k = 1:length(filenames)
            [~, name, ~] = fileparts(filenames{k});
            match = regexp(name, '(CJ\d+_\d+M)', 'tokens');
            if ~isempty(match)
                file_prefixes{k} = match{1}{1};
            else
                file_prefixes{k} = '未知前缀';
            end
        end
        
        % 收集所有唯一前缀
        unique_prefixes = unique(file_prefixes);
        all_prefixes = union(all_prefixes, unique_prefixes, 'stable');
        
        % 计算每个前缀的SNR区间分布
        prefix_snr_counts = containers.Map;
        for p = 1:length(unique_prefixes)
            prefix_idx = strcmp(file_prefixes, unique_prefixes{p});
            prefix_snr = snr(prefix_idx);
            counts = histcounts(prefix_snr, snr_bins);
            prefix_snr_counts(unique_prefixes{p}) = counts;
        end
        
        % 计算每个SNR区间的总计
        total_snr_counts = zeros(1, length(snr_bins)-1);
        for p = 1:length(unique_prefixes)
            total_snr_counts = total_snr_counts + prefix_snr_counts(unique_prefixes{p});
        end
        
        % 计算每个前缀的总计
        prefix_totals = containers.Map;
        for p = 1:length(unique_prefixes)
            prefix_totals(unique_prefixes{p}) = sum(prefix_snr_counts(unique_prefixes{p}));
        end
        
        % 计算时长统计
        durations_ms = [];
        for k = 1:length(data.Var2)
            pos_str = data.Var2{k};
            if ~startsWith(pos_str, '[') || ~endsWith(pos_str, ']')
                continue;
            end
            
            pos_content = pos_str(2:end-1);
            pos_nums = strsplit(pos_content, {' ', ','});
            pos_nums = pos_nums(~cellfun('isempty', pos_nums));
            
            if numel(pos_nums) ~= 2
                continue;
            end
            
            start_pos = str2double(pos_nums{1});
            end_pos = str2double(pos_nums{2});
            
            if isnan(start_pos) || isnan(end_pos) || start_pos >= end_pos
                continue;
            end
            
            duration_ms = (end_pos - start_pos) / fs * 1000;
            if duration_ms <= max_duration_ms
                durations_ms = [durations_ms; duration_ms];
            end
        end
        
        % 时长区间统计（2ms间隔）
        bin_edges = 0:2:max_duration_ms;
        [duration_counts, ~] = histcounts(durations_ms, bin_edges);
        duration_total = sum(duration_counts);
        
        % 保存统计结果（包含classid）
        folder.snr_counts = prefix_snr_counts;
        folder.total_snr = total_snr_counts;
        folder.prefixes = unique_prefixes;
        folder.prefix_totals = prefix_totals;
        folder.duration_counts = duration_counts;
        folder.duration_total = duration_total;
        
        snrFolders(iRow) = folder; % 保存到结构体
        
    catch e
        warning('处理信号类型 %s 时出错: %s', foldernames{iRow}, e.message);
    end
end

% 检查是否有数据
if isempty(snrFolders)
    warning('没有处理任何有效数据！');
    return;
end

% 创建并保存合并的统计CSV（新增信号ID列）
merged_csv_path = fullfile(parentDir, 'merged_statistics.csv');

% **新增信号ID列到表头**
header = {'signal_ID', 'signal_type'};  % 第一列为信号ID，第二列为信号类型

% 拼接SNR相关列
header = [header, cellfun(@(x) sprintf('SNR_%s', x), snr_labels, 'UniformOutput', false)];
header = [header, {'SNR_total'}];

% 拼接时长相关列
header = [header, cellfun(@(x) sprintf('Time_[%d-%d) ms', bin_edges(x), bin_edges(x+1)), ...
                           num2cell(1:length(bin_edges)-1), 'UniformOutput', false)];
header = [header, {'Time_total'}];

% 拼接前缀相关列
if ~isempty(all_prefixes) && size(all_prefixes, 1) > 1
    all_prefixes = all_prefixes'; % 转置为行向量
end
header = [header, all_prefixes, {'Distance_total'}];

% 定义变量类型（第一列为string，对应信号ID）
varTypes = ["string", "string", ...  % 信号ID和信号类型均为字符串
            repmat("double", 1, length(snr_labels)+1), ...
            repmat("double", 1, length(bin_edges)-1+1), ...
            repmat("double", 1, length(all_prefixes)+1)];

% 创建合并表格
merged_data = table('Size', [length(snrFolders), length(header)], ...
                    'VariableNames', header, ...
                    'VariableTypes', varTypes);

% 填充表格数据（新增信号ID列）
for i = 1:length(snrFolders)
    folder = snrFolders(i);
    
    % **设置信号ID和信号类型**
    merged_data{i, 'signal_ID'} = string(folder.classid); % 强制转换为标量字符串       % 新增：写入信号ID
    merged_data{i, 'signal_type'} = string(folder.name); % 信号类型名称
    
    % 设置SNR统计
    for s = 1:length(snr_labels)
        merged_data{i, sprintf('SNR_%s', snr_labels{s})} = folder.total_snr(s);
    end
    merged_data{i, 'SNR_total'} = sum(folder.total_snr);
    
    % 设置时长统计
    for d = 1:length(bin_edges)-1
        merged_data{i, sprintf('Time_[%d-%d) ms', bin_edges(d), bin_edges(d+1))} = folder.duration_counts(d);
    end
    merged_data{i, 'Time_total'} = folder.duration_total;
    
    % 设置前缀统计
    for p = 1:length(all_prefixes)
        prefix = all_prefixes{p};
        if isKey(folder.prefix_totals, prefix)
            merged_data{i, prefix} = folder.prefix_totals(prefix);
        else
            merged_data{i, prefix} = 0;
        end
    end
    
    % 前缀总计
    merged_data{i, 'Distance_total'} = sum(cell2mat([folder.prefix_totals.values()]));
end

% 保存表格到CSV
writetable(merged_data, merged_csv_path, 'Delimiter', ',', 'QuoteStrings', true);
fprintf('已保存合并统计数据到: %s\n', merged_csv_path);
fprintf('所有信号类型处理完成！\n');

% 输出处理时间
elapsedTime = toc;
fprintf('处理完成，耗时: %.2f 秒\n', elapsedTime);