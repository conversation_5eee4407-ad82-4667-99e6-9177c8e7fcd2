#!/usr/bin/env python3
"""
数据文件可视化查看工具 (Data File Visualization Tool)

这是一个基于 Python 和 Tkinter 的多功能数据分析工具，集成了数据文件可视化、
模型推理、信号段分析等功能。主要用于查看和分析信号数据文件。

核心功能:
    - 数据文件可视化（支持 .dat、.bvsp、.hdfv 格式）
    - 四种信号分析图表（时域点值图、时域信号图、频谱图、时频图）
    - 2D/3D 视图切换（支持GPU加速）
    - 模型推理和信号分类（集成ArcFace模型）
    - 信号段检测和标记显示
    - 多语言界面支持（中文/英文）
    - 交互式缩放和导航（滚轮缩放、拖拽平移）

支持的文件格式:
    - .bvsp/.dat: 二进制信号文件（使用 Read_sigfile 函数解析）
    - .hdfv: HDF5 格式数据文件（支持复杂数据结构）

技术特性:
    - 高性能图表绘制（智能采样、异步计算）
    - GPU加速计算（支持CuPy和PyTorch）
    - 自适应字体配置（跨平台兼容）
    - 内存优化处理（大文件支持）
    - 实时信号处理和分析

作者：caonairui
创建时间：2025-06-24
最后更新：2025-07-15
版本：v2.3
"""

# 解决 OpenMP 库冲突问题
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 标准库导入
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import time
from pathlib import Path
import threading
import queue
from concurrent.futures import ThreadPoolExecutor

# 科学计算库导入
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from scipy.signal import spectrogram, firwin, lfilter
from mpl_toolkits.mplot3d import Axes3D

# 添加项目路径到系统路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 尝试导入 torch（3D 显示功能需要）
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("警告: 未找到 torch 库，3D 显示功能将不可用。如需 3D 功能，请安装: pip install torch")

# 导入项目相关模块用于模型推理
try:
    # 添加项目路径到系统路径
    parent_dir = Path(__file__).parent.parent
    sys.path.append(str(parent_dir))
    
    from chanlib.func_scansig import proc_wbsig
    from usrlib.predict_proc import predict_classify_proc_all
    from usrlib.usrlib import Read_sigfile, get_classes, Init_model
    from usrlib.MyHelper import read_signal_from_hdf5
    from nets.arcface import Arcface
    
    MODEL_INFERENCE_AVAILABLE = True
    print("模型推理功能可用")
except ImportError as e:
    MODEL_INFERENCE_AVAILABLE = False
    print(f"警告: 模型推理功能不可用，原因: {e}")

# 配置 matplotlib 中文字体显示
import platform

def configure_matplotlib_fonts():
    """智能配置matplotlib字体，避免字体警告"""
    import matplotlib.font_manager as fm

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    if platform.system() == 'Linux':
        # Linux系统字体候选列表，按优先级排序
        font_candidates = [
            'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
            'Source Han Sans SC', 'Droid Sans Fallback', 'AR PL UMing CN',
            'SimHei', 'Liberation Sans'
        ]
        # 只添加系统中实际存在的字体
        valid_fonts = [font for font in font_candidates if font in available_fonts]
        # 如果没有找到任何中文字体，添加DejaVu Sans作为最后备选
        if not valid_fonts:
            valid_fonts = ['DejaVu Sans']
        plt.rcParams['font.sans-serif'] = valid_fonts
    elif platform.system() == 'Windows':
        # Windows系统
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
    else:
        # 其他系统使用通用字体
        plt.rcParams['font.sans-serif'] = ['Liberation Sans', 'DejaVu Sans', 'Arial']

    plt.rcParams['axes.unicode_minus'] = False
    print(f"data_viewer matplotlib字体配置: {plt.rcParams['font.sans-serif'][:3]}...")

# 配置字体
configure_matplotlib_fonts()


class ScaledMessageBox:
    """
    自定义的放大弹窗类，支持多语言和 1.5 倍字体放大
    
    该类提供了与标准 messagebox 兼容的接口，但具有以下增强功能:
    - 1.5 倍字体放大，改善可读性
    - 中英文界面支持
    - 自适应窗口大小
    - 居中显示
    - 跨平台兼容的图标显示
    
    支持的弹窗类型:
    - showinfo: 信息提示
    - showwarning: 警告提示
    - showerror: 错误提示
    - askokcancel: 确认/取消对话框
    - askyesno: 是/否对话框
    """
    
    @staticmethod
    def get_scaled_font(language='zh'):
        """
        获取 1.5 倍放大的字体
        
        Args:
            language (str): 语言代码，'zh' 为中文，'en' 为英文
            
        Returns:
            tkinter.font.Font: 配置好的字体对象
        """
        import tkinter.font as tkFont
        import platform
        
        system = platform.system()
        if system == 'Windows':
            font_family = 'Microsoft YaHei' if language == 'zh' else 'Segoe UI'
        elif system == 'Linux':
            font_family = 'WenQuanYi Zen Hei' if language == 'zh' else 'DejaVu Sans'
        else:
            font_family = 'DejaVu Sans'
        
        # 1.5 倍放大字体: 9 * 1.5 = 13.5 -> 14
        base_size = int(9 * 1.5)
        return tkFont.Font(family=font_family, size=base_size, weight='normal')
    
    @staticmethod
    def showinfo(title, message, parent=None, language='zh'):
        """显示信息弹窗"""
        return ScaledMessageBox._show_dialog(title, message, 'info', parent, language)
    
    @staticmethod
    def showwarning(title, message, parent=None, language='zh'):
        """显示警告弹窗"""
        return ScaledMessageBox._show_dialog(title, message, 'warning', parent, language)
    
    @staticmethod
    def showerror(title, message, parent=None, language='zh'):
        """显示错误弹窗"""
        return ScaledMessageBox._show_dialog(title, message, 'error', parent, language)
    
    @staticmethod
    def askokcancel(title, message, parent=None, language='zh'):
        """显示确认取消弹窗"""
        return ScaledMessageBox._show_dialog(title, message, 'okcancel', parent, language)
    
    @staticmethod
    def askyesno(title, message, parent=None, language='zh'):
        """显示是否弹窗"""
        return ScaledMessageBox._show_dialog(title, message, 'yesno', parent, language)
    
    @staticmethod
    def _show_dialog(title, message, dialog_type, parent=None, language='zh'):
        """
        显示自定义弹窗的核心实现
        
        Args:
            title (str): 弹窗标题
            message (str): 弹窗内容
            dialog_type (str): 弹窗类型 ('info', 'warning', 'error', 'okcancel', 'yesno')
            parent (tk.Widget): 父窗口
            language (str): 语言代码
            
        Returns:
            bool: 用户选择结果（对于确认类对话框）
        """
        # 创建弹窗窗口
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.resizable(False, False)
        dialog.grab_set()  # 模态对话框
        
        # 设置窗口大小和位置（1.5 倍放大，增加高度确保按钮显示）
        base_width = 300
        base_height = 150  # 增加基础高度以确保按钮显示
        width = int(base_width * 1.5)  # 450
        height = int(base_height * 1.5)  # 225
        
        # 根据消息长度调整高度
        msg_lines = message.count('\n') + 1
        if msg_lines > 3:
            height += int(20 * (msg_lines - 3) * 1.5)
        
        # 居中显示
        if parent:
            parent_x = parent.winfo_rootx()
            parent_y = parent.winfo_rooty()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
        else:
            x = (dialog.winfo_screenwidth() - width) // 2
            y = (dialog.winfo_screenheight() - height) // 2
        
        dialog.geometry(f"{width}x{height}+{x}+{y}")
        
        # 获取放大字体
        font = ScaledMessageBox.get_scaled_font(language)
        button_font = ScaledMessageBox.get_scaled_font(language)
        
        # 创建图标和消息框架
        main_frame = tk.Frame(dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)
        
        # 添加图标
        icon_frame = tk.Frame(main_frame)
        icon_frame.pack(side=tk.LEFT, padx=(0, 15))
        
        # 根据类型和操作系统选择图标
        import platform
        system = platform.system()
        
        if dialog_type == 'info':
            icon_text = "[i]" if system == 'Linux' else "ℹ️"
            icon_color = "#1E90FF"
        elif dialog_type == 'warning':
            icon_text = "/!\\" if system == 'Linux' else "⚠️"
            icon_color = "#FF8C00"
        elif dialog_type == 'error':
            icon_text = "[X]" if system == 'Linux' else "❌"
            icon_color = "#DC143C"
        else:
            icon_text = "[?]" if system == 'Linux' else "❓"
            icon_color = "#4682B4"
        
        # 根据系统选择合适的图标字体
        if system == 'Linux':
            icon_font = ('monospace', int(20 * 1.5), 'bold')  # Linux 使用等宽粗体
        else:
            icon_font = ('Arial', int(24 * 1.5))  # 其他系统使用原字体
        
        icon_label = tk.Label(icon_frame, text=icon_text, font=icon_font, 
                             fg=icon_color)
        icon_label.pack()
        
        # 添加消息文本
        msg_frame = tk.Frame(main_frame)
        msg_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        msg_label = tk.Label(msg_frame, text=message, font=font, wraplength=int(250 * 1.5),
                           justify=tk.LEFT, anchor='w')
        msg_label.pack(fill=tk.BOTH, expand=True)
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=(0, 20))
        
        # 存储对话框结果
        result = [None]
        
        # 定义按钮事件处理函数
        def on_ok():
            result[0] = True
            dialog.destroy()
        
        def on_cancel():
            result[0] = False
            dialog.destroy()
        
        def on_yes():
            result[0] = True
            dialog.destroy()
        
        def on_no():
            result[0] = False
            dialog.destroy()
        
        # 根据语言获取按钮文本
        if language == 'zh':
            ok_text, cancel_text, yes_text, no_text = "确定", "取消", "是", "否"
        else:
            ok_text, cancel_text, yes_text, no_text = "OK", "Cancel", "Yes", "No"
        
        # 根据对话框类型添加按钮
        if dialog_type in ['info', 'warning', 'error']:
            ok_btn = tk.Button(button_frame, text=ok_text, command=on_ok, font=button_font, width=12)
            ok_btn.pack(side=tk.RIGHT)
            result[0] = True  # 默认返回 True
        elif dialog_type == 'okcancel':
            # 创建按钮容器，确保按钮正确显示
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)
            
            ok_btn = tk.Button(btn_container, text=ok_text, command=on_ok, font=button_font, width=10)
            ok_btn.pack(side=tk.RIGHT, padx=(0, 10))
            
            cancel_btn = tk.Button(btn_container, text=cancel_text, command=on_cancel, font=button_font, width=10)
            cancel_btn.pack(side=tk.RIGHT)
            
            result[0] = False  # 默认返回 False（取消）
        elif dialog_type == 'yesno':
            # 创建按钮容器，确保按钮正确显示
            btn_container = tk.Frame(button_frame)
            btn_container.pack(side=tk.RIGHT)
            
            yes_btn = tk.Button(btn_container, text=yes_text, command=on_yes, font=button_font, width=10)
            yes_btn.pack(side=tk.RIGHT, padx=(0, 10))
            
            no_btn = tk.Button(btn_container, text=no_text, command=on_no, font=button_font, width=10)
            no_btn.pack(side=tk.RIGHT)
            
            result[0] = False  # 默认返回 False（否）
        
        # 绑定键盘事件
        def on_escape(event):
            if dialog_type in ['info', 'warning', 'error']:
                on_ok()
            else:
                on_cancel()
        dialog.bind('<Escape>', on_escape)
        
        def on_enter(event):
            if dialog_type in ['info', 'warning', 'error']:
                on_ok()
            elif dialog_type == 'okcancel':
                on_ok()
            elif dialog_type == 'yesno':
                on_yes()
        dialog.bind('<Return>', on_enter)
        
        # 设置焦点到确定按钮
        if dialog_type in ['info', 'warning', 'error']:
            ok_btn.focus_set()
        elif dialog_type == 'okcancel':
            ok_btn.focus_set()
        elif dialog_type == 'yesno':
            yes_btn.focus_set()
        
        # 等待对话框关闭
        dialog.wait_window()
        
        return result[0]


class DataViewer:
    """
    数据文件可视化查看工具主类
    
    这个类实现了一个完整的数据文件可视化 GUI 应用程序，集成了以下核心功能:
    
    数据可视化功能:
    - 支持多种信号数据文件格式 (.bvsp, .dat, .hdfv)
    - 提供四种信号分析图表（时域信号点值图、时域信号图、频谱图、时频图）
    - 2D/3D 视图模式切换
    - 智能抽样优化大数据显示性能
    - 交互式缩放功能（鼠标滚轮）
    
    模型推理功能:
    - 深度学习模型加载和推理
    - 类别定义文件管理
    - 信号段自动检测和标记
    - 推理结果可视化和分析
    
    界面交互功能:
    - 文件列表管理和导航
    - 可调节高度的面板系统
    - 面板折叠/展开功能
    - 多语言界面支持（中文/英文）
    - 完整的中文用户界面
    
    技术特性:
    - 多线程并行计算，避免界面卡顿
    - MATLAB 兼容的频谱和时频图计算
    - 完整的错误处理和容错机制
    - 内存优化和性能调优
    
    Attributes:
        root (tk.Tk): Tkinter 根窗口对象
        current_language (str): 当前界面语言 ('zh' 或 'en')
        current_files (list): 当前加载的文件列表
        current_index (int): 当前选中的文件索引
        current_signal (np.ndarray): 当前加载的信号数据
        current_metadata (dict): 当前信号的元数据（采样率、中心频率等）
        is_3d_mode (bool): 视图模式，False=2D, True=3D
        model (object): 加载的深度学习模型对象
        cls_count (int): 类别数量
        cls_names (list): 类别名称列表
    """
    
    def __init__(self, root):
        """
        初始化数据查看器
        
        Args:
            root (tk.Tk): Tkinter 根窗口对象
        """
        self.root = root
        
        # 语言和界面配置
        self.current_language = 'zh'  # 当前语言设置（'zh'为中文，'en'为英文）
        
        # 定义中文界面文本
        self.texts_zh = {
            'title': '数据文件可视化查看工具',
            'select_file': '选择文件',
            'select_folder': '选择文件夹',
            'previous': '上一个',
            'next': '下一个',
            'toggle_view': '切换视图',
            'view_2d': '2D视图',
            'view_3d': '3D视图',
            'language_switch': '中/En',
            'file_list': '文件列表',
            'file_info': '文件信息',
            'inference_results': '推理结果',
            'please_select': '请选择数据文件',
            'warning': '警告',
            'no_files_found': '未找到支持的数据文件！',
            'loading': '正在加载',
            'loaded': '已加载',
            'load_failed': '加载失败',
            'error': '错误',
            'load_file_failed': '加载文件失败',
            'select_model': '选择模型',
            'select_class_def': '选择类别定义',
            'model_inference': '模型推理',
            'show_classes': '类别定义',
            'model_not_loaded': '请先选择并加载模型文件',
            'no_data_loaded': '请先加载数据文件',
            'processing': '正在处理',
            'inference_complete': '推理完成',
            'inference_failed': '推理失败',
            'model_loaded': '模型已加载',
            'model_load_failed': '模型加载失败',
            'clear_results': '清空',
            'copy_all': '复制全部',
            'copy_selected': '复制选中',
            'expand_panel': '展开面板',
            'results_cleared': '推理结果已清空',
            'new_inference_prompt': '点击"模型推理"开始新的推理...',
            'copied_to_clipboard': '已复制到剪贴板',
            'selected_copied': '选中内容已复制到剪贴板',
            'select_content_first': '请先选中要复制的内容',
            'ready': '就绪',
            # 图表相关文本
            'signal_analysis_charts': '信号分析图表',
            'time_domain_points': '时域信号点值图',
            'time_domain_signal': '时域信号图',
            'frequency_spectrum': '频谱图',
            'time_frequency': '时频图',
            'power_spectral_density_3d': '功率谱密度图 3D',
            'filtered_psd_3d': '滤波后功率谱密度图 3D',
            'signal_amplitude': '信号幅度',
            # 单位和标签
            'time_points_unit': '时域数据点 (×10^5)',
            'signal_voltage': '信号电压值',
            'time_ms': '时间(ms)',
            'voltage_v': 'V',
            'frequency_mhz': '频率(MHz)',
            'spectrum_value': '频谱值',
            'time_frame': '时间帧',
            'frequency_frame': '频率帧',
            'power_db': '功率(dB)',
            'sample_points': '采样点',
            'amplitude': '幅度',
            # 文件信息
            'file_path': '文件路径',
            'file_size': '文件大小',
            'sample_rate': '采样率',
            'center_frequency': '中心频率',
            'signal_length': '信号长度',
            'duration': '持续时间',
            'samples_unit': '个采样点',
            'file_unit': '文件',
            # 状态消息
            'preparing_data': '正在准备数据',
            'data_sampling_completed': '数据抽样完成',
            'time_plot_completed_calculating_spectrum': '时域图绘制完成，正在计算频谱',
            'spectrum_completed_calculating_timefreq': '频谱图绘制完成，正在计算时频图',
            'completing_rendering': '正在完成绘制',
            'time_domain_range': '时域范围',
            'display_text': '显示',
            'sampling_text': '抽样',
            'points_unit': '点',
            'fallback_display_completed': '备用显示完成',
            'data_points_unit': '个数据点',
            # 类别定义相关
            'class_definitions_title': '类别定义信息',
            'class_id': '类别ID',
            'class_name': '类别名称',
            'bandwidth': '带宽(Hz)',
            'min_duration': '最短时间(ms)',
            'no_class_file_selected': '尚未选择类别定义文件',
            'class_file_not_found': '类别定义文件不存在',
            'failed_to_read_class_file': '读取类别定义文件失败',
            'total_classes': '总计类别数',
            'close': '关闭',
        }
        
        # 定义英文界面文本（简化显示）
        self.texts_en = {
            'title': 'Data File Visualization Tool',
            'select_file': 'Select File',
            'select_folder': 'Select Folder',
            'previous': 'Previous',
            'next': 'Next',
            'toggle_view': 'Toggle View',
            'view_2d': '2D View',
            'view_3d': '3D View',
            'language_switch': 'En/中',
            # ... 其他英文文本
        }
        
        # 设置当前语言文本
        self.texts = self.texts_zh if self.current_language == 'zh' else self.texts_en
        
        # 配置字体系统
        self.setup_fonts()
        
        # 设置窗口属性
        self.setup_window()
        
        # 初始化数据成员
        self.init_data_members()
        
        # 创建用户界面
        self.create_interface()
    
    def setup_window(self):
        """
        设置主窗口属性
        
        配置窗口标题、大小、最小尺寸等基本属性，
        所有系统统一使用 1.5 倍 UI 缩放以改善可读性。
        """
        # 设置窗口标题
        self.root.title(self.texts['title'])
        
        # 所有系统统一使用 1.5 倍 UI 缩放
        window_width = int(1200 * 1.5)   # 1800
        window_height = int(800 * 1.5)   # 1200
        min_width = int(800 * 1.5)       # 1200
        min_height = int(600 * 1.5)      # 900
        
        self.root.geometry(f"{window_width}x{window_height}")
        self.root.resizable(True, True)  # 允许调整大小
        self.root.minsize(min_width, min_height)  # 设置最小窗口大小
        
        # 设置窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        print(f"UI 统一放大 1.5 倍: {window_width}x{window_height}")
    
    def init_data_members(self):
        """
        初始化所有数据成员变量
        
        包括文件管理、信号数据、视图状态、模型推理相关变量等。
        """
        # 文件管理相关
        self.current_files = []      # 当前加载的文件列表
        self.current_index = 0       # 当前选中的文件索引
        self.current_signal = None   # 当前加载的信号数据
        self.current_metadata = {}   # 当前信号的元数据
        
        # 视图和显示相关
        self.is_3d_mode = False      # 视图模式：False=2D, True=3D
        
        # 多线程计算相关
        self.thread_pool = ThreadPoolExecutor(max_workers=3)  # 3 个工作线程
        self.computation_queue = queue.Queue()
        self.is_computing = False
        
        # 模型推理相关
        self.model_path = None       # 当前选择的模型文件路径
        self.class_def_path = None   # 当前选择的 class_def.txt 文件路径
        self.model = None            # 加载的模型对象
        self.cls_count = 0           # 类别数量
        self.cls_ids = []            # 类别 ID 列表
        self.cls_names = []          # 类别名称列表
        self.inference_results = []  # 推理结果列表
        
        # 时域信号点值图缩放相关
        self.time_plot_zoom_level = 1.0     # 缩放级别，1.0 为默认大小（最小）
        self.time_plot_center = 0.5         # 缩放中心点（0.0-1.0 的相对位置）
        self.time_plot_original_xlim = None # 原始 X 轴范围
        self.min_zoom_level = 1.0           # 最小缩放级别（默认大小）
        self.max_zoom_level = 50.0          # 最大缩放级别
        
        # 频谱图缩放相关
        self.spectrum_plot_zoom_level = 1.0     # 频谱图缩放级别
        self.spectrum_plot_center = 0.5         # 频谱图缩放中心点
        self.spectrum_plot_original_xlim = None # 频谱图原始 X 轴范围

        # 记住上次选择的目录
        self.last_selected_dir = os.getcwd()
    
    def setup_fonts(self):
        """
        配置应用程序字体系统
        
        根据操作系统和语言设置选择合适的字体，支持中英文显示，
        所有字体统一放大 1.5 倍以改善可读性。
        """
        import tkinter.font as tkFont
        import platform
        
        # 检测操作系统并选择合适的字体
        system = platform.system()
        
        if system == 'Windows':
            # Windows 系统使用微软雅黑和 Segoe UI
            chinese_font = 'Microsoft YaHei'
            english_font = 'Segoe UI'
            monospace_font = 'Consolas'
        elif system == 'Linux':
            # Linux 系统使用支持中文的开源字体
            chinese_font = 'fixed'  # 适用于 Ubuntu 等 Linux 发行版
            english_font = 'DejaVu Sans'
            monospace_font = 'DejaVu Sans Mono'
        else:
            # 其他系统使用通用字体
            chinese_font = 'DejaVu Sans'
            english_font = 'DejaVu Sans'
            monospace_font = 'DejaVu Sans Mono'
        
        print(f"检测到操作系统: {system}")
        print(f"中文字体: {chinese_font}, 英文字体: {english_font}, 等宽字体: {monospace_font}")
        
        # 所有系统统一放大 1.5 倍字体
        base_font_size = int(9 * 1.5)  # 13.5 -> 13
        print(f"字体统一放大 1.5 倍，基础字体大小: {base_font_size}")
        
        # 字体配置参数
        self.font_config = {
            # 按钮字体 - 支持中英文
            'button_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'button_font_size': base_font_size,
            'button_font_weight': 'normal',
            
            # 标题字体
            'title_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'title_font_size': base_font_size,
            'title_font_weight': 'bold',
            
            # 状态标签字体
            'status_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'status_font_size': base_font_size,
            'status_font_weight': 'normal',
            
            # 文本框字体
            'text_font_family': monospace_font,
            'text_font_size': base_font_size,
            'text_font_weight': 'normal',
            
            # 文件列表字体（放大 1.25 倍）
            'list_font_family': chinese_font if self.current_language == 'zh' else english_font,
            'list_font_size': int(base_font_size * 1.25),  # 13 * 1.25 = 16.25 -> 16
            'list_font_weight': 'normal',
        }
        
        # 验证字体可用性并设置备用字体
        self._validate_and_set_fonts()
        
        # 创建字体对象
        self.fonts = {
            'button_font': tkFont.Font(
                family=self.font_config['button_font_family'],
                size=self.font_config['button_font_size'],
                weight=self.font_config['button_font_weight']
            ),
            'title_font': tkFont.Font(
                family=self.font_config['title_font_family'],
                size=self.font_config['title_font_size'],
                weight=self.font_config['title_font_weight']
            ),
            'status_font': tkFont.Font(
                family=self.font_config['status_font_family'],
                size=self.font_config['status_font_size'],
                weight=self.font_config['status_font_weight']
            ),
            'text_font': tkFont.Font(
                family=self.font_config['text_font_family'],
                size=self.font_config['text_font_size'],
                weight=self.font_config['text_font_weight']
            ),
            'list_font': tkFont.Font(
                family=self.font_config['list_font_family'],
                size=self.font_config['list_font_size'],
                weight=self.font_config['list_font_weight']
            ),
        }
    
    def _validate_and_set_fonts(self):
        """
        验证字体是否可用，如果不可用则使用备用字体
        
        该方法会检查系统中是否存在指定的字体，如果不存在，
        会自动选择系统中可用的备用字体。
        """
        available_fonts = self.get_available_fonts()
        
        # 存储系统字体信息以供后续使用
        self.system_fonts = {
            'chinese': self.font_config['button_font_family'],
            'english': self.font_config['status_font_family'],
            'monospace': self.font_config['text_font_family']
        }
        
        # 验证中文字体
        chinese_font = self.font_config['button_font_family']
        if chinese_font not in available_fonts:
            # 尝试备用中文字体
            fallback_chinese = ['WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'Source Han Sans', 
                               'Liberation Sans', 'DejaVu Sans', 'Arial']
            for font in fallback_chinese:
                if font in available_fonts:
                    chinese_font = font
                    break
            else:
                import tkinter.font as tkFont
                chinese_font = tkFont.nametofont("TkDefaultFont").actual()['family']
        
        # 验证英文字体和等宽字体（类似处理）
        # ... 这里简化了验证过程
        
        # 更新字体配置
        self.font_config.update({
            'button_font_family': chinese_font if self.current_language == 'zh' else self.font_config['status_font_family'],
            'title_font_family': chinese_font if self.current_language == 'zh' else self.font_config['status_font_family'],
            'status_font_family': chinese_font if self.current_language == 'zh' else self.font_config['status_font_family'],
            'list_font_family': chinese_font if self.current_language == 'zh' else self.font_config['status_font_family'],
        })
        
        print(f"最终选择字体 - 中文: {chinese_font}")
    
    def get_available_fonts(self):
        """
        获取系统可用字体列表
        
        Returns:
            list: 系统中可用的字体族名称列表
        """
        try:
            import tkinter.font as tkFont
            return sorted(tkFont.families())
        except Exception as e:
            print(f"获取系统字体失败: {e}")
            return []
    
    def on_closing(self):
        """
        处理窗口关闭事件
        
        在窗口关闭时进行必要的清理工作，包括关闭线程池、
        清理 matplotlib 图形等，避免内存泄漏。
        """
        try:
            # 关闭线程池
            if hasattr(self, 'thread_pool'):
                self.thread_pool.shutdown(wait=False)
            
            # 清理 matplotlib 图形以避免内存泄漏
            plt.close('all')
        except:
            pass
        finally:
            self.root.destroy()
    
    def create_interface(self):
        """
        创建用户界面
        
        构建整个应用程序的 GUI 界面，包括：
        - 顶部工具栏（文件选择和导航按钮、视图切换、语言切换、模型功能按钮）
        - 左侧可调节面板（文件列表、文件信息、推理结果）
        - 右侧图表显示区域（四种信号分析图表）
        
        界面特性：
        - 1.5 倍字体放大，改善可读性
        - 可折叠和调节高度的左侧面板系统
        - 支持 2D/3D 视图切换
        - 完整的中英文界面支持
        """
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建顶部工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 添加工具栏按钮（使用自定义字体）
        button_style = {'font': self.fonts['button_font']} if hasattr(self, 'fonts') else {}
        
        self.select_file_button = tk.Button(toolbar, text=self.texts['select_file'], command=self.select_file, **button_style)
        self.select_file_button.pack(side=tk.LEFT, padx=(0, 5))
        self.select_folder_button = tk.Button(toolbar, text=self.texts['select_folder'], command=self.select_folder, **button_style)
        self.select_folder_button.pack(side=tk.LEFT, padx=(0, 5))
        self.previous_button = tk.Button(toolbar, text=self.texts['previous'], command=self.prev_file, **button_style)
        self.previous_button.pack(side=tk.LEFT, padx=(10, 5))
        self.next_button = tk.Button(toolbar, text=self.texts['next'], command=self.next_file, **button_style)
        self.next_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 添加视图切换按钮
        self.view_button = tk.Button(toolbar, text=self.texts['view_2d'], command=self.toggle_view_mode, **button_style)
        self.view_button.pack(side=tk.LEFT, padx=(10, 5))
        
        # 添加语言切换按钮
        self.language_button = tk.Button(toolbar, text=self.texts['language_switch'], command=self.toggle_language, **button_style)
        self.language_button.pack(side=tk.LEFT, padx=(10, 5))
        
        # 添加模型相关按钮（如果模型推理功能可用）
        if MODEL_INFERENCE_AVAILABLE:
            # 添加类别定义按钮
            self.class_def_button = tk.Button(toolbar, text=self.texts['select_class_def'], command=self.select_class_def_file, **button_style)
            self.class_def_button.pack(side=tk.LEFT, padx=(10, 5))
            
            self.model_button = tk.Button(toolbar, text=self.texts['select_model'], command=self.select_model, **button_style)
            self.model_button.pack(side=tk.LEFT, padx=(0, 5))
            self.inference_button = tk.Button(toolbar, text=self.texts['model_inference'], command=self.run_inference, **button_style)
            self.inference_button.pack(side=tk.LEFT, padx=(0, 5))
            self.show_classes_button = tk.Button(toolbar, text=self.texts['show_classes'], command=self.show_class_definitions, **button_style)
            self.show_classes_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 添加状态标签（显示在工具栏右侧）
        status_style = {'font': self.fonts['status_font']} if hasattr(self, 'fonts') else {}
        self.status_label = tk.Label(toolbar, text=self.texts['please_select'], **status_style)
        self.status_label.pack(side=tk.RIGHT)
        
        # 创建内容区域框架
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧面板（可调节高度的折叠面板系统）
        # 所有系统统一放大1.5倍面板宽度
        panel_width = int(250 * 1.5)  # 375
        
        self.left_panel = ttk.Frame(content_frame, width=panel_width)  # 增加宽度以适应折叠按钮
        self.left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.left_panel.pack_propagate(False)  # 保持固定宽度
        
        # 创建可调节高度的分隔面板
        self.create_resizable_panels()
        
        # 创建右侧面板（图表显示区域）
        self.right_panel = ttk.Frame(content_frame)
        self.right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建图表容器
        self.create_chart_container()
        
        # 初始化空白图表
        self.init_plots()
    
    def create_resizable_panels(self):
        """
        创建可调节高度和折叠功能的左侧面板系统
        
        实现功能：
        - 使用 PanedWindow 创建可拖拽调节高度的面板
        - 支持面板折叠/展开功能
        - 三个主要面板：文件列表、文件信息、推理结果
        - 所有尺寸按 1.5 倍缩放优化显示
        
        面板特性：
        - 文件列表面板：显示当前加载的所有文件
        - 文件信息面板：显示当前文件的详细元数据
        - 推理结果面板：显示模型推理结果和分析
        """
        # 创建垂直PanedWindow来实现可调节高度
        self.paned_window = tk.PanedWindow(self.left_panel, orient=tk.VERTICAL, 
                                          sashwidth=5, sashrelief=tk.RAISED)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        # 初始化折叠状态字典
        self.panel_collapsed = {
            'file_list': False,
            'file_info': False,
            'inference_results': False
        }
        
        # 所有系统统一放大1.5倍面板高度
        file_list_height = int(200 * 1.5)     # 300
        file_info_height = int(150 * 1.5)     # 225
        inference_height = int(200 * 1.5)     # 300
        
        # 创建文件列表面板
        self.create_collapsible_panel(
            panel_id='file_list',
            title=self.texts['file_list'],
            height=file_list_height,
            create_content_func=self.create_file_list_content
        )
        
        # 创建文件信息面板
        self.create_collapsible_panel(
            panel_id='file_info',
            title=self.texts['file_info'],
            height=file_info_height,
            create_content_func=self.create_file_info_content
        )
        
        # 创建推理结果面板（如果模型推理功能可用）
        if MODEL_INFERENCE_AVAILABLE:
            self.create_collapsible_panel(
                panel_id='inference_results',
                title=self.texts['inference_results'],
                height=inference_height,
                create_content_func=self.create_inference_results_content
            )
    
    def create_collapsible_panel(self, panel_id, title, height, create_content_func):
        """
        创建可折叠的面板
        
        参数:
            panel_id (str): 面板ID
            title (str): 面板标题
            height (int): 初始高度
            create_content_func (function): 创建内容的函数
        """
        # 创建主面板框架
        panel_frame = ttk.Frame(self.paned_window)
        panel_frame.pack_propagate(False)
        
        # 创建标题栏
        title_frame = ttk.Frame(panel_frame)
        title_frame.pack(fill=tk.X, padx=2, pady=2)
        
        # 创建折叠/展开按钮
        button_style = {'font': self.fonts['button_font']} if hasattr(self, 'fonts') else {}
        title_style = {'font': self.fonts['title_font']} if hasattr(self, 'fonts') else {'font': ('Arial', 9, 'bold')}
        
        collapse_button = tk.Button(title_frame, text="▼", width=3,
                                    command=lambda: self.toggle_panel_collapse(panel_id), **button_style)
        collapse_button.pack(side=tk.LEFT)
        
        # 创建标题标签
        title_label = tk.Label(title_frame, text=title, **title_style)
        title_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 存储标题标签引用，用于语言切换时更新字体和文本
        setattr(self, f'{panel_id}_title_label', title_label)
        
        # 为推理结果面板添加清空按钮
        if panel_id == 'inference_results':
            clear_button = tk.Button(title_frame, text=self.texts['clear_results'], width=6,
                                    command=lambda: self.clear_inference_results(), **button_style)
            clear_button.pack(side=tk.RIGHT, padx=(0, 5))
            # 存储按钮引用，用于语言切换时更新
            setattr(self, f'{panel_id}_clear_button', clear_button)
        
        # 存储按钮引用，用于更新图标
        setattr(self, f'{panel_id}_collapse_button', collapse_button)
        
        # 创建内容区域
        content_frame = ttk.Frame(panel_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=(0, 2))
        
        # 调用内容创建函数
        create_content_func(content_frame)
        
        # 存储面板引用
        setattr(self, f'{panel_id}_frame', panel_frame)
        setattr(self, f'{panel_id}_content', content_frame)
        
        # 添加到PanedWindow（所有系统统一放大1.5倍minsize）
        min_panel_size = int(30 * 1.5)  # 45
        self.paned_window.add(panel_frame, minsize=min_panel_size, height=height)
    
    def create_file_list_content(self, parent):
        """创建文件列表内容"""
        # 创建滚动条框架
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Listbox和滚动条（应用1.25倍放大字体）
        list_style = {'font': self.fonts['list_font']} if hasattr(self, 'fonts') else {'font': ('Arial', 16)}
        self.file_listbox = tk.Listbox(list_frame, selectmode=tk.SINGLE, **list_style)
        scrollbar_list = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.config(yscrollcommand=scrollbar_list.set)
        
        # 布局
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_list.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定事件
        self.file_listbox.bind('<<ListboxSelect>>', self.on_file_select)
    
    def create_file_info_content(self, parent):
        """创建文件信息内容"""
        # 创建文本框和滚动条
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        text_style = {'font': self.fonts['text_font']} if hasattr(self, 'fonts') else {'font': ('Consolas', 9)}
        self.info_text = tk.Text(info_frame, wrap=tk.WORD, **text_style)
        scrollbar_info = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.config(yscrollcommand=scrollbar_info.set)
        
        # 布局
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_info.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_inference_results_content(self, parent):
        """创建推理结果内容"""
        # 创建文本框和滚动条
        results_frame = ttk.Frame(parent)
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        text_style = {'font': self.fonts['text_font']} if hasattr(self, 'fonts') else {'font': ('Consolas', 9)}
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, **text_style)
        scrollbar_results = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.config(yscrollcommand=scrollbar_results.set)
        
        # 布局
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_results.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置为只读
        self.results_text.config(state=tk.DISABLED)
        
        # 添加右键菜单
        self.create_results_context_menu()
    
    def toggle_panel_collapse(self, panel_id):
        """
        切换面板的折叠/展开状态
        
        参数:
            panel_id (str): 面板ID
        """
        try:
            # 获取面板组件
            panel_frame = getattr(self, f'{panel_id}_frame')
            content_frame = getattr(self, f'{panel_id}_content')
            collapse_button = getattr(self, f'{panel_id}_collapse_button')
            
            # 所有系统统一使用1.5倍缩放因子
            scale_factor = 1.5
            
            # 切换折叠状态
            is_collapsed = self.panel_collapsed[panel_id]
            
            if is_collapsed:
                # 展开面板
                content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=(0, 2))
                collapse_button.config(text="▼")
                self.panel_collapsed[panel_id] = False
                
                # 恢复面板的默认高度
                if panel_id == 'file_list':
                    default_height = int(200 * scale_factor)
                elif panel_id == 'file_info':
                    default_height = int(150 * scale_factor)
                elif panel_id == 'inference_results':
                    default_height = int(200 * scale_factor)
                else:
                    default_height = int(150 * scale_factor)
                
                # 使用 after 延迟设置高度，确保内容先显示
                self.root.after(10, lambda: self.paned_window.paneconfigure(panel_frame, height=default_height))
            else:
                # 折叠面板
                content_frame.pack_forget()
                collapse_button.config(text="?")
                self.panel_collapsed[panel_id] = True
                
                # 设置面板的最小高度（仅显示标题栏）
                collapsed_height = int(35 * scale_factor)
                self.paned_window.paneconfigure(panel_frame, height=collapsed_height)
                
        except Exception as e:
            print(f"折叠面板操作失败: {e}")
            # 重置折叠状态以避免状态不一致
            self.panel_collapsed[panel_id] = False
    
    def clear_inference_results(self):
        """清空推理结果"""
        if hasattr(self, 'results_text'):
            self.results_text.config(state=tk.NORMAL)
            self.results_text.delete('1.0', tk.END)
            self.results_text.insert(tk.END, f"{self.texts['results_cleared']}\n\n{self.texts['new_inference_prompt']}")
            self.results_text.config(state=tk.DISABLED)
    
    def create_results_context_menu(self):
        """为推理结果创建右键菜单"""
        self.results_context_menu = tk.Menu(self.root, tearoff=0)
        self.results_context_menu.add_command(label=self.texts['copy_all'], command=self.copy_all_results)
        self.results_context_menu.add_command(label=self.texts['copy_selected'], command=self.copy_selected_results)
        self.results_context_menu.add_separator()
        self.results_context_menu.add_command(label=self.texts['clear_results'], command=self.clear_inference_results)
        self.results_context_menu.add_separator()
        self.results_context_menu.add_command(label=self.texts['expand_panel'], command=lambda: self.expand_panel_for_viewing('inference_results'))
        
        # 绑定右键事件
        self.results_text.bind("<Button-3>", self.show_results_context_menu)
        
        # 绑定键盘快捷键
        self.results_text.bind("<Control-c>", lambda e: self.copy_selected_results())
        self.results_text.bind("<Control-a>", lambda e: self.select_all_results())
        self.results_text.bind("<Delete>", lambda e: self.clear_inference_results())
        self.results_text.bind("<F5>", lambda e: self.expand_panel_for_viewing('inference_results'))
    
    def show_results_context_menu(self, event):
        """显示推理结果右键菜单"""
        try:
            self.results_context_menu.tk_popup(event.x_root, event.y_root)
        except:
            pass
        finally:
            self.results_context_menu.grab_release()
    
    def copy_all_results(self):
        """复制所有推理结果到剪贴板"""
        try:
            self.results_text.config(state=tk.NORMAL)
            content = self.results_text.get('1.0', tk.END)
            self.results_text.config(state=tk.DISABLED)
            
            self.root.clipboard_clear()
            self.root.clipboard_append(content)
            self.root.update()  # 确保剪贴板更新
            
            # 临时显示复制成功的消息
            self.status_label.config(text=self.texts['copied_to_clipboard'])
            self.root.after(2000, lambda: self.status_label.config(text=self.texts.get('loaded', self.texts['ready'])))
        except Exception as e:
            print(f"复制失败: {e}")
    
    def copy_selected_results(self):
        """复制选中的推理结果到剪贴板"""
        try:
            if self.results_text.tag_ranges(tk.SEL):
                content = self.results_text.get(tk.SEL_FIRST, tk.SEL_LAST)
                self.root.clipboard_clear()
                self.root.clipboard_append(content)
                self.root.update()
                
                self.status_label.config(text=self.texts['selected_copied'])
                self.root.after(2000, lambda: self.status_label.config(text=self.texts.get('loaded', self.texts['ready'])))
            else:
                self.status_label.config(text=self.texts['select_content_first'])
                self.root.after(2000, lambda: self.status_label.config(text=self.texts.get('loaded', self.texts['ready'])))
        except Exception as e:
            print(f"复制选中内容失败: {e}")
    
    def select_all_results(self):
        """选择推理结果的所有文本"""
        try:
            self.results_text.config(state=tk.NORMAL)
            self.results_text.tag_add(tk.SEL, '1.0', tk.END)
            self.results_text.config(state=tk.DISABLED)
            return "break"  # 阻止默认的 Ctrl+A 行为
        except Exception as e:
            print(f"选择全部失败: {e}")
    
    def expand_panel_for_viewing(self, panel_id):
        """展开面板以便更好地查看内容"""
        if self.panel_collapsed.get(panel_id, False):
            self.toggle_panel_collapse(panel_id)
        
        # 将面板高度设置为较大值以便查看（所有系统统一放大1.5倍）
        panel_frame = getattr(self, f'{panel_id}_frame')
        if panel_id == 'inference_results':
            view_height = int(400 * 1.5)  # 600
            self.paned_window.paneconfigure(panel_frame, height=view_height)
        
        # 滚动到顶部
        if hasattr(self, 'results_text'):
            self.results_text.see('1.0')

    def create_chart_container(self):
        """
        创建图表容器
        """
        # 创建matplotlib图表（所有系统统一放大1.5倍）
        fig_width = 12 * 1.5   # 18
        fig_height = 10 * 1.5  # 15
        
        self.fig = plt.figure(figsize=(fig_width, fig_height))
        # 不设置总标题，保持简洁
        # chart_title = self.texts['signal_analysis_charts']
        # self.fig.suptitle(chart_title)
        
        # 根据当前模式创建子图
        self.create_subplots()
        
        # 将matplotlib图表嵌入到tkinter中
        self.canvas = FigureCanvasTkAgg(self.fig, self.right_panel)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加鼠标滚轮事件监听（用于时域信号点值图的横向缩放）
        self.canvas.mpl_connect('scroll_event', self.on_mouse_scroll)
    
    def on_mouse_scroll(self, event):
        """
        处理鼠标滚轮事件，实现交互式图表缩放
        
        该方法为时域信号点值图和频谱图提供了滚轮缩放功能，增强用户交互体验。
        
        缩放特性：
        - 时域信号点值图：支持 1-50 倍横向缩放
        - 频谱图：支持 1-50 倍横向缩放
        - 以鼠标光标位置为缩放中心
        - 自动边界检测，防止超出数据范围
        
        交互操作：
        - 向上滚动：放大（缩放因子 1.2）
        - 向下滚动：缩小（缩放因子 1/1.2）
        - 缩放范围自动限制在原始数据范围内
        - 加载新数据时自动重置缩放级别
        
        技术实现：
        - 实时计算鼠标相对位置
        - 动态调整 X 轴显示范围
        - 保持 Y 轴范围不变
        - 即时重绘图表
        
        Args:
            event: matplotlib 滚轮事件对象，包含鼠标位置和滚动方向
        """
        # 只在2D模式下处理
        if self.is_3d_mode or not hasattr(self, 'axes') or len(self.axes) < 3:
            return
        
        # 检查鼠标位置
        ax1 = self.axes[0]  # 时域信号点值图
        ax3 = self.axes[2]  # 频谱图
        
        if event.inaxes == ax1:
            # 时域信号点值图缩放
            self.handle_time_plot_zoom(event, ax1)
        elif event.inaxes == ax3:
            # 频谱图缩放
            self.handle_spectrum_plot_zoom(event, ax3)
    
    def handle_time_plot_zoom(self, event, ax):
        """
        处理时域信号点值图的缩放
        """
        
        # 保存原始X轴范围（仅在第一次缩放时）
        if self.time_plot_original_xlim is None:
            self.time_plot_original_xlim = ax.get_xlim()
        
        # 获取鼠标的绝对坐标位置（数据坐标）
        xlim = ax.get_xlim()
        mouse_x = event.xdata if event.xdata is not None else (xlim[0] + xlim[1]) / 2
        
        # 计算缩放因子
        zoom_factor = 1.2 if event.button == 'up' else 1.0 / 1.2
        
        # 计算新的缩放级别
        new_zoom_level = self.time_plot_zoom_level * zoom_factor
        
        # 限制缩放范围
        if new_zoom_level < self.min_zoom_level:
            new_zoom_level = self.min_zoom_level
        elif new_zoom_level > self.max_zoom_level:
            new_zoom_level = self.max_zoom_level
        
        # 如果缩放级别没有变化，则不进行任何操作
        if abs(new_zoom_level - self.time_plot_zoom_level) < 0.01:
            return
        
        self.time_plot_zoom_level = new_zoom_level
        
        # 计算新的X轴范围
        if self.time_plot_original_xlim is not None:
            orig_min, orig_max = self.time_plot_original_xlim
            orig_range = orig_max - orig_min
            
            # 确保鼠标位置在原始范围内
            mouse_x = max(orig_min, min(orig_max, mouse_x))
            
            # 计算鼠标在原始范围内的相对位置
            mouse_rel = (mouse_x - orig_min) / orig_range if orig_range > 0 else 0.5
            
            # 新的显示范围
            new_range = orig_range / self.time_plot_zoom_level
            
            # 以鼠标位置为中心计算新的范围
            new_min = mouse_x - mouse_rel * new_range
            new_max = mouse_x + (1 - mouse_rel) * new_range
            
            # 确保新范围不超出原始范围
            if new_min < orig_min:
                offset = orig_min - new_min
                new_min = orig_min
                new_max = min(orig_max, new_max + offset)
            elif new_max > orig_max:
                offset = new_max - orig_max
                new_max = orig_max
                new_min = max(orig_min, new_min - offset)
            
            # 应用新的X轴范围
            ax.set_xlim(new_min, new_max)
            
            # 重新绘制图表
            self.canvas.draw()
    
    def handle_spectrum_plot_zoom(self, event, ax):
        """
        处理频谱图的缩放
        """
        # 保存原始X轴范围（仅在第一次缩放时）
        if self.spectrum_plot_original_xlim is None:
            self.spectrum_plot_original_xlim = ax.get_xlim()
        
        # 获取鼠标的绝对坐标位置（数据坐标）
        xlim = ax.get_xlim()
        mouse_x = event.xdata if event.xdata is not None else (xlim[0] + xlim[1]) / 2
        
        # 计算缩放因子
        zoom_factor = 1.2 if event.button == 'up' else 1.0 / 1.2
        
        # 计算新的缩放级别
        new_zoom_level = self.spectrum_plot_zoom_level * zoom_factor
        
        # 限制缩放范围
        if new_zoom_level < self.min_zoom_level:
            new_zoom_level = self.min_zoom_level
        elif new_zoom_level > self.max_zoom_level:
            new_zoom_level = self.max_zoom_level
        
        # 如果缩放级别没有变化，则不进行任何操作
        if abs(new_zoom_level - self.spectrum_plot_zoom_level) < 0.01:
            return
        
        self.spectrum_plot_zoom_level = new_zoom_level
        
        # 计算新的X轴范围
        if self.spectrum_plot_original_xlim is not None:
            orig_min, orig_max = self.spectrum_plot_original_xlim
            orig_range = orig_max - orig_min
            
            # 确保鼠标位置在原始范围内
            mouse_x = max(orig_min, min(orig_max, mouse_x))
            
            # 计算鼠标在原始范围内的相对位置
            mouse_rel = (mouse_x - orig_min) / orig_range if orig_range > 0 else 0.5
            
            # 新的显示范围
            new_range = orig_range / self.spectrum_plot_zoom_level
            
            # 以鼠标位置为中心计算新的范围
            new_min = mouse_x - mouse_rel * new_range
            new_max = mouse_x + (1 - mouse_rel) * new_range
            
            # 确保新范围不超出原始范围
            if new_min < orig_min:
                offset = orig_min - new_min
                new_min = orig_min
                new_max = min(orig_max, new_max + offset)
            elif new_max > orig_max:
                offset = new_max - orig_max
                new_max = orig_max
                new_min = max(orig_min, new_min - offset)
            
            # 应用新的X轴范围
            ax.set_xlim(new_min, new_max)
            
            # 重新绘制图表
            self.canvas.draw()
    
    def create_subplots(self):
        """
        根据当前视图模式创建子图
        """
        # 清除现有子图
        self.fig.clear()
        
        if self.is_3d_mode:
            # 3D模式：2个子图垂直排列，都是3D
            self.axes = []
            self.axes.append(self.fig.add_subplot(2, 1, 1, projection='3d'))  # 功率谱密度图3D
            self.axes.append(self.fig.add_subplot(2, 1, 2, projection='3d'))  # 滤波后功率谱密度图3D
        else:
            # 2D模式：4个子图垂直排列
            self.axes = []
            self.axes.append(self.fig.add_subplot(4, 1, 1))  # 时域信号值图
            self.axes.append(self.fig.add_subplot(4, 1, 2))  # 时域信号图
            self.axes.append(self.fig.add_subplot(4, 1, 3))  # 频谱图
            self.axes.append(self.fig.add_subplot(4, 1, 4))  # 时频图
    
    def toggle_view_mode(self):
        """
        切换2D/3D视图模式
        """
        # 检查3D模式是否可用
        if not self.is_3d_mode and not TORCH_AVAILABLE:
            ScaledMessageBox.showwarning(self.texts['warning'], 
                                 "3D显示功能需要torch库支持。\n请安装torch: pip install torch" if self.current_language == 'zh' else "3D display requires torch library.\nPlease install: pip install torch",
                                 parent=self.root, language=self.current_language)
            return
        
        # 切换模式
        self.is_3d_mode = not self.is_3d_mode
        
        # 更新按钮文本
        if self.is_3d_mode:
            self.view_button.config(text=self.texts['view_3d'])
        else:
            self.view_button.config(text=self.texts['view_2d'])
        
        # 重新创建子图
        self.create_subplots()
        
        # 如果有数据，重新显示
        if self.current_signal is not None:
            self.display_signal()
        else:
            self.init_plots()
    
    def toggle_language(self):
        """
        切换界面语言（中文/英文）
        """
        # 切换语言
        self.current_language = 'en' if self.current_language == 'zh' else 'zh'
        
        # 更新文本字典
        self.texts = self.texts_zh if self.current_language == 'zh' else self.texts_en
        
        # 更新字体
        self.update_fonts_on_language_change()
        
        # 更新界面元素
        self.update_interface_texts()
        
        # 更新图表标题（不管是否有数据都更新）
        self.update_chart_titles_on_language_change()
    
    def update_interface_texts(self):
        """
        更新所有界面元素的文本
        """
        try:
            # 更新窗口标题
            self.root.title(self.texts['title'])
            
            # 更新工具栏按钮
            self.select_file_button.config(text=self.texts['select_file'])
            self.select_folder_button.config(text=self.texts['select_folder'])
            self.previous_button.config(text=self.texts['previous'])
            self.next_button.config(text=self.texts['next'])
            
            # 更新视图切换按钮
            if self.is_3d_mode:
                self.view_button.config(text=self.texts['view_3d'])
            else:
                self.view_button.config(text=self.texts['view_2d'])
            
            # 更新语言切换按钮
            self.language_button.config(text=self.texts['language_switch'])
            
            # 更新模型相关按钮（如果存在）
            if MODEL_INFERENCE_AVAILABLE:
                self.class_def_button.config(text=self.texts['select_class_def'])
                self.model_button.config(text=self.texts['select_model'])
                self.inference_button.config(text=self.texts['model_inference'])
                self.show_classes_button.config(text=self.texts['show_classes'])
            
            # 更新状态标签（如果有文件列表）
            if self.current_files:
                self.status_label.config(text=f"{self.texts['file_unit']} {self.current_index + 1} / {len(self.current_files)}")
            else:
                self.status_label.config(text=self.texts['please_select'])
            
            # 更新面板标题
            self.update_panel_titles()
            
            # 更新清空按钮（如果存在）
            if MODEL_INFERENCE_AVAILABLE and hasattr(self, 'inference_results_clear_button'):
                self.inference_results_clear_button.config(text=self.texts['clear_results'])
            
            # 更新文件列表字体（语言切换时）
            if hasattr(self, 'file_listbox') and hasattr(self, 'fonts') and 'list_font' in self.fonts:
                self.file_listbox.config(font=self.fonts['list_font'])
            
            # 更新右键菜单
            self.update_context_menu()
            
        except Exception as e:
            print(f"更新界面文本失败: {e}")
    
    def update_panel_titles(self):
        """
        更新面板标题（文本和字体）
        """
        try:
            # 获取面板标题标签并更新
            panels = ['file_list', 'file_info']
            if MODEL_INFERENCE_AVAILABLE:
                panels.append('inference_results')
            
            for panel_id in panels:
                # 直接使用存储的标题标签引用更新文本和字体
                title_label = getattr(self, f'{panel_id}_title_label', None)
                if title_label:
                    # 更新标题文本
                    title_label.config(text=self.texts[panel_id])
                    # 更新标题字体（语言切换时字体族可能改变）
                    if hasattr(self, 'fonts') and 'title_font' in self.fonts:
                        title_label.config(font=self.fonts['title_font'])
                    print(f"已更新 {panel_id} 面板标题: {self.texts[panel_id]}")
        except Exception as e:
            print(f"更新面板标题失败: {e}")
    
    def update_context_menu(self):
        """
        更新右键菜单文本
        """
        try:
            if hasattr(self, 'results_context_menu'):
                # 删除旧菜单项
                self.results_context_menu.delete(0, 'end')
                
                # 重新添加菜单项
                self.results_context_menu.add_command(label=self.texts['copy_all'], command=self.copy_all_results)
                self.results_context_menu.add_command(label=self.texts['copy_selected'], command=self.copy_selected_results)
                self.results_context_menu.add_separator()
                self.results_context_menu.add_command(label=self.texts['clear_results'], command=self.clear_inference_results)
                self.results_context_menu.add_separator()
                self.results_context_menu.add_command(label=self.texts['expand_panel'], command=lambda: self.expand_panel_for_viewing('inference_results'))
        except Exception as e:
            print(f"更新右键菜单失败: {e}")
    
    def update_chart_titles(self):
        """
        更新图表标题
        """
        try:
            if hasattr(self, 'fig'):
                # 不设置总标题，保持简洁
                # chart_title = self.texts['signal_analysis_charts']
                # self.fig.suptitle(chart_title)
                
                # 重新显示信号以更新子图标题
                if self.current_signal is not None:
                    self.display_signal()
                else:
                    self.init_plots()
                    
        except Exception as e:
            print(f"更新图表标题失败: {e}")
    
    def update_chart_titles_on_language_change(self):
        """
        语言切换时专门更新图表标题
        """
        try:
            if hasattr(self, 'fig') and hasattr(self, 'canvas'):
                # 不设置总标题，保持简洁
                # chart_title = self.texts['signal_analysis_charts']
                # self.fig.suptitle(chart_title)
                
                # 总是重新初始化图表以更新子图标题
                if hasattr(self, 'axes'):
                    # 清空所有子图
                    for ax in self.axes:
                        ax.clear()
                    
                    if self.is_3d_mode:
                        # 3D模式：2个子图
                        self.axes[0].set_title(self.texts['power_spectral_density_3d'])
                        self.axes[1].set_title(self.texts['filtered_psd_3d'])
                    else:
                        # 2D模式：4个子图
                        self.axes[0].set_title(self.texts['time_domain_points'])
                        self.axes[1].set_title(self.texts['time_domain_signal'])
                        self.axes[2].set_title(self.texts['frequency_spectrum'])
                        self.axes[3].set_title(self.texts['time_frequency'])
                    
                    # 调整布局并重绘
                    plt.tight_layout()
                    self.canvas.draw()
                
                # 如果有数据，重新显示
                if self.current_signal is not None:
                    self.display_signal()
                    
        except Exception as e:
            print(f"语言切换时更新图表标题失败: {e}")
    
    def init_plots(self):
        """
        初始化图表显示
        
        清空所有子图并设置中文标题，为后续数据显示做准备
        根据当前视图模式显示不同的子图
        """
        # 清空所有子图
        for ax in self.axes:
            ax.clear()
        
        if self.is_3d_mode:
            # 3D模式：2个子图
            self.axes[0].set_title(self.texts['power_spectral_density_3d'])  # 功率谱密度图 3D
            self.axes[1].set_title(self.texts['filtered_psd_3d'])  # 滤波后功率谱密度图 3D
        else:
            # 2D模式：4个子图
            self.axes[0].set_title(self.texts['time_domain_points'])  # 时域信号点值图
            self.axes[1].set_title(self.texts['time_domain_signal'])  # 时域信号图
            self.axes[2].set_title(self.texts['frequency_spectrum'])  # 频谱图
            self.axes[3].set_title(self.texts['time_frequency'])  # 时频图
        
        # 调整子图布局并刷新画布
        plt.tight_layout()
        self.canvas.draw()

    def enhanced_file_dialog(self, dialog_type, title, filetypes=None, **kwargs):
        """增强的文件对话框，支持合适的显示尺寸"""
        import platform

        # 设置初始目录为上次选择的目录
        if 'initialdir' not in kwargs:
            import os
            kwargs['initialdir'] = getattr(self, 'last_selected_dir', os.getcwd())

        if platform.system() == 'Linux':
            # 在Linux下使用zenity创建可控尺寸的文件选择对话框
            try:
                import subprocess
                import os

                # 设置初始目录
                initial_dir = kwargs.get('initialdir', os.getcwd())

                if dialog_type == 'open':
                    # 构建zenity文件选择命令
                    cmd = [
                        'zenity', '--file-selection',
                        '--title=' + title,
                        '--width=700',
                        '--height=500'
                    ]

                    # 设置初始目录，确保路径以/结尾
                    if initial_dir and os.path.exists(initial_dir):
                        if not initial_dir.endswith('/'):
                            initial_dir += '/'
                        cmd.append('--filename=' + initial_dir)
                    
                    if filetypes:
                        for name, pattern in filetypes:
                            if pattern != "*.*":
                                patterns = pattern.split()
                                filter_str = f'{name} | ' + ' '.join(patterns)
                                cmd.extend(['--file-filter=' + filter_str])

                    # 执行zenity命令
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        selected_path = result.stdout.strip()
                        # 更新上次选择的目录
                        self.last_selected_dir = os.path.dirname(selected_path)
                        return selected_path
                    else:
                        return None

                elif dialog_type == 'directory':
                    # 构建zenity目录选择命令
                    cmd = [
                        'zenity', '--file-selection',
                        '--directory',
                        '--title=' + title,
                        '--width=700',
                        '--height=500'
                    ]

                    # 设置初始目录，确保路径以/结尾
                    if initial_dir and os.path.exists(initial_dir):
                        if not initial_dir.endswith('/'):
                            initial_dir += '/'
                        cmd.append('--filename=' + initial_dir)

                    # 执行zenity命令
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        selected_path = result.stdout.strip()
                        # 更新上次选择的目录
                        self.last_selected_dir = selected_path
                        return selected_path
                    else:
                        return None
                else:
                    return None

            except Exception as e:
                print(f"zenity文件对话框错误: {e}")
                # 如果zenity失败，回退到标准对话框
                if dialog_type == 'open':
                    return filedialog.askopenfilename(
                        title=title,
                        filetypes=filetypes or [("所有文件", "*.*")],
                        **kwargs
                    )
                elif dialog_type == 'directory':
                    return filedialog.askdirectory(
                        title=title,
                        **kwargs
                    )
        else:
            # Windows和其他系统使用标准对话框
            if dialog_type == 'open':
                return filedialog.askopenfilename(
                    title=title,
                    filetypes=filetypes or [("所有文件", "*.*")],
                    **kwargs
                )
            elif dialog_type == 'directory':
                return filedialog.askdirectory(
                    title=title,
                    **kwargs
                )

    def select_file(self):
        """
        选择单个数据文件

        弹出文件选择对话框，让用户选择一个数据文件进行分析
        支持的格式：.dat、.bvsp、.hdfv
        """
        # 设置对话框标题和文件类型
        select_file_dialog_title = "选择数据文件" if self.current_language == 'zh' else "Select Data File"
        supported_formats_text = "支持的格式" if self.current_language == 'zh' else "Supported Formats"
        all_files_text = "所有文件" if self.current_language == 'zh' else "All Files"

        # 打开文件选择对话框
        filename = self.enhanced_file_dialog(
            'open',
            title=select_file_dialog_title,
            filetypes=[(supported_formats_text, "*.dat *.bvsp *.hdfv"), (all_files_text, "*.*")]
        )
        
        # 如果用户选择了文件
        if filename:
            self.current_files = [filename]  # 设置当前文件列表
            self.current_index = 0           # 重置索引
            self.update_file_list()          # 更新文件列表显示
            self.load_current_file()         # 加载选中的文件
    
    def select_folder(self):
        """
        选择文件夹批量加载数据文件
        
        弹出文件夹选择对话框，扫描文件夹中所有支持格式的数据文件
        并将它们加载到文件列表中供用户浏览
        """
        # 设置文件夹选择对话框标题
        folder_dialog_title = "选择文件夹" if self.current_language == 'zh' else "Select Folder"
        folder = self.enhanced_file_dialog('directory', title=folder_dialog_title)
        
        if folder:
            # 定义支持的文件扩展名
            extensions = ['.dat', '.bvsp', '.hdfv']
            files = []
            
            # 递归搜索文件夹中的所有支持格式文件
            for root, dirs, filenames in os.walk(folder):
                for filename in filenames:
                    if any(filename.lower().endswith(ext) for ext in extensions):
                        files.append(os.path.join(root, filename))
            
            # 如果找到了支持的文件
            if files:
                self.current_files = sorted(files)  # 按文件名排序
                self.current_index = 0              # 重置索引到第一个文件
                self.update_file_list()             # 更新文件列表显示
                self.load_current_file()            # 加载第一个文件
            else:
                # 如果没有找到支持的文件，显示警告
                ScaledMessageBox.showwarning(self.texts['warning'], self.texts['no_files_found'], 
                                            parent=self.root, language=self.current_language)
    
    def update_file_list(self):
        """
        更新文件列表显示
        
        刷新左侧文件列表框的内容，显示当前加载的所有文件
        当前选中的文件会用 >>> 标记，并更新状态栏信息
        """
        # 清空文件列表框
        self.file_listbox.delete(0, tk.END)
        
        # 遍历所有文件，添加到列表框中
        for i, file_path in enumerate(self.current_files):
            filename = os.path.basename(file_path)  # 只显示文件名，不显示完整路径
            marker = ">>> " if i == self.current_index else "    "  # 当前文件用箭头标记
            self.file_listbox.insert(tk.END, f"{marker}{filename}")
        
        # 更新选中状态和状态栏
        if self.current_files:
            self.file_listbox.selection_set(self.current_index)  # 设置列表框选中项
            # 更新状态栏显示当前文件索引
            self.status_label.config(text=f"{self.texts['file_unit']} {self.current_index + 1} / {len(self.current_files)}")
    
    def update_file_markers_only(self, old_index, new_index):
        """
        只更新文件列表中的选择标记，保持滚动位置
        
        这个函数只修改相关行的文本，不重建整个列表，从而保持用户的滚动位置
        
        参数:
            old_index (int): 之前选中的文件索引
            new_index (int): 新选中的文件索引
        """
        try:
            # 移除旧的标记（如果索引有效）
            if 0 <= old_index < len(self.current_files):
                old_filename = os.path.basename(self.current_files[old_index])
                old_text_without_marker = f"    {old_filename}"
                self.file_listbox.delete(old_index)
                self.file_listbox.insert(old_index, old_text_without_marker)
            
            # 添加新的标记（如果索引有效）
            if 0 <= new_index < len(self.current_files):
                new_filename = os.path.basename(self.current_files[new_index])
                new_text_with_marker = f">>> {new_filename}"
                self.file_listbox.delete(new_index)
                self.file_listbox.insert(new_index, new_text_with_marker)
                
                # 更新选中状态（但不会影响滚动位置）
                self.file_listbox.selection_clear(0, tk.END)
                self.file_listbox.selection_set(new_index)
                
                # 确保选中的项可见（轻柔滚动到可见区域，不跳到顶部）
                self.file_listbox.see(new_index)
            
            # 更新状态栏
            if self.current_files:
                self.status_label.config(text=f"{self.texts['file_unit']} {self.current_index + 1} / {len(self.current_files)}")
                
        except Exception as e:
            print(f"更新文件标记失败: {e}")
            # 如果轻量级更新失败，回退到完整更新
            self.update_file_list()
    
    def prev_file(self):
        """
        切换到上一个文件
        
        如果当前不是第一个文件，则切换到上一个文件并重新加载显示
        """
        if self.current_files and self.current_index > 0:
            old_index = self.current_index
            self.current_index -= 1  # 索引减1
            self.update_file_markers_only(old_index, self.current_index)  # 只更新标记，保持滚动位置
            self.load_current_file() # 加载新的当前文件
    
    def next_file(self):
        """
        切换到下一个文件
        
        如果当前不是最后一个文件，则切换到下一个文件并重新加载显示
        """
        if self.current_files and self.current_index < len(self.current_files) - 1:
            old_index = self.current_index
            self.current_index += 1  # 索引加1
            self.update_file_markers_only(old_index, self.current_index)  # 只更新标记，保持滚动位置
            self.load_current_file() # 加载新的当前文件
    
    def on_file_select(self, event):
        """
        处理文件列表选择事件
        
        当用户在文件列表框中点击选择文件时触发
        
        参数:
            event: tkinter事件对象
        """
        selection = self.file_listbox.curselection()  # 获取选中的索引
        if selection:
            new_index = selection[0]
            if new_index != self.current_index:  # 只有当索引真正改变时才处理
                old_index = self.current_index
                self.current_index = new_index  # 更新当前文件索引
                self.update_file_markers_only(old_index, new_index)  # 只更新标记，保持滚动位置
                self.load_current_file()           # 加载选中的文件
    
    def load_current_file(self):
        """
        加载当前选中的文件
        
        这是文件加载的主入口函数，负责协调整个加载过程：
        1. 更新状态显示为"正在加载"
        2. 调用具体的数据加载函数
        3. 更新信号显示和文件信息
        4. 处理加载过程中的异常
        """
        if not self.current_files:
            return
        
        # 获取当前文件路径
        current_file = self.current_files[self.current_index]
        
        # 更新状态栏显示正在加载
        loading_text = f"{self.texts['loading']}: {os.path.basename(current_file)}"
        self.status_label.config(text=loading_text)
        
        try:
            # 尝试加载文件数据
            self.load_signal_data(current_file)   # 加载信号数据
            self.display_signal()                 # 显示信号图表
            self.display_file_info(current_file)  # 显示文件信息
            
            # 更新状态栏显示加载完成
            loaded_text = f"{self.texts['loaded']}: {os.path.basename(current_file)}"
            self.status_label.config(text=loaded_text)
            
        except Exception as e:
            # 如果加载失败，显示错误信息
            error_msg = f"{self.texts['load_file_failed']}: {str(e)}"
            ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                      parent=self.root, language=self.current_language)
            self.status_label.config(text=self.texts['load_failed'])
    
    def load_signal_data(self, file_path):
        """
        加载信号数据文件并解析元数据
        
        该方法是数据加载的核心实现，支持多种格式的信号数据文件。
        加载完成后会设置 self.current_signal 和 self.current_metadata。
        
        支持的文件格式：
        - .bvsp/.dat: 使用 Read_sigfile 函数解析，与项目其他模块保持一致
        - .hdfv: HDF5 格式，使用 read_signal_from_hdf5 函数解析
        
        数据处理流程：
        1. 根据文件扩展名选择对应的解析函数
        2. 提取信号数据（转换为复数格式）
        3. 提取并标准化元数据（采样率、中心频率、带宽）
        4. 如果加载失败，自动回退到模拟数据
        
        Args:
            file_path (str): 数据文件的完整路径
            
        Raises:
            ValueError: 不支持的文件格式
            Exception: 文件读取或解析失败
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in ['.bvsp', '.dat']:
                # 使用与testcase_on_tinydataset.py相同的Read_sigfile函数
                (rxDatas, samp_rate, center_freq, bandwidth) = Read_sigfile(file_path, [0, -1])
                
                # 转换信号数据格式（与testcase_on_tinydataset.py保持一致）
                chan_id = 0
                self.current_signal = rxDatas[chan_id,:,0]+1j*rxDatas[chan_id,:,1]  # 转换为复数形式
                
                # 调试输出：显示加载的元数据
                print(f"文件加载调试信息:")
                print(f"  文件: {file_path}")
                print(f"  采样率: {samp_rate/1e6:.1f} MHz")
                print(f"  中心频率: {center_freq/1e6:.1f} MHz")
                print(f"  带宽: {bandwidth/1e6:.1f} MHz")
                print(f"  信号长度: {len(self.current_signal)}")
                print(f"  MIMO天线数: {rxDatas.shape[0]}")
                
                # 设置信号元数据（与testcase_on_tinydataset.py保持一致）
                self.current_metadata = {
                    'fs': float(samp_rate),
                    'fc': float(center_freq), 
                    'bw': float(bandwidth)
                }
                
            elif file_ext == '.hdfv':
                # HDF5格式文件 - 使用read_signal_from_hdf5函数
                samples, metadata = read_signal_from_hdf5(file_path)
                
                # 设置信号数据
                self.current_signal = samples  # HDF5中的信号已经是复数格式
                
                # 从metadata中提取信息（与mainproc.py保持一致）
                samp_rate = metadata['fs']
                center_freq = metadata['fc']
                bandwidth = metadata['bw']
                
                # 调试输出：显示加载的元数据
                print(f"文件加载调试信息:")
                print(f"  文件: {file_path}")
                print(f"  采样率: {samp_rate/1e6:.1f} MHz")
                print(f"  中心频率: {center_freq/1e6:.1f} MHz")
                print(f"  带宽: {bandwidth/1e6:.1f} MHz")
                print(f"  信号长度: {len(self.current_signal)}")
                print(f"  信号类型: {type(self.current_signal)}")
                print(f"  时间戳: {metadata.get('timestamp', '未记录')}")
                
                # 设置信号元数据（与其他格式保持一致）
                self.current_metadata = {
                    'fs': float(samp_rate),
                    'fc': float(center_freq), 
                    'bw': float(bandwidth)
                }
                
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")
                
        except Exception as e:
            print(f"加载文件失败: {e}")
            # 如果加载失败，使用模拟数据作为备选
            self.load_fallback_data()
    

    
    def load_fallback_data(self):
        """
        加载备选模拟数据（当真实文件加载失败时使用）
        """
        print("使用模拟数据作为备选...")
        
        # 模拟数据参数
        fs = 1e6        # 采样率 1MHz
        duration = 0.01 # 持续时间 10ms
        t = np.arange(0, duration, 1/fs)  # 时间轴
        
        # 生成双频测试信号
        freq1 = 100e3   # 第一个频率 100kHz
        freq2 = 200e3   # 第二个频率 200kHz
        
        # 创建双频正弦信号
        signal = (np.sin(2 * np.pi * freq1 * t) + 0.5 * np.sin(2 * np.pi * freq2 * t))
        
        # 添加噪声
        noise = 0.1 * np.random.randn(len(t))
        
        # 创建复数信号（I + jQ）
        self.current_signal = signal + noise + 1j * (0.5 * signal + 0.1 * np.random.randn(len(t)))
        
        # 设置信号元数据
        self.current_metadata = {'fs': fs, 'fc': 2.4e9, 'bw': fs}
    
    def display_signal(self):
        """
        根据当前视图模式显示信号
        """
        if self.current_signal is None:
            return
        
        try:
            if self.is_3d_mode:
                self.display_signal_3d()
            else:
                self.display_signal_2d()
        except Exception as e:
            print(f"显示信号失败: {e}")
            self.display_signal_fallback()
    
    def create_smart_sampling_indices(self, N_sig, max_points):
        """
        创建智能抽样索引，确保包含完整数据范围
        
        参数:
            N_sig (int): 原始信号长度
            max_points (int): 最大显示点数
            
        返回:
            tuple: (indices, step) 抽样索引数组和步长
        """
        if N_sig <= max_points:
            return np.arange(N_sig), 1
        
        # 计算基本抽样步长
        step = N_sig // max_points
        
        # 确保包含首尾数据点的智能抽样
        # 强制包含第一个和最后一个数据点
        indices = np.arange(0, N_sig, step)
        
        # 如果最后一个索引不是N_sig-1，则添加最后一个点
        if indices[-1] != N_sig - 1:
            indices = np.append(indices, N_sig - 1)
        
        return indices, step

    def compute_fft_async(self, sig, fs, fc):
        """
        异步计算 FFT 频谱，采用 MATLAB 兼容的分段平均方法
        
        该方法实现了自适应的 FFT 计算策略，根据信号长度选择最优的处理方式：
        
        计算策略：
        - ≤100万点：直接 FFT 计算，保持完整精度
        - 100万-400万点：智能抽样 FFT，保持频谱完整性
        - >400万点：分段平均 FFT（类似 MATLAB 的 spectrogram）
        
        技术实现：
        - 使用 Blackman 窗函数，与 MATLAB 保持一致
        - 50% 重叠的分段处理，减少频谱泄漏
        - 64K FFT 长度，平衡分辨率和计算量
        - 严格按 MATLAB 方式计算频率轴
        
        频率轴计算：
        nb_freqs = fc - fs/2 + (0:N-1) * fs/N  # MATLAB 方式
        
        Args:
            sig (np.ndarray): 输入信号（复数）
            fs (float): 采样率 (Hz)
            fc (float): 中心频率 (Hz)
            
        Returns:
            Future: 包含 (freqs_mhz, fftdata) 的 Future 对象
        """
        def _compute():
            def compute_direct_fft(sig, fs, fc):
                """直接FFT计算（小数据量）"""
                sig_len = len(sig)
                fftdata = np.fft.fftshift(np.abs(np.fft.fft(sig)) / sig_len)
                
                # 生成频率轴
                freq_indices = np.arange(sig_len, dtype=np.float64)
                nb_freqs = fc - fs/2.0 + freq_indices * (fs / sig_len)
                freqs_mhz = nb_freqs / 1e6
                
                return fftdata, freqs_mhz
            
            def compute_smart_sampling_fft(sig, fs, fc):
                """智能抽样FFT（中等数据量）"""
                original_len = len(sig)
                target_len = 1048576  # 目标100万点
                step = max(1, original_len // target_len)
                
                # 均匀抽样，确保覆盖整个信号
                sampled_sig = sig[::step]
                print(f"  智能抽样: {len(sampled_sig):,}/{original_len:,} 点，步长={step}")
                
                # 对抽样信号进行FFT
                sig_len = len(sampled_sig)
                fftdata = np.fft.fftshift(np.abs(np.fft.fft(sampled_sig)) / sig_len)
                
                # 频率轴计算（基于原始采样率）
                freq_indices = np.arange(sig_len, dtype=np.float64)
                nb_freqs = fc - fs/2.0 + freq_indices * (fs / sig_len)
                freqs_mhz = nb_freqs / 1e6
                
                return fftdata, freqs_mhz
            
            def compute_welch_like_fft(sig, fs, fc):
                """分段平均FFT（MATLAB风格，大数据量）"""
                # MATLAB风格的分段参数
                fft_len = 65536  # 64K FFT长度，平衡分辨率和计算量
                overlap = fft_len // 2  # 50%重叠
                window = np.blackman(fft_len)  # 与MATLAB一致的窗函数
                
                # 计算分段数量
                step = fft_len - overlap
                num_segments = (len(sig) - overlap) // step
                print(f"  分段参数: FFT长度={fft_len:,}，重叠={overlap:,}，分段数={num_segments}")
                
                if num_segments <= 0:
                    # 回退到直接FFT
                    return compute_direct_fft(sig, fs, fc)
                
                # 分段FFT并平均
                accumulated_fft = np.zeros(fft_len, dtype=np.complex128)
                valid_segments = 0
                
                for i in range(num_segments):
                    start_idx = i * step
                    end_idx = start_idx + fft_len
                    
                    if end_idx <= len(sig):
                        # 提取分段并加窗
                        segment = sig[start_idx:end_idx] * window
                        # FFT计算
                        segment_fft = np.fft.fft(segment)
                        accumulated_fft += segment_fft
                        valid_segments += 1
                
                # 平均并归一化
                if valid_segments > 0:
                    averaged_fft = accumulated_fft / valid_segments
                    fftdata = np.fft.fftshift(np.abs(averaged_fft) / fft_len)
                else:
                    # 回退处理
                    return compute_smart_sampling_fft(sig, fs, fc)
                
                # 频率轴计算
                freq_indices = np.arange(fft_len, dtype=np.float64) 
                nb_freqs = fc - fs/2.0 + freq_indices * (fs / fft_len)
                freqs_mhz = nb_freqs / 1e6
                
                print(f"  有效分段: {valid_segments}/{num_segments}")
                return fftdata, freqs_mhz
            
            original_len = len(sig)
            
            # 方案选择：根据信号长度选择最优处理方式
            if original_len <= 1048576:  # <=100万点：直接FFT
                print(f"🔧 FFT策略: 直接计算 ({original_len} 点)")
                fftdata, freqs_mhz = compute_direct_fft(sig, fs, fc)
                coverage_info = "完整数据"
            
            elif original_len <= 4194304:  # 100万-400万点：智能抽样FFT  
                print(f"🔧 FFT策略: 智能抽样 ({original_len} 点)")
                fftdata, freqs_mhz = compute_smart_sampling_fft(sig, fs, fc)
                coverage_info = "智能抽样，保持完整频谱"
            
            else:  # >400万点：分段平均FFT（MATLAB方式）
                print(f"🔧 FFT策略: 分段平均FFT ({original_len} 点)")
                fftdata, freqs_mhz = compute_welch_like_fft(sig, fs, fc)
                coverage_info = "分段平均，完整覆盖"
            
            # 统一的调试输出
            print(f"📊 频谱计算完成:")
            print(f"  处理策略: {coverage_info}")
            print(f"  原始长度: {original_len:,} 点") 
            print(f"  采样率: {fs/1e6:.3f} MHz")
            print(f"  中心频率: {fc/1e6:.3f} MHz")
            print(f"  频率范围: {freqs_mhz[0]:.3f} ~ {freqs_mhz[-1]:.3f} MHz")
            print(f"  频率分辨率: {(freqs_mhz[1] - freqs_mhz[0]):.6f} MHz")
            print(f"  有效频谱点: {len(fftdata):,}")
            
            return freqs_mhz, fftdata
        
        return self.thread_pool.submit(_compute)

    def compute_spectrogram_async(self, sig, fs, fc):
        """
        异步计算时频图，严格按照 MATLAB 的计算方式
        
        该方法实现了与 MATLAB spectrogram 函数兼容的时频分析：
        
        MATLAB 对应代码：
        [S,F,T,P] = spectrogram(signal, window, noverlap, nfft, fs, 'yaxis');
        F = (-window/2:1:window/2-1)*fs/window + fc;
        P_dB = 10*log10(circshift(P, window/2));
        
        计算参数：
        - 窗口大小：4096 点（自适应调整）
        - 重叠：50%（2048 点）
        - FFT 长度：与窗口大小相同
        - 窗函数：Hann 窗
        
        性能优化：
        - 超过 200 万点时自动抽样至 100 万点
        - 保持时频分辨率的前提下优化计算速度
        - 时间轴根据原始信号长度进行缩放
        
        频率轴处理：
        - 按 MATLAB 方式重新计算频率轴
        - 使用 circshift 进行频域移位
        - 转换为 dB 刻度显示
        
        Args:
            sig (np.ndarray): 输入信号（复数）
            fs (float): 采样率 (Hz)
            fc (float): 中心频率 (Hz)
            
        Returns:
            Future: 包含 (F_mhz, T_ms, Pxx_db, success) 的 Future 对象
        """
        def _compute():
            # 性能优化：对于极大的信号，合理控制数据量，但保持时频分辨率
            stft_sig = sig
            if len(sig) > 2097152:  # 超过200万点时进行优化
                # 使用抽样策略，但确保保持信号特征
                step = len(sig) // 1048576  # 目标约100万点
                stft_sig = sig[::step]
                print(f"时频图优化: 抽样至 {len(stft_sig)}/{len(sig)} 点，步长={step}")
            
            # 严格按照MATLAB的时频图计算
            # MATLAB参数设置
            window = 4096
            noverlap = window // 2
            nfft = window
            
            # 数据长度检查，如果数据太少则调整窗口大小
            if len(stft_sig) < window * 2:
                if len(stft_sig) > 2048:
                    window = 1024
                elif len(stft_sig) > 1024:
                    window = 512
                else:
                    window = 256
                noverlap = window // 2
                nfft = window
                print(f"时频图: 调整窗口大小为 {window}")
            
            try:
                # 使用scipy的spectrogram函数，对应MATLAB的spectrogram
                # MATLAB: [S,F,T,P]=spectrogram(a_channel,window,noverlap,nfft,wb_fs,'yaxis');
                f_spec, t_spec, Pxx = spectrogram(stft_sig, fs=fs, window='hann', 
                                                 nperseg=window, noverlap=noverlap, 
                                                 nfft=nfft, return_onesided=False)
                
                # MATLAB中重新计算频率轴，不使用spectrogram返回的f_spec：
                # F = (-window/2:1:window/2-1)*wb_fs/window+wb_fc;
                # F = F/1e6;%转换为M
                F = (np.arange(-window//2, window//2) * fs / window + fc) / 1e6  # MHz
                
                # 时间轴需要根据原始信号长度进行调整
                if len(stft_sig) != len(sig):
                    # 如果使用了抽样，需要调整时间轴到原始信号的时间范围
                    time_scale = len(sig) / len(stft_sig)
                    T = t_spec * 1000 * time_scale  # 调整时间轴
                else:
                    T = t_spec * 1000  # ms
                
                # MATLAB功率计算：10*log10(circshift(P,window/2))
                # 注意：MATLAB代码中写的是circshift(P,2048)，但应该是window/2
                Pxx_shifted = np.roll(Pxx, window//2, axis=0)  # 频域移位
                Pxx_db = 10 * np.log10(Pxx_shifted + 1e-10)    # dB转换
                
                return F, T, Pxx_db, True  # 成功标志
                
            except Exception as e:
                print(f"时频图计算失败: {e}")
                return None, None, np.abs(stft_sig), False  # 失败时返回简化数据
        
        return self.thread_pool.submit(_compute)

    def display_signal_2d(self):
        """
        显示 2D 信号分析图表（多线程优化版本）
        
        该方法实现了四种信号分析图表的并行计算和显示：
        1. 时域信号点值图 - 支持滚轮缩放交互
        2. 时域信号图 - 以时间为 X 轴的连续信号波形
        3. 频谱图 - FFT 频域分析，严格按 MATLAB 方式计算
        4. 时频图 - STFT 时频域分析，支持动态参数调整
        
        技术特性：
        - 多线程并行计算 FFT 和时频图，避免界面卡顿
        - 智能抽样优化大数据显示性能（最大 10 万点显示）
        - X 轴范围精确控制，消除留白
        - 实时状态更新和进度显示
        - MATLAB 兼容的频率轴计算和时频图生成
        - 自适应 Y 轴范围，优先使用整百刻度
        
        性能优化：
        - 对于大于 100 万点的信号，使用智能抽样
        - FFT 计算采用分段平均方法（类似 MATLAB 的 spectrogram）
        - 异步计算，主界面保持响应
        """
        if self.is_computing:
            return  # 如果正在计算，避免重复启动
        
        # 重置缩放相关变量（新数据加载时）
        self.time_plot_zoom_level = 1.0
        self.time_plot_center = 0.5
        self.time_plot_original_xlim = None
        self.spectrum_plot_zoom_level = 1.0
        self.spectrum_plot_center = 0.5
        self.spectrum_plot_original_xlim = None
        
        self.is_computing = True
        
        try:
            # 更新状态显示
            self.status_label.config(text=f"{self.texts['preparing_data']}...")
            self.root.update()
            
            # 清空所有子图
            for ax in self.axes:
                ax.clear()
            
            # 获取信号数据和元数据
            sig = self.current_signal
            fs = self.current_metadata.get('fs', 1e6)
            fc = self.current_metadata.get('fc', 2.4e9)
            
            signal_i = np.real(sig)  # I路信号
            signal_q = np.imag(sig)  # Q路信号
            N_sig = len(sig)
            
            # 智能抽样：确保包含完整数据范围
            max_display_points = 100000  # 最大显示点数（10万点）
            indices, step = self.create_smart_sampling_indices(N_sig, max_display_points)
            
            # 抽样数据
            sig_display = sig[indices]
            signal_i_display = signal_i[indices]
            signal_q_display = signal_q[indices]
            N_display = len(indices)
            
            # 更新状态
            self.status_label.config(text=f"{self.texts['data_sampling_completed']}: {N_display}/{N_sig} {self.texts['points_unit']}")
            self.root.update()
            
            # 启动异步计算任务 - 使用原始信号确保准确性
            # 注意：频谱图和时频图必须使用原始信号，不能使用抽样数据
            fft_future = self.compute_fft_async(sig, fs, fc)  # 使用原始信号
            stft_future = self.compute_spectrogram_async(sig, fs, fc)  # 使用原始信号
            
            # 1. 时域信号点值图（立即绘制，无需等待）
            ax1 = self.axes[0]
            # X轴转换为1e5单位
            indices_1e5 = indices / 1e5
            ax1.plot(indices_1e5, signal_i_display, 'b-', linewidth=0.5)
            ax1.plot(indices_1e5, signal_q_display, 'r-', linewidth=0.5)
            ax1.set_title(self.texts['time_domain_points'])
            ax1.set_xlabel(self.texts['time_points_unit'])
            ax1.set_ylabel(self.texts['signal_voltage'])
            ax1.grid(True, alpha=0.3)
            
            # 设置y轴范围（自适应计算，优先整百或半百刻度）
            y_min, y_max = self.calculate_adaptive_ylim(signal_i_display, signal_q_display)
            ax1.set_ylim(y_min, y_max)
            
            # 设置X轴范围和刻度（以1e5为单位）
            x_max_1e5 = (N_sig - 1) / 1e5
            ax1.set_xlim(0, x_max_1e5)
            
            # 设置刻度（与MATLAB对齐：每0.5×10^5一个刻度）
            tick_step = 0.5  # MATLAB对齐：步长为0.5
            
            x_ticks = np.arange(0, x_max_1e5 + tick_step, tick_step)
            ax1.set_xticks(x_ticks)
            # 根据刻度值决定显示格式：整数显示为整数，小数显示为小数
            ax1.set_xticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in x_ticks])
            
            # 2. 时域信号图（立即绘制，无需等待）
            ax2 = self.axes[1]
            # 计算实际的时间轴（严格按照MATLAB的计算方式）
            # MATLAB: signal_time = (0:N_sig-1)*(1/wb_fs); signal_time_ms = signal_time*1000;
            signal_time_start = 0
            signal_time_end = (N_sig - 1) / fs * 1000  # 完整数据的时间范围(ms)
            signal_time_display = indices / fs * 1000  # 显示点对应的实际时间
            
            ax2.plot(signal_time_display, signal_i_display, 'b-', linewidth=0.5)
            ax2.plot(signal_time_display, signal_q_display, 'r-', linewidth=0.5)
            ax2.set_title(self.texts['time_domain_signal'])
            ax2.set_xlabel(self.texts['time_ms'])
            ax2.set_ylabel(self.texts['voltage_v'])
            ax2.grid(True, alpha=0.3)
            
            # 设置y轴范围
            y_min, y_max = self.calculate_adaptive_ylim(signal_i_display, signal_q_display)
            ax2.set_ylim(y_min, y_max)
            
            # 设置X轴范围为完整的时间范围
            ax2.set_xlim(signal_time_start, signal_time_end)
            
            # 设置时域信号图的x轴刻度
            if signal_time_end > 0:
                # 根据时间范围设置合适的刻度间隔
                if signal_time_end <= 5:
                    time_tick_step = 1  # 0-5ms：每1ms一个刻度
                elif signal_time_end <= 20:
                    time_tick_step = 2  # 5-20ms：每2ms一个刻度
                elif signal_time_end <= 50:
                    time_tick_step = 5  # 20-50ms：每5ms一个刻度
                else:
                    time_tick_step = 10  # >50ms：每10ms一个刻度
                
                time_ticks = np.arange(0, signal_time_end + time_tick_step, time_tick_step)
                ax2.set_xticks(time_ticks)
                ax2.set_xticklabels([f'{int(tick)}' for tick in time_ticks])
            
            # 更新状态
            self.status_label.config(text=f"{self.texts['time_plot_completed_calculating_spectrum']}...")
            self.root.update()
            
            # 等待FFT计算完成并绘制频谱图
            try:
                freqs_mhz, fftdata = fft_future.result(timeout=30)  # 30秒超时
                
                ax3 = self.axes[2]
                ax3.plot(freqs_mhz, fftdata, 'g-', linewidth=0.8)
                ax3.set_title(f'{self.texts["frequency_spectrum"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                ax3.set_xlabel(self.texts['frequency_mhz'])
                ax3.set_ylabel(self.texts['spectrum_value'])
                ax3.grid(True, alpha=0.3)
                
                # 消除X轴留白：精确设置频率范围
                ax3.set_xlim(freqs_mhz[0], freqs_mhz[-1])
                
                # MATLAB对齐：自适应设置X轴刻度，显示关键频率点
                key_freqs = self.find_adaptive_frequency_ticks(freqs_mhz, fftdata)
                
                if key_freqs:
                    ax3.set_xticks(key_freqs)
                    ax3.set_xticklabels([f'{f:.1f}' if f != int(f) else f'{int(f)}' for f in key_freqs])
                    print(f"频谱图显示关键频率点: {key_freqs}")
                
                self.status_label.config(text=f"{self.texts['spectrum_completed_calculating_timefreq']}...")
                self.root.update()
                
            except Exception as e:
                print(f"FFT计算超时或失败: {e}")
                ax3 = self.axes[2]
                ax3.text(0.5, 0.5, '频谱计算失败', ha='center', va='center', transform=ax3.transAxes)
                ax3.set_title('频谱图 (计算失败)')
            
            # 等待时频图计算完成并绘制
            try:
                F_mhz, T_ms, Pxx_db, success = stft_future.result(timeout=45)  # 45秒超时
                
                ax4 = self.axes[3]
                if success:
                    # 绘制时频图 - 严格按照MATLAB的方式
                    # MATLAB: surf(T*1000,F,10*log10(circshift(P,2048)),'edgecolor','none'); view(0,90);
                    # 其中 T*1000 是X轴(时间ms)，F是Y轴(频率MHz)
                    
                    # 设置显示范围：[X轴最小值, X轴最大值, Y轴最小值, Y轴最大值]
                    # 对应：[时间最小值, 时间最大值, 频率最小值, 频率最大值]
                    extent = [T_ms[0], T_ms[-1], F_mhz[0], F_mhz[-1]]
                    
                    # 使用imshow显示时频图
                    # 注意：Pxx_db的维度应该是[频率bins, 时间frames]，正好对应imshow的[行,列]
                    im = ax4.imshow(Pxx_db, aspect='auto', cmap='viridis', 
                                   extent=extent, origin='lower', 
                                   interpolation='bilinear')
                    
                    ax4.set_title(f'{self.texts["time_frequency"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                    ax4.set_xlabel(self.texts['time_ms'])  # X轴：时间(ms)
                    ax4.set_ylabel(self.texts['frequency_mhz'])  # Y轴：频率(MHz)
                    
                    # 设置轴范围 - 时间轴对应完整数据范围
                    if len(T_ms) > 1:
                        ax4.set_xlim(T_ms[0], T_ms[-1])
                    
                    # 频率轴范围
                    if len(F_mhz) > 1:
                        ax4.set_ylim(F_mhz[0], F_mhz[-1])
                    
                    # MATLAB对齐：设置自定义Y轴刻度
                    matlab_ticks = [2420, 2440, 2460]
                    valid_ticks = []
                    
                    for tick in matlab_ticks:
                        if F_mhz[0] <= tick <= F_mhz[-1]:
                            valid_ticks.append(tick)
                    
                    # 如果范围更大，添加更多刻度
                    if F_mhz[-1] > 2470:
                        if F_mhz[0] <= 2480 <= F_mhz[-1]:
                            valid_ticks.append(2480)
                    if F_mhz[0] < 2410:
                        if F_mhz[0] <= 2400 <= F_mhz[-1]:
                            valid_ticks.insert(0, 2400)
                    
                    if valid_ticks:
                        ax4.set_yticks(valid_ticks)
                        ax4.set_yticklabels([f'{tick}' for tick in valid_ticks])
                        print(f"时频图Y轴刻度设置: {valid_ticks}")
                    
                else:
                    # 绘制简化的幅度图
                    ax4.plot(Pxx_db, 'purple', linewidth=0.5)  # Pxx_db这时是信号幅度
                    ax4.set_title('信号幅度 (时频图计算失败)')
                    ax4.set_xlabel('采样点')
                    ax4.set_ylabel('幅度')
                    ax4.grid(True, alpha=0.3)
                    
                    # 消除X轴留白
                    if len(Pxx_db) > 1:
                        ax4.set_xlim(0, len(Pxx_db) - 1)
                
            except Exception as e:
                print(f"时频图计算超时或失败: {e}")
                ax4 = self.axes[3]
                ax4.text(0.5, 0.5, '时频图计算失败', ha='center', va='center', transform=ax4.transAxes)
                ax4.set_title('时频图 (计算失败)')
            
            # 最终绘制和状态更新
            self.status_label.config(text=f"{self.texts['completing_rendering']}...")
            self.root.update()
            
            # 调整布局
            plt.tight_layout()
            self.canvas.draw()
            
            # 完成状态更新 - 提供详细的数据范围信息
            time_range_ms = (N_sig - 1) / fs * 1000
            coverage_info = f"{self.texts['time_domain_range']}: 0-{N_sig-1}{self.texts['points_unit']}, 0-{time_range_ms:.1f}ms"
            display_info = f"{self.texts['display_text']}: {N_display}/{N_sig}{self.texts['points_unit']}"
            
            if N_sig > max_display_points:
                sampling_info = f"{self.texts['sampling_text']}: 1:{step}"
                self.status_label.config(text=f"{display_info}, {sampling_info}, {coverage_info}")
            else:
                self.status_label.config(text=f"{display_info}, {coverage_info}")
                
        except Exception as e:
            print(f"绘制过程出现错误: {e}")
            render_failed_text = "绘制失败" if self.current_language == 'zh' else "Rendering failed"
            self.status_label.config(text=render_failed_text)
            
        finally:
            self.is_computing = False
    
    def display_signal_3d(self):
        """
        显示 3D 信号分析图表（基于 PyTorch 的 STFT 计算）
        
        该方法实现了两个 3D 功率谱密度图表的显示，参考项目中的 func_scansig.py
        
        显示内容：
        1. 功率谱密度图 3D - 原始 STFT 功率谱的 3D 可视化
        2. 滤波后功率谱密度图 3D - 经过 FIR 滤波器处理的功率谱
        
        技术实现：
        - 使用 PyTorch 进行 STFT 计算，支持 GPU 加速
        - Blackman 窗函数，与项目其他模块保持一致
        - 自适应 FFT 长度（最大 2048 点）
        - 50% 重叠的短时傅里叶变换
        
        信号处理流程：
        1. PyTorch STFT 计算复数频谱
        2. 频谱重排（torch.roll）对齐零频率
        3. 计算功率谱密度（PSD）并转换为 dB
        4. FIR 低通滤波（48 阶，截止频率 0.05）
        5. 边缘裁剪去除滤波器过渡区域
        
        3D 可视化：
        - 使用 matplotlib 的 plot_surface
        - 不同视角展示（30°/45° 和 60°/135°）
        - viridis 和 plasma 颜色映射
        
        依赖要求：
        - 需要安装 torch 库
        - 如果 torch 不可用，显示错误信息并提示安装
        """
        # 检查torch是否可用
        if not TORCH_AVAILABLE:
            # 如果torch不可用，显示提示信息并回退到2D模式
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, '3D显示功能需要torch库支持\n请安装: pip install torch', 
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_title('3D功能不可用')
            
            plt.tight_layout()
            self.canvas.draw()
            
            # 自动切换回2D模式
            self.is_3d_mode = False
            self.view_button.config(text=self.texts['view_2d'])
            return
        
        # 清空所有子图
        for ax in self.axes:
            ax.clear()
        
        # 获取信号数据
        sig = self.current_signal
        fs = self.current_metadata.get('fs', 1e6)
        
        # 参考func_scansig.py进行STFT计算
        fft_len = min(2048, len(sig) // 4)
        if fft_len < 256:
            fft_len = 256
        
        try:
            # 使用PyTorch进行STFT（与func_scansig.py一致）
            complex_signal = torch.from_numpy(sig).to(torch.complex64)
            w_blackman = torch.blackman_window(fft_len, periodic=True)
            fft_overlap = fft_len // 2
            
            # 进行短时傅里叶变换
            stft_result = torch.stft(input=complex_signal, window=w_blackman, 
                                   n_fft=fft_len, hop_length=fft_overlap, 
                                   win_length=fft_len, normalized=False, 
                                   center=False, return_complex=True)
            
            # 重排频谱结果（与func_scansig.py一致）
            shifted_stft_result = torch.roll(stft_result, fft_overlap-1, dims=0)
            z_values = shifted_stft_result.numpy()
            
            # 计算功率谱密度
            zOut = np.abs(z_values) / np.float32(fft_len)
            psd_value_out = 20 * np.log10(zOut + 1e-10) - 56.8  # 与func_scansig.py一致
            
            # 频域滤波（简化版）
            M, N = psd_value_out.shape
            L_filter = 48
            b = firwin(L_filter+1, 0.05)
            psd_filtered = np.zeros((M, N))
            for i in range(N):
                psd_filtered[:, i] = lfilter(b, 1, psd_value_out[:, i])
            
            # 截取中心区域
            if fft_len == 2048:
                sc = 90
            elif fft_len == 1024:
                sc = 50
            else:
                sc = int(fft_len * 0.044)  # 约4.4%的边缘裁剪
            
            psd_filtered_cliped = psd_filtered[sc + L_filter // 2: -(sc - L_filter // 2), :]
            
            # 创建网格
            t = np.arange(N)
            f = np.arange(-fft_len // 2 + sc, fft_len // 2 - sc)
            T, F = np.meshgrid(t, f)
            
            # 1. 功率谱密度图 3D（参考func_scansig.py）
            ax1 = self.axes[0]
            psd_part = psd_value_out[sc: -sc, :]
            T_display, F_display = np.meshgrid(t, f)
            
            surf1 = ax1.plot_surface(T_display, F_display, psd_part, 
                                   cmap='viridis', edgecolor='none', alpha=0.8)
            ax1.set_title(self.texts['power_spectral_density_3d'])  # 功率谱密度图 3D
            ax1.set_xlabel(self.texts['time_frame'])  # 时间帧
            ax1.set_ylabel(self.texts['frequency_frame'])  # 频率帧
            ax1.set_zlabel(self.texts['power_db'])  # 功率(dB)
            ax1.view_init(elev=30, azim=45)  # 设置视角
            
            # 2. 滤波后功率谱密度图 3D
            ax2 = self.axes[1]
            T_filtered, F_filtered = np.meshgrid(t, np.arange(psd_filtered_cliped.shape[0]))
            
            surf2 = ax2.plot_surface(T_filtered, F_filtered, psd_filtered_cliped, 
                                   cmap='plasma', edgecolor='none', alpha=0.8)
            ax2.set_title(self.texts['filtered_psd_3d'])  # 滤波后功率谱密度图 3D
            ax2.set_xlabel(self.texts['time_frame'])  # 时间帧
            ax2.set_ylabel(self.texts['frequency_frame'])  # 频率帧
            ax2.set_zlabel(self.texts['power_db'])  # 功率(dB)
            ax2.view_init(elev=60, azim=135)  # 不同视角
            
        except Exception as e:
            print(f"3D显示计算失败: {e}")
            # 显示错误信息
            for ax in self.axes:
                ax.clear()
                ax.text(0.5, 0.5, f'3D显示计算失败:\n{str(e)}', 
                       ha='center', va='center', transform=ax.transAxes, fontsize=10)
                ax.set_title('3D显示失败')
        
        # 调整布局
        plt.tight_layout()
        self.canvas.draw()
    
    def display_signal_fallback(self):
        """
        显示信号的备用方案（简化版图表）
        
        当正常显示失败时使用的简化显示方案，根据当前视图模式显示
        """
        try:
            if self.current_signal is None:
                return
            
            # 重置缩放相关变量（备用方案也需要重置）
            self.time_plot_zoom_level = 1.0
            self.time_plot_center = 0.5
            self.time_plot_original_xlim = None
            self.spectrum_plot_zoom_level = 1.0
            self.spectrum_plot_center = 0.5
            self.spectrum_plot_original_xlim = None
            
            # 更新状态显示
            fallback_text = "使用备用显示方案" if self.current_language == 'zh' else "Using fallback display"
            self.status_label.config(text=f"{fallback_text}...")
            self.root.update()
            
            # 清空所有子图
            for ax in self.axes:
                ax.clear()
            
            sig = self.current_signal
            fs = self.current_metadata.get('fs', 1e6)
            fc = self.current_metadata.get('fc', 2.4e9)  # 添加中心频率
            
            # 性能优化：限制显示数据量
            max_points = 50000  # 备用方案使用更少的点数
            if len(sig) > max_points:
                step = len(sig) // max_points
                sig = sig[::step]
            
            if self.is_3d_mode:
                # 3D模式的备用方案：显示简化的2D图表
                ax1 = self.axes[0]
                
                # 限制FFT计算的数据量
                fft_sig = sig[:min(len(sig), 16384)]  # 最多使用16K点进行FFT
                
                # 严格按照MATLAB方式计算频谱
                sig_len = len(fft_sig)
                fft_data = np.fft.fftshift(np.abs(np.fft.fft(fft_sig)) / sig_len)
                nb_freqs = fc - fs/2 + np.arange(sig_len) * fs / sig_len
                freqs_mhz = nb_freqs / 1e6  # 转换为MHz
                
                ax1.plot(freqs_mhz, fft_data, linewidth=0.8)
                ax1.set_title(f'频谱图 [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                ax1.set_xlabel('频率(MHz)')
                ax1.set_ylabel('频谱值')
                ax1.grid(True, alpha=0.3)
                
                # 设置X轴范围
                ax1.set_xlim(freqs_mhz[0], freqs_mhz[-1])
                
                # MATLAB对齐：自适应设置X轴刻度（备用方案版本）
                key_freqs = self.find_adaptive_frequency_ticks(freqs_mhz, fft_data)
                
                if key_freqs:
                    ax1.set_xticks(key_freqs)
                    ax1.set_xticklabels([f'{f:.1f}' if f != int(f) else f'{int(f)}' for f in key_freqs])
                    print(f"频谱图(备用)显示关键频率点: {key_freqs}")
                
                ax2 = self.axes[1]
                ax2.plot(np.abs(sig), linewidth=0.8)
                ax2.set_title(self.texts['signal_amplitude'])
                ax2.set_xlabel(self.texts['sample_points'])
                ax2.set_ylabel(self.texts['amplitude'])
                ax2.grid(True, alpha=0.3)
            else:
                # 2D模式的备用方案：显示基本的4个图表
                signal_i = np.real(sig)
                signal_q = np.imag(sig)
                
                # 1. 时域信号点值图（备用方案，参考MATLAB）
                ax1 = self.axes[0]
                # 备用方案中也使用正确的X轴索引
                x_indices = np.arange(0, len(self.current_signal), step) if len(self.current_signal) > max_points else np.arange(len(sig))
                # X轴转换为1e5单位
                x_indices_1e5 = x_indices / 1e5
                ax1.plot(x_indices_1e5, signal_i, 'b-', linewidth=0.8)
                ax1.plot(x_indices_1e5, signal_q, 'r-', linewidth=0.8)
                ax1.set_title(self.texts['time_domain_points'])
                ax1.set_xlabel(self.texts['time_points_unit'])
                ax1.set_ylabel(self.texts['signal_voltage'])
                ax1.grid(True, alpha=0.3)
                
                # 设置y轴范围（自适应计算，优先整百或半百刻度）
                y_min, y_max = self.calculate_adaptive_ylim(signal_i, signal_q)
                ax1.set_ylim(y_min, y_max)
                
                # 设置X轴范围和刻度（以1e5为单位）
                x_max_1e5 = (len(self.current_signal) - 1) / 1e5
                ax1.set_xlim(0, x_max_1e5)
                
                # 设置刻度（与MATLAB对齐：每0.5×10^5一个刻度）
                tick_step = 0.5  # MATLAB对齐：步长为0.5
                
                x_ticks = np.arange(0, x_max_1e5 + tick_step, tick_step)
                ax1.set_xticks(x_ticks)
                # 根据刻度值决定显示格式：整数显示为整数，小数显示为小数
                ax1.set_xticklabels([f'{tick:.1f}' if tick != int(tick) else f'{int(tick)}' for tick in x_ticks])
                
                # 2. 时域信号图（备用方案，参考MATLAB）
                ax2 = self.axes[1]
                # 计算对应的实际时间
                if len(self.current_signal) > max_points:
                    actual_time_indices = np.arange(0, len(self.current_signal), step)
                    signal_time_ms = actual_time_indices / fs * 1000
                else:
                    signal_time_ms = np.arange(len(sig)) / fs * 1000
                
                ax2.plot(signal_time_ms, signal_i, 'b-', linewidth=0.8)
                ax2.plot(signal_time_ms, signal_q, 'r-', linewidth=0.8)
                ax2.set_title(self.texts['time_domain_signal'])
                ax2.set_xlabel(self.texts['time_ms'])
                ax2.set_ylabel(self.texts['voltage_v'])
                ax2.grid(True, alpha=0.3)
                
                # 设置y轴范围（自适应计算，优先整百或半百刻度）
                y_min, y_max = self.calculate_adaptive_ylim(signal_i, signal_q)
                ax2.set_ylim(y_min, y_max)
                
                # 设置完整的时间范围
                full_time_range = (len(self.current_signal) - 1) / fs * 1000
                ax2.set_xlim(0, full_time_range)
                
                # 设置时域信号图的x轴刻度（备用方案）
                if full_time_range > 0:
                    # 根据时间范围设置合适的刻度间隔
                    if full_time_range <= 5:
                        time_tick_step = 1  # 0-5ms：每1ms一个刻度
                    elif full_time_range <= 20:
                        time_tick_step = 2  # 5-20ms：每2ms一个刻度
                    elif full_time_range <= 50:
                        time_tick_step = 5  # 20-50ms：每5ms一个刻度
                    else:
                        time_tick_step = 10  # >50ms：每10ms一个刻度
                    
                    time_ticks = np.arange(0, full_time_range + time_tick_step, time_tick_step)
                    ax2.set_xticks(time_ticks)
                    ax2.set_xticklabels([f'{int(tick)}' for tick in time_ticks])
                
                # 3. 简单频谱图（按MATLAB方式）
                ax3 = self.axes[2]
                # 限制FFT计算的数据量
                fft_sig = sig[:min(len(sig), 16384)]
                
                # 严格按照MATLAB方式计算频谱
                # MATLAB: fftdata = fftshift(abs(fft(sig))/sig_len);
                # MATLAB: nb_freqs=wb_fc - wb_fs/2 +(0 : wb_samps - 1)*wb_fs/wb_samps;
                sig_len = len(fft_sig)
                fft_data = np.fft.fftshift(np.abs(np.fft.fft(fft_sig)) / sig_len)
                nb_freqs = fc - fs/2 + np.arange(sig_len) * fs / sig_len
                freqs_mhz = nb_freqs / 1e6  # 转换为MHz
                
                ax3.plot(freqs_mhz, fft_data, linewidth=0.8)
                ax3.set_title(f'{self.texts["frequency_spectrum"]} [fc= {fc/1e6:.1f}MHz  fs= {fs/1e6:.1f}MHz]')
                ax3.set_xlabel(self.texts['frequency_mhz'])
                ax3.set_ylabel(self.texts['spectrum_value'])
                ax3.grid(True, alpha=0.3)
                
                # 4. 信号幅度
                ax4 = self.axes[3]
                ax4.plot(np.abs(sig), linewidth=0.8)
                ax4.set_title(self.texts['signal_amplitude'])
                ax4.set_xlabel(self.texts['sample_points'])
                ax4.set_ylabel(self.texts['amplitude'])
                ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            self.canvas.draw()
            
            # 更新状态
            self.status_label.config(text=f"{self.texts['fallback_display_completed']} ({len(sig)} {self.texts['data_points_unit']})")
            
        except Exception as e:
            print(f"备用显示也失败: {e}")
            display_failed_text = "显示失败" if self.current_language == 'zh' else "Display failed"
            self.status_label.config(text=display_failed_text)
    
    def display_file_info(self, file_path):
        """
        显示当前文件的详细信息
        
        在左侧面板的文件信息区域显示当前文件的基本参数：
        - 文件路径和大小
        - 信号参数（采样率、中心频率等）
        - 信号长度和持续时间
        
        参数:
            file_path (str): 当前文件的完整路径
        """
        try:
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            # 从元数据中获取信号参数
            fs = self.current_metadata.get('fs', 0)  # 采样率
            fc = self.current_metadata.get('fc', 0)  # 中心频率
            
            # 构建信息文本
            info_text = f"{self.texts['file_path']}: {file_path}\n"
            info_text += f"{self.texts['file_size']}: {file_size / 1024 / 1024:.2f} MB\n"
            info_text += f"{self.texts['sample_rate']}: {fs / 1e6:.3f} MHz\n"
            info_text += f"{self.texts['center_frequency']}: {fc / 1e9:.3f} GHz\n"
            
            # 如果信号数据已加载，显示信号相关信息
            if self.current_signal is not None:
                info_text += f"{self.texts['signal_length']}: {len(self.current_signal)} {self.texts['samples_unit']}\n"
                info_text += f"{self.texts['duration']}: {len(self.current_signal) / fs * 1000:.2f} ms\n"
            
            # 更新文件信息显示区域
            self.info_text.delete(1.0, tk.END)  # 清空现有内容
            self.info_text.insert(tk.END, info_text)  # 插入新信息
            
        except Exception as e:
            print(f"显示文件信息失败: {e}")
    
    def load_classify_model_custom(self, model_path, class_def_path):
        """
        自定义模型加载函数，不依赖pathsetting.json
        
        参数:
            model_path (str): 模型文件路径(.pth)
            class_def_path (str): 类别定义文件路径(class_def.txt)
        
        返回:
            tuple: (Model, cls_count, cls_ids, cls_names)
        """
        if not MODEL_INFERENCE_AVAILABLE:
            raise ImportError("模型推理功能不可用")
        
        # 读取类别定义
        cls_ids, cls_names, cls_count = get_classes(class_def_path)
        print(f'无人机类别个数: {cls_count}')
        
        # 加载模型
        t1 = time.time()
        modeltype = 0  # 分类模型
        Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
        Init_model(Model, True, model_path)
        Model.eval()
        
        t2 = time.time()
        print(f"load model: {t2 - t1:.2f}s")
        
        return Model, cls_count, cls_ids, cls_names
    
    def select_class_def_file(self):
        """
        选择class_def.txt文件并更新界面状态
        
        返回:
            str: 选择的文件路径，如果取消则返回None
        """
        class_def_title = "选择类别定义文件" if self.current_language == 'zh' else "Select Class Definition File"
        class_def_format = "类别定义文件" if self.current_language == 'zh' else "Class Definition File"
        text_files = "文本文件" if self.current_language == 'zh' else "Text Files"
        all_files = "所有文件" if self.current_language == 'zh' else "All Files"
        
        # 打开文件选择对话框
        class_def_file = self.enhanced_file_dialog(
            'open',
            title=class_def_title,
            filetypes=[(class_def_format, "class_def.txt"), (text_files, "*.txt"), (all_files, "*.*")]
        )
        
        if class_def_file:
            # 更新类别定义文件路径
            self.class_def_path = class_def_file
            
            # 更新状态标签
            status_msg = f"已选择类别定义: {os.path.basename(class_def_file)}" if self.current_language == 'zh' else f"Class definition selected: {os.path.basename(class_def_file)}"
            self.status_label.config(text=status_msg)
            
            # 自动恢复原状态
            self.root.after(3000, lambda: self.status_label.config(text=self.texts.get('ready', '准备就绪')))
            
            print(f"已选择类别定义文件: {class_def_file}")
            
            # 如果有推理结果文本框，显示文件信息
            if hasattr(self, 'results_text'):
                try:
                    # 简单验证文件格式
                    class_data = self.parse_class_def_file(class_def_file)
                    if class_data:
                        self.results_text.config(state=tk.NORMAL)
                        self.results_text.delete('1.0', tk.END)
                        info_text = f"类别定义文件: {os.path.basename(class_def_file)}\n"
                        info_text += f"完整路径: {class_def_file}\n"
                        info_text += f"包含类别: {len(class_data)} 个\n\n"
                        info_text += "类别列表:\n"
                        for class_info in class_data[:10]:  # 最多显示10个
                            info_text += f"  {class_info['id']}: {class_info['name']}\n"
                        if len(class_data) > 10:
                            info_text += f"  ... 还有 {len(class_data) - 10} 个类别\n"
                        info_text += "\n点击'类别定义'按钮查看详细信息。"
                        self.results_text.insert(tk.END, info_text)
                        self.results_text.config(state=tk.DISABLED)
                        
                        # 自动展开推理结果面板
                        if hasattr(self, 'panel_collapsed') and self.panel_collapsed.get('inference_results', False):
                            self.toggle_panel_collapse('inference_results')
                except Exception as e:
                    print(f"预览类别定义文件失败: {e}")
        
        return class_def_file if class_def_file else None
    
    def select_model(self):
        """
        选择模型文件(.pth)
        """
        if not MODEL_INFERENCE_AVAILABLE:
            ScaledMessageBox.showwarning(self.texts['warning'], "模型推理功能不可用", 
                                        parent=self.root, language=self.current_language)
            return
        
        # 检查是否已选择类别定义文件
        if self.class_def_path is None or not os.path.exists(self.class_def_path):
            # 询问用户是否要先选择类别定义文件
            if self.current_language == 'zh':
                ask_title = "需要先选择类别定义文件"
                ask_msg = "选择模型前需要先选择类别定义文件(class_def.txt)，\n该文件定义了模型可以识别的类别信息。\n\n点击确定选择类别定义文件，点击取消返回。"
            else:
                ask_title = "Class Definition File Required"
                ask_msg = "A class definition file (class_def.txt) is required before selecting a model.\nThis file defines the categories that the model can recognize.\n\nClick OK to select class definition file, Cancel to return."
            
            result = ScaledMessageBox.askokcancel(ask_title, ask_msg, 
                                                 parent=self.root, language=self.current_language)
            
            if not result:
                return  # 用户取消
            
            # 选择class_def.txt文件
            class_def_file = self.select_class_def_file()
            if not class_def_file:
                warning_msg = "未选择类别定义文件，无法加载模型" if self.current_language == 'zh' else "No class definition file selected, cannot load model"
                ScaledMessageBox.showwarning(self.texts['warning'], warning_msg, 
                                           parent=self.root, language=self.current_language)
                return
            
            # 文件选择成功，路径已在select_class_def_file中设置
        
        # 选择模型文件
        model_title = "选择模型文件" if self.current_language == 'zh' else "Select Model File"
        model_format = "模型文件" if self.current_language == 'zh' else "Model File"
        all_files = "所有文件" if self.current_language == 'zh' else "All Files"
        
        # 打开文件选择对话框
        model_file = self.enhanced_file_dialog(
            'open',
            title=model_title,
            filetypes=[(model_format, "*.pth *.pt"), (all_files, "*.*")]
        )
        
        if model_file:
            # 更新状态
            self.status_label.config(text=f"{self.texts['loading']}: {os.path.basename(model_file)}")
            
            try:
                # 加载模型（使用自定义函数）
                self.model, self.cls_count, self.cls_ids, self.cls_names = self.load_classify_model_custom(
                    model_file, self.class_def_path)
                self.model_path = model_file
                
                # 更新状态
                model_info = f"{self.texts['model_loaded']}: {os.path.basename(model_file)}"
                self.status_label.config(text=model_info)
                
                # 在推理结果区域显示模型信息
                if hasattr(self, 'results_text'):
                    self.results_text.config(state=tk.NORMAL)
                    self.results_text.delete('1.0', tk.END)
                    model_info_text = f"模型文件: {os.path.basename(model_file)}\n"
                    model_info_text += f"类别定义: {os.path.basename(self.class_def_path)}\n"
                    model_info_text += f"类别数量: {self.cls_count}\n"
                    model_info_text += f"类别列表:\n"
                    for i, name in enumerate(self.cls_names):
                        model_info_text += f"  {i}: {name}\n"
                    self.results_text.insert(tk.END, model_info_text)
                    self.results_text.config(state=tk.DISABLED)
                    
                    # 自动展开推理结果面板以显示模型信息
                    if hasattr(self, 'panel_collapsed') and self.panel_collapsed.get('inference_results', False):
                        self.toggle_panel_collapse('inference_results')
                
                print(f"模型加载成功: {model_file}")
                print(f"使用类别定义: {self.class_def_path}")
                
            except Exception as e:
                error_msg = f"{self.texts['model_load_failed']}: {str(e)}"
                ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                          parent=self.root, language=self.current_language)
                self.status_label.config(text=self.texts['load_failed'])
                print(f"模型加载失败: {e}")
    
    def run_inference(self):
        """
        运行深度学习模型推理
        
        该方法是模型推理功能的主入口，集成了完整的信号处理和推理流程：
        
        推理流程：
        1. 验证模型和数据是否已正确加载
        2. 使用 proc_wbsig 函数进行信号预处理
        3. 调用 predict_classify_proc_all 执行批量推理
        4. 解析推理结果并生成详细分析报告
        5. 在图表上自动标记检测到的信号段
        
        技术实现：
        - 多线程执行，避免界面卡顿
        - 自动生成和清理临时数据集文件
        - 实时状态更新和错误处理
        - 支持置信度阈值过滤
        
        结果输出：
        - 显示每个信号段的类别、置信度、位置信息
        - 包含信号参数（中心频率、带宽、持续时间等）
        - 自动在时域和频域图表上添加标记
        - 提供统计汇总信息
        
        异常处理：
        - 模型未加载时显示提示
        - 数据文件未加载时显示提示
        - 推理失败时显示详细错误信息
        """
        if not MODEL_INFERENCE_AVAILABLE:
            ScaledMessageBox.showwarning(self.texts['warning'], "模型推理功能不可用", 
                                        parent=self.root, language=self.current_language)
            return
        
        # 检查模型是否已加载
        if self.model is None:
            ScaledMessageBox.showwarning(self.texts['warning'], self.texts['model_not_loaded'], 
                                        parent=self.root, language=self.current_language)
            return
        
        # 检查数据是否已加载
        if self.current_signal is None or not self.current_files:
            ScaledMessageBox.showwarning(self.texts['warning'], self.texts['no_data_loaded'], 
                                        parent=self.root, language=self.current_language)
            return
        
        # 获取当前文件路径
        current_file = self.current_files[self.current_index]
        
        # 更新状态
        self.status_label.config(text=f"{self.texts['processing']}: {os.path.basename(current_file)}")
        
        # 在新线程中运行推理以避免界面卡顿
        def run_inference_thread():
            try:
                # 1. 准备信号数据
                samples = self.current_signal
                
                # 2. 获取信号元数据（与testcase_on_tinydataset.py保持一致）
                if hasattr(self, 'current_metadata'):
                    wb_fs = self.current_metadata.get('fs', 15.36e6)
                    wb_fc = self.current_metadata.get('fc', 915e6)
                    wb_bw = self.current_metadata.get('bw', 12e6)
                else:
                    # 使用默认值
                    wb_fs = 15.36e6
                    wb_fc = 915e6
                    wb_bw = 12e6
                
                # 3. 信号处理（proc_wbsig）- 直接传递参数，不使用init_args
                print(f"正在处理信号... 长度: {len(samples)}, fs: {wb_fs/1e6:.1f}MHz, fc: {wb_fc/1e6:.1f}MHz, bw: {wb_bw/1e6:.1f}MHz")
                proc_wbsig(samples, wb_fs, wb_fc, wb_bw)
                
                # 5. 模型推理
                dataset_name = "fchanscan-S1.hdf5"  # proc_wbsig生成的数据集文件
                threshold_classify = np.ones(self.cls_count, dtype=float) * 0.7  # 设置阈值
                
                if os.path.exists(dataset_name):
                    print("正在运行模型推理...")
                    
                    # 先读取位置信息，避免多线程竞争
                    position_info = None
                    try:
                        from utils.dataloader import LoadHdfsDataset
                        rx_signal, classid_gt, class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_name)
                        
                        position_info = {
                            'start_poses': start_poses,
                            'end_poses': end_poses,
                            'fc_values': fc_values,
                            'fs_value': fs_value,
                            'bw_value': bw_value,
                            'snr_values': snr_values,
                            'duration_values': duration_values
                        }
                    except Exception as e:
                        print(f"读取位置信息失败: {e}")
                        position_info = None
                    
                    # 运行模型推理
                    cur_predicts = predict_classify_proc_all(self.model, self.cls_count, self.cls_names, dataset_name, threshold_classify)
                    
                    # 6. 分析推理结果（传递位置信息）
                    self.analyze_and_display_results(cur_predicts, current_file, position_info)
                    
                    # 推理和结果显示完成后再清理临时文件
                    # 这样可以支持多次推理重复使用同一个文件名
                    if os.path.exists(dataset_name):
                        os.remove(dataset_name)
                else:
                    raise Exception("信号处理未生成有效的数据集文件")
                
                # 更新状态
                self.root.after(0, lambda: self.status_label.config(text=self.texts['inference_complete']))
                
            except Exception as e:
                error_msg = f"{self.texts['inference_failed']}: {str(e)}"
                print(f"推理失败: {e}")
                self.root.after(0, lambda: ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                                                      parent=self.root, language=self.current_language))
                self.root.after(0, lambda: self.status_label.config(text=self.texts['inference_failed']))
        
        # 启动推理线程
        threading.Thread(target=run_inference_thread, daemon=True).start()
    
    def safe_array_len(self, arr):
        """
        安全地获取数组长度，处理NumPy数组的特殊情况
        
        参数:
            arr: 数组或列表
            
        返回:
            int: 数组长度
        """
        try:
            if arr is None:
                return 0
            length = len(arr)
            # 如果length是numpy标量，转换为Python int
            if hasattr(length, 'item'):
                length = length.item()
            return int(length)
        except:
            return 0
    
    def analyze_and_display_results(self, predictions, filename, position_info=None):
        """
        分析并显示推理结果
        
        参数:
            predictions: 模型预测结果（torch.Tensor）
            filename: 原始文件名
            position_info: 位置信息字典，包含start_poses, end_poses, fc_values等
        """
        def update_results():
            try:
                # 获取预测结果
                val_predict = torch.argmax(predictions, dim=1)  # 获取每个样本的预测类别
                nCount = min(val_predict.shape[0], 10)  # 限制显示数量
                
                # 使用传入的位置信息
                start_poses, end_poses, fc_values = [], [], []
                snr_values, duration_values = [], []
                fs_value, bw_value = None, None
                
                if position_info is not None:
                    start_poses = position_info.get('start_poses', [])
                    end_poses = position_info.get('end_poses', [])
                    fc_values = position_info.get('fc_values', [])
                    snr_values = position_info.get('snr_values', [])
                    duration_values = position_info.get('duration_values', [])
                    fs_value = position_info.get('fs_value', None)
                    bw_value = position_info.get('bw_value', None)
                else:
                    start_poses, end_poses, fc_values = [], [], []
                    snr_values, duration_values = [], []
                
                # 准备显示文本
                results_text = f"文件: {os.path.basename(filename)}\n"
                results_text += f"检测到 {val_predict.shape[0]} 个信号段\n"
                results_text += "="*50 + "\n"
                
                # 统计预测结果
                class_counts = {}
                valid_predictions = 0
                threshold_classify = np.ones(self.cls_count, dtype=float) * 0.7
                
                for i in range(nCount):
                    id_cls = val_predict[i].item()
                    props = predictions[i, id_cls].item()
                    
                    # 检查是否超过阈值
                    if props > threshold_classify[id_cls]:
                        valid_predictions += 1
                        class_name = self.cls_names[id_cls]
                        
                        # 统计类别数量
                        if class_name not in class_counts:
                            class_counts[class_name] = 0
                        class_counts[class_name] += 1
                        
                        # 添加到结果文本（包含位置信息）
                        results_text += f"信号段 {i+1}:\n"
                        results_text += f"  类别: {class_name}\n"
                        results_text += f"  置信度: {props*100:.1f}%\n"
                        results_text += f"  类别ID: {id_cls}\n"
                        
                        # 添加位置信息
                        if i < self.safe_array_len(start_poses) and i < self.safe_array_len(end_poses):
                            start_pos = start_poses[i]
                            end_pos = end_poses[i]
                            
                            # 处理可能的数组格式
                            if hasattr(start_pos, '__len__') and len(start_pos) > 0:
                                start_pos = start_pos[0]
                            if hasattr(end_pos, '__len__') and len(end_pos) > 0:
                                end_pos = end_pos[0]
                            
                            # 转换为Python原生类型以避免格式化错误
                            if hasattr(start_pos, 'item'):
                                start_pos = start_pos.item()
                            if hasattr(end_pos, 'item'):
                                end_pos = end_pos.item()
                            start_pos = int(start_pos)  # 确保是int类型
                            end_pos = int(end_pos)  # 确保是int类型
                            
                            results_text += f"  位置: [{start_pos}, {end_pos}]\n"
                            
                            # 计算信号长度和持续时间
                            signal_length = end_pos - start_pos + 1
                            # 优先使用数据集中的采样率，如果没有则使用元数据中的采样率
                            current_fs = fs_value if fs_value is not None else (self.current_metadata.get('fs', 61.44e6) if hasattr(self, 'current_metadata') else 61.44e6)
                            # 确保 current_fs 是标量值
                            if hasattr(current_fs, '__len__') and len(current_fs) > 0:
                                current_fs = current_fs[0]
                            if hasattr(current_fs, 'item'):
                                current_fs = current_fs.item()
                            current_fs = float(current_fs)  # 确保是float类型
                            duration_ms = signal_length / current_fs * 1000
                            results_text += f"  长度: {signal_length} 点 ({duration_ms:.2f} ms)\n"
                        
                        # 添加频率信息
                        if i < self.safe_array_len(fc_values):
                            fc_val = fc_values[i]
                            # 确保 fc_val 是标量值，不是数组
                            if hasattr(fc_val, '__len__') and len(fc_val) > 0:
                                fc_val = fc_val[0]
                            # 转换为Python原生类型以避免格式化错误
                            if hasattr(fc_val, 'item'):
                                fc_val = fc_val.item()
                            fc_val = float(fc_val)  # 确保是float类型
                            results_text += f"  中心频率: {fc_val/1e6:.2f} MHz\n"
                        
                        # 添加带宽信息
                        if bw_value is not None:
                            # 确保 bw_value 是标量值，不是数组
                            bw_val = bw_value
                            if hasattr(bw_val, '__len__') and len(bw_val) > 0:
                                bw_val = bw_val[0]
                            # 转换为Python原生类型以避免格式化错误
                            if hasattr(bw_val, 'item'):
                                bw_val = bw_val.item()
                            bw_val = float(bw_val)  # 确保是float类型
                            results_text += f"  带宽: {bw_val/1e6:.2f} MHz\n"
                        
                        # 添加SNR信息
                        if i < self.safe_array_len(snr_values):
                            snr_val = snr_values[i]
                            # 处理可能的数组格式
                            if hasattr(snr_val, '__len__') and len(snr_val) > 0:
                                snr_val = snr_val[0]
                            if hasattr(snr_val, 'item'):
                                snr_val = snr_val.item()
                            snr_val = float(snr_val)
                            results_text += f"  信噪比: {snr_val:.2f} dB\n"
                        
                        # 添加原始持续时间信息（来自数据集）
                        if i < self.safe_array_len(duration_values):
                            duration_val = duration_values[i]
                            # 处理可能的数组格式
                            if hasattr(duration_val, '__len__') and len(duration_val) > 0:
                                duration_val = duration_val[0]
                            if hasattr(duration_val, 'item'):
                                duration_val = duration_val.item()
                            duration_val = float(duration_val)
                            results_text += f"  原始持续时间: {duration_val:.2f} ms\n"
                        
                        results_text += "\n"
                
                # 添加汇总信息
                results_text += "="*50 + "\n"
                results_text += f"汇总统计:\n"
                results_text += f"有效预测: {valid_predictions}/{nCount}\n"
                
                if class_counts:
                    results_text += f"\n检测到的类别分布:\n"
                    for class_name, count in class_counts.items():
                        results_text += f"  {class_name}: {count} 次\n"
                else:
                    results_text += "未检测到满足阈值的信号\n"
                
                # 添加技术信息
                if valid_predictions > 0:
                    results_text += f"\n技术信息:\n"
                    results_text += f"  阈值设置: 0.7 (70%)\n"
                    results_text += f"  处理信号段: {val_predict.shape[0]} 个\n"
                    results_text += f"  数据来源: fchanscan-S1.hdf5\n"
                    if fs_value is not None:
                        # 确保 fs_value 是标量值，不是数组
                        current_fs_display = fs_value
                        if hasattr(current_fs_display, '__len__') and len(current_fs_display) > 0:
                            current_fs_display = current_fs_display[0]
                        # 转换为Python原生类型以避免格式化错误
                        if hasattr(current_fs_display, 'item'):
                            current_fs_display = current_fs_display.item()
                        current_fs_display = float(current_fs_display)  # 确保是float类型
                        results_text += f"  采样率: {current_fs_display/1e6:.2f} MHz\n"
                    if bw_value is not None:
                        # 确保 bw_value 是标量值，不是数组
                        current_bw_display = bw_value
                        if hasattr(current_bw_display, '__len__') and len(current_bw_display) > 0:
                            current_bw_display = current_bw_display[0]
                        # 转换为Python原生类型以避免格式化错误
                        if hasattr(current_bw_display, 'item'):
                            current_bw_display = current_bw_display.item()
                        current_bw_display = float(current_bw_display)  # 确保是float类型
                        results_text += f"  信号带宽: {current_bw_display/1e6:.2f} MHz\n"
                
                # 更新推理结果显示
                if hasattr(self, 'results_text'):
                    self.results_text.config(state=tk.NORMAL)
                    self.results_text.delete('1.0', tk.END)
                    self.results_text.insert(tk.END, results_text)
                    self.results_text.config(state=tk.DISABLED)
                    
                    # 自动展开推理结果面板（如果当前是折叠状态）
                    if hasattr(self, 'panel_collapsed') and self.panel_collapsed.get('inference_results', False):
                        self.toggle_panel_collapse('inference_results')
                
                print(f"推理完成: 检测到 {valid_predictions} 个有效信号段")
                
                # 在时域信号图上添加推理结果标记
                if valid_predictions > 0 and position_info is not None:
                    self.root.after(100, lambda: self.add_inference_markers_to_plots(position_info, predictions))
                
            except Exception as e:
                print(f"结果分析失败: {e}")
                if hasattr(self, 'results_text'):
                    self.results_text.config(state=tk.NORMAL)
                    self.results_text.delete('1.0', tk.END)
                    self.results_text.insert(tk.END, f"结果分析失败: {str(e)}")
                    self.results_text.config(state=tk.DISABLED)
                    
                    # 自动展开推理结果面板以显示错误信息
                    if hasattr(self, 'panel_collapsed') and self.panel_collapsed.get('inference_results', False):
                        self.toggle_panel_collapse('inference_results')
        
        # 在主线程中更新界面
        self.root.after(0, update_results)
    
    def show_class_definitions(self):
        """
        显示类别定义信息窗口
        """
        if not MODEL_INFERENCE_AVAILABLE:
            ScaledMessageBox.showwarning(self.texts['warning'], "模型推理功能不可用", 
                                        parent=self.root, language=self.current_language)
            return
        
        # 检查是否已选择class_def文件
        if self.class_def_path is None or not os.path.exists(self.class_def_path):
            # 询问用户是否要选择类别定义文件
            if self.current_language == 'zh':
                ask_title = "未选择类别定义文件"
                ask_msg = "还没有选择类别定义文件，是否现在选择？\n\n点击确定选择文件，点击取消返回。"
            else:
                ask_title = "No Class Definition File Selected"
                ask_msg = "No class definition file has been selected yet. Would you like to select one now?\n\nClick OK to select file, Cancel to return."
            
            result = ScaledMessageBox.askokcancel(ask_title, ask_msg, 
                                                 parent=self.root, language=self.current_language)
            
            if not result:
                return  # 用户取消
            
            # 选择class_def.txt文件
            class_def_file = self.select_class_def_file()
            if not class_def_file:
                warning_msg = "未选择类别定义文件" if self.current_language == 'zh' else "No class definition file selected"
                ScaledMessageBox.showwarning(self.texts['warning'], warning_msg, 
                                           parent=self.root, language=self.current_language)
                return
            
            self.class_def_path = class_def_file
            print(f"已选择类别定义文件: {self.class_def_path}")
        
        try:
            # 读取类别定义文件
            class_data = self.parse_class_def_file(self.class_def_path)
            
            if not class_data:
                ScaledMessageBox.showerror(self.texts['error'], self.texts['failed_to_read_class_file'], 
                                          parent=self.root, language=self.current_language)
                return
            
            # 创建类别定义显示窗口
            self.create_class_definitions_window(class_data)
            
        except FileNotFoundError as e:
            error_msg = f"文件不存在: {self.class_def_path}"
            ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                      parent=self.root, language=self.current_language)
        except UnicodeDecodeError as e:
            error_msg = f"文件编码错误，无法读取文件。\n请确保文件是文本格式且编码为UTF-8。\n\n文件路径: {self.class_def_path}"
            ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                      parent=self.root, language=self.current_language)
        except ValueError as e:
            # 生成详细诊断信息
            diagnosis = self.diagnose_class_def_file(self.class_def_path)
            print("\n" + diagnosis)  # 输出到控制台
            
            error_msg = f"文件格式错误:\n{str(e)}\n\n正确格式应为:\n类别ID:类别名称:带宽:最短时间(ms)\n例如: 0:nb_RFD900X:384e3:3.34\n\n详细诊断信息已输出到控制台。"
            ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                      parent=self.root, language=self.current_language)
        except Exception as e:
            # 生成详细诊断信息
            diagnosis = self.diagnose_class_def_file(self.class_def_path)
            print("\n" + diagnosis)  # 输出到控制台
            
            error_msg = f"{self.texts['failed_to_read_class_file']}:\n{str(e)}\n\n调试信息:\n文件路径: {self.class_def_path}\n\n详细诊断信息已输出到控制台，请查看。"
            ScaledMessageBox.showerror(self.texts['error'], error_msg, 
                                      parent=self.root, language=self.current_language)
    
    def parse_class_def_file(self, file_path):
        """
        解析class_def.txt文件（增强错误处理版本）
        
        文件格式：类别ID:类别名称:带宽:最短时间(ms)
        例如：0:nb_RFD900X:384e3:3.34
        
        参数:
            file_path (str): 类别定义文件路径
            
        返回:
            list: 解析后的类别数据列表
        """
        class_data = []
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            error_msg = f"文件不存在: {file_path}"
            print(error_msg)
            raise FileNotFoundError(error_msg)
        
        # 检查文件大小
        try:
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                error_msg = f"文件为空: {file_path}"
                print(error_msg)
                raise ValueError(error_msg)
        except OSError as e:
            error_msg = f"无法获取文件大小: {e}"
            print(error_msg)
            raise
        
        # 尝试多种编码格式
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'ascii', 'latin1']
        file_content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    file_content = f.read()
                    used_encoding = encoding
                    break
            except UnicodeDecodeError:
                continue
            except Exception:
                continue
        
        if file_content is None:
            error_msg = f"无法使用任何编码读取文件: {file_path}"
            print(error_msg)
            raise UnicodeDecodeError(error_msg)
        
        # 解析文件内容
        try:
            lines = file_content.split('\n')
            valid_lines = 0
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue
                
                # 解析格式：ID:名称:带宽:最短时间(ms)
                parts = line.split(':')
                
                if len(parts) >= 4:
                    try:
                        class_id = int(parts[0].strip())
                        class_name = parts[1].strip()
                        bandwidth_str = parts[2].strip()
                        min_duration_str = parts[3].strip()
                        
                        # 解析带宽（处理科学计数法）
                        bandwidth = float(bandwidth_str)
                        
                        # 解析最短时间
                        min_duration = float(min_duration_str)
                        
                        class_data.append({
                            'id': class_id,
                            'name': class_name,
                            'bandwidth': bandwidth,
                            'min_duration': min_duration
                        })
                        
                        valid_lines += 1
                        
                    except ValueError as e:
                        print(f"解析第{line_num}行数据失败: {line}, 错误: {e}")
                        continue
                else:
                    print(f"第{line_num}行格式错误(应为4个部分): {line}")
                    continue
            
            if valid_lines == 0:
                error_msg = f"文件中没有有效的类别定义数据: {file_path}"
                print(error_msg)
                raise ValueError(error_msg)
                        
        except Exception as e:
            print(f"解析文件内容失败: {e}")
            raise
        
        return class_data
    
    def diagnose_class_def_file(self, file_path, max_lines=10):
        """
        诊断class_def文件格式问题，显示文件前几行内容
        
        参数:
            file_path (str): 文件路径
            max_lines (int): 显示的最大行数
            
        返回:
            str: 诊断信息
        """
        diagnosis = []
        diagnosis.append(f"文件诊断: {file_path}")
        diagnosis.append("=" * 50)
        
        try:
            # 检查文件存在性
            if not os.path.exists(file_path):
                diagnosis.append("❌ 错误: 文件不存在")
                return "\n".join(diagnosis)
            
            diagnosis.append("✅ 文件存在")
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            diagnosis.append(f"📊 文件大小: {file_size} 字节")
            
            if file_size == 0:
                diagnosis.append("❌ 错误: 文件为空")
                return "\n".join(diagnosis)
            
            # 尝试读取文件内容
            file_content = None
            used_encoding = None
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        file_content = f.read()
                        used_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue
            
            if file_content is None:
                diagnosis.append("❌ 错误: 无法使用常见编码读取文件")
                return "\n".join(diagnosis)
            
            diagnosis.append(f"✅ 成功读取文件，使用编码: {used_encoding}")
            
            # 分析文件内容
            lines = file_content.split('\n')
            diagnosis.append(f"📄 总行数: {len(lines)}")
            
            # 显示前几行内容
            diagnosis.append(f"\n📋 文件前 {min(max_lines, len(lines))} 行内容:")
            diagnosis.append("-" * 30)
            
            valid_count = 0
            empty_count = 0
            comment_count = 0
            error_count = 0
            
            for i, line in enumerate(lines[:max_lines], 1):
                original_line = line
                line = line.strip()
                
                if not line:
                    diagnosis.append(f"第{i:2d}行: (空行)")
                    empty_count += 1
                elif line.startswith('#'):
                    diagnosis.append(f"第{i:2d}行: {original_line[:60]}... (注释)")
                    comment_count += 1
                else:
                    parts = line.split(':')
                    if len(parts) >= 4:
                        diagnosis.append(f"第{i:2d}行: {original_line[:60]}... ✅")
                        valid_count += 1
                    else:
                        diagnosis.append(f"第{i:2d}行: {original_line[:60]}... ❌ (格式错误)")
                        error_count += 1
            
            # 统计信息
            diagnosis.append("-" * 30)
            diagnosis.append(f"📈 统计信息:")
            diagnosis.append(f"   有效行: {valid_count}")
            diagnosis.append(f"   空行: {empty_count}")
            diagnosis.append(f"   注释行: {comment_count}")
            diagnosis.append(f"   格式错误行: {error_count}")
            
            # 格式说明
            diagnosis.append(f"\n📝 正确格式:")
            diagnosis.append(f"   类别ID:类别名称:带宽:最短时间(ms)")
            diagnosis.append(f"   例如: 0:nb_RFD900X:384e3:3.34")
            
        except Exception as e:
            diagnosis.append(f"❌ 诊断过程发生错误: {e}")
        
        return "\n".join(diagnosis)
    
    def create_class_definitions_window(self, class_data):
        """
        创建类别定义显示窗口
        
        参数:
            class_data (list): 类别数据列表
        """
        try:
            # 创建新窗口
            class_window = tk.Toplevel(self.root)
            class_window.title(self.texts['class_definitions_title'])
            class_window.resizable(True, True)
            
            # 设置窗口大小和位置（1.5倍缩放）
            window_width = int(800 * 1.5)   # 1200
            window_height = int(600 * 1.5)  # 900
            
            # 居中显示
            screen_width = class_window.winfo_screenwidth()
            screen_height = class_window.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            class_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            
            # 设置最小窗口大小
            class_window.minsize(int(600 * 1.5), int(400 * 1.5))
            
            # 创建主框架
            main_frame = ttk.Frame(class_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 创建标题标签
            title_label = tk.Label(main_frame, text=self.texts['class_definitions_title'],
                                  font=self.fonts['title_font'])
            title_label.pack(pady=(0, 10))
            
            # 创建表格框架
            tree_frame = ttk.Frame(main_frame)
            tree_frame.pack(fill=tk.BOTH, expand=True)
            
            # 创建Treeview（表格）
            columns = ('id', 'name', 'bandwidth', 'min_duration')
            tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
            
            # 定义列标题
            tree.heading('id', text=self.texts['class_id'])
            tree.heading('name', text=self.texts['class_name'])
            tree.heading('bandwidth', text=self.texts['bandwidth'])
            tree.heading('min_duration', text=self.texts['min_duration'])
            
            # 设置列宽（1.5倍缩放）
            tree.column('id', width=int(80 * 1.5), anchor='center')  # 120
            tree.column('name', width=int(250 * 1.5), anchor='w')    # 375
            tree.column('bandwidth', width=int(150 * 1.5), anchor='center')  # 225
            tree.column('min_duration', width=int(120 * 1.5), anchor='center')  # 180
            
            # 添加垂直滚动条
            v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=v_scrollbar.set)
            
            # 添加水平滚动条
            h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=tree.xview)
            tree.configure(xscrollcommand=h_scrollbar.set)
            
            # 布局表格和滚动条
            tree.grid(row=0, column=0, sticky='nsew')
            v_scrollbar.grid(row=0, column=1, sticky='ns')
            h_scrollbar.grid(row=1, column=0, sticky='ew')
            
            # 配置grid权重
            tree_frame.grid_rowconfigure(0, weight=1)
            tree_frame.grid_columnconfigure(0, weight=1)
            
            # 填充数据
            for i, data in enumerate(class_data):
                # 格式化带宽显示
                bandwidth_display = self.format_bandwidth(data['bandwidth'])
                
                # 格式化最短时间显示（添加ms后缀）
                min_duration_display = f"{data['min_duration']} ms"
                
                item = tree.insert('', 'end', values=(
                    data['id'],
                    data['name'],
                    bandwidth_display,
                    min_duration_display
                ))
                
                # 设置交替行颜色标签
                if i % 2 == 0:
                    tree.item(item, tags=('evenrow',))
                else:
                    tree.item(item, tags=('oddrow',))
            
            # 配置交替行颜色
            tree.tag_configure('evenrow', background='#f0f0f0')
            tree.tag_configure('oddrow', background='white')
            
            # 创建信息标签
            info_frame = ttk.Frame(main_frame)
            info_frame.pack(fill=tk.X, pady=(10, 0))
            
            total_label = tk.Label(info_frame, 
                                  text=f"{self.texts['total_classes']}: {len(class_data)}",
                                  font=self.fonts['status_font'])
            total_label.pack(side=tk.LEFT)
            
            # 文件路径标签
            file_label = tk.Label(info_frame, 
                                 text=f"{self.texts['file_path']}: {self.class_def_path}",
                                 font=self.fonts['status_font'])
            file_label.pack(side=tk.RIGHT)
            
            # 创建按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))
            
            # 关闭按钮
            close_button = tk.Button(button_frame, text=self.texts.get('close', '关闭'), 
                                    command=class_window.destroy, font=self.fonts['button_font'])
            close_button.pack(side=tk.RIGHT)
            
            # 设置焦点
            tree.focus_set()
            
        except Exception as e:
            print(f"创建类别定义窗口失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def format_bandwidth(self, bandwidth):
        """
        格式化带宽显示
        
        参数:
            bandwidth (float): 带宽值（Hz）
            
        返回:
            str: 格式化的带宽字符串
        """
        try:
            if bandwidth >= 1e6:
                return f"{bandwidth/1e6:.2f} MHz"
            elif bandwidth >= 1e3:
                return f"{bandwidth/1e3:.1f} kHz"
            else:
                return f"{bandwidth:.0f} Hz"
        except:
            return str(bandwidth)
    
    def add_inference_markers_to_plots(self, position_info, predictions):
        """
        在时域信号点值图、频谱图和时频图上添加推理结果标记
        
        参数:
            position_info: 位置信息字典
            predictions: 模型预测结果
        """
        if position_info is None or not hasattr(self, 'axes') or len(self.axes) < 4:
            return
        
        try:
            # 获取位置信息
            start_poses = position_info.get('start_poses', [])
            end_poses = position_info.get('end_poses', [])
            fc_values = position_info.get('fc_values', [])
            fs_value = position_info.get('fs_value', None)
            
            # 安全地检查数组长度
            start_poses_len = self.safe_array_len(start_poses)
            end_poses_len = self.safe_array_len(end_poses)
            fc_values_len = self.safe_array_len(fc_values)
                
            if start_poses_len == 0 or end_poses_len == 0:
                return
            
            # 获取预测结果
            val_predict = torch.argmax(predictions, dim=1)
            threshold_classify = np.ones(self.cls_count, dtype=float) * 0.7
            
            # 标记颜色列表
            marker_colors = ['orange', 'purple', 'lime', 'cyan', 'magenta', 'yellow', 'pink', 'brown']
            
            # 在时域信号点值图(ax1)、频谱图(ax3)和时频图(ax4)上添加标记
            ax1 = self.axes[0]  # 时域信号点值图
            ax2 = self.axes[1]  # 时域信号图（不添加标记）
            ax3 = self.axes[2]  # 频谱图
            ax4 = self.axes[3]  # 时频图
            
            # 记录有效的推理结果用于图例
            valid_segments = []
            
            nCount = min(val_predict.shape[0], start_poses_len, end_poses_len)
            
            for i in range(nCount):
                id_cls = val_predict[i].item()
                props = predictions[i, id_cls].item()
                
                # 检查是否超过阈值
                if props > threshold_classify[id_cls]:
                    # 获取位置信息
                    start_pos = start_poses[i]
                    end_pos = end_poses[i]
                    
                    # 处理可能的数组格式
                    if hasattr(start_pos, '__len__') and len(start_pos) > 0:
                        start_pos = start_pos[0]
                    if hasattr(end_pos, '__len__') and len(end_pos) > 0:
                        end_pos = end_pos[0]
                    if hasattr(start_pos, 'item'):
                        start_pos = start_pos.item()
                    if hasattr(end_pos, 'item'):
                        end_pos = end_pos.item()
                    start_pos = int(start_pos)
                    end_pos = int(end_pos)
                    
                    # 获取中心频率信息
                    fc_text = ""
                    if i < fc_values_len:
                        fc_val = fc_values[i]
                        # 确保 fc_val 是标量值，不是数组
                        if hasattr(fc_val, '__len__') and len(fc_val) > 0:
                            fc_val = fc_val[0]
                        # 转换为Python原生类型以避免格式化错误
                        if hasattr(fc_val, 'item'):
                            fc_val = fc_val.item()
                        fc_val = float(fc_val)  # 确保是float类型
                        fc_text = f"\n{fc_val/1e6:.1f}MHz"
                    
                    # 选择标记颜色
                    color = marker_colors[i % len(marker_colors)]
                    alpha = 0.3
                    
                    # 获取类别名称和置信度
                    class_name = self.cls_names[id_cls]
                    confidence = props * 100
                    
                    # 在时域信号点值图上标记（X轴为1e5单位）
                    start_pos_1e5 = start_pos / 1e5
                    end_pos_1e5 = end_pos / 1e5
                    ax1.axvspan(start_pos_1e5, end_pos_1e5, color=color, alpha=alpha, 
                               label=f'{class_name} ({confidence:.1f}%)')
                    
                    # 添加文本标注（在标记区域的中间）
                    mid_pos_1e5 = (start_pos_1e5 + end_pos_1e5) / 2
                    y_min, y_max = ax1.get_ylim()
                    text_y = y_max * 0.85 - (i % 3) * (y_max * 0.15)  # 错开显示位置
                    # 增加中心频率显示
                    display_text = f'{class_name}\n{confidence:.1f}%{fc_text}'
                    ax1.text(mid_pos_1e5, text_y, display_text, 
                            ha='center', va='center', fontsize=8, 
                            bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.7),
                            rotation=0)
                    
                    # 在频谱图上标记中心频率（如果有中心频率信息）
                    if i < fc_values_len:
                        fc_val_mhz = fc_val / 1e6  # 转换为MHz
                        # 添加垂直线标记中心频率
                        ax3.axvline(x=fc_val_mhz, color=color, linestyle='--', linewidth=2, alpha=0.8, 
                                   label=f'{class_name}: {fc_val_mhz:.1f}MHz')
                        
                        # 在时频图上标记中心频率和时间范围
                        try:
                            # 获取时频图的当前轴范围
                            tf_x_min, tf_x_max = ax4.get_xlim()  # 时间轴(ms)
                            tf_y_min, tf_y_max = ax4.get_ylim()  # 频率轴(MHz)
                            
                            # 计算信号段对应的时间范围(ms)
                            if hasattr(self, 'current_metadata') and self.current_metadata.get('fs'):
                                current_fs = self.current_metadata['fs']
                                start_time_ms = start_pos / current_fs * 1000
                                end_time_ms = end_pos / current_fs * 1000
                                
                                # 添加水平线标记中心频率
                                ax4.axhline(y=fc_val_mhz, color=color, linestyle='--', linewidth=1.5, alpha=0.7)
                                
                                # 添加垂直线标记时间范围（信号段的开始和结束）
                                if tf_x_min <= start_time_ms <= tf_x_max:
                                    ax4.axvline(x=start_time_ms, color=color, linestyle=':', linewidth=1, alpha=0.6)
                                if tf_x_min <= end_time_ms <= tf_x_max:
                                    ax4.axvline(x=end_time_ms, color=color, linestyle=':', linewidth=1, alpha=0.6)
                                
                                # 在时频图上添加文本标注
                                # 将文本放在信号段时间范围的中心位置
                                mid_time_ms = (start_time_ms + end_time_ms) / 2
                                if tf_x_min <= mid_time_ms <= tf_x_max and tf_y_min <= fc_val_mhz <= tf_y_max:
                                    # 上下错开显示，避免重叠
                                    tf_text_y_offset = (tf_y_max - tf_y_min) * 0.08 * (i % 3)  # 根据索引错开
                                    tf_text_y = fc_val_mhz + (tf_y_max - tf_y_min) * 0.05 + tf_text_y_offset
                                    tf_display_text = f'{class_name}\n{fc_val_mhz:.1f}MHz'
                                    ax4.text(mid_time_ms, tf_text_y, tf_display_text, 
                                            ha='center', va='bottom', fontsize=8, 
                                            bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.8),
                                            rotation=0)
                        except Exception as e:
                            print(f"时频图标记失败: {e}")
                            pass  # 时频图标记失败不影响其他功能
                    
                    # 时域信号图不添加标记，保持简洁
                    
                    # 记录有效段信息
                    valid_segments.append({
                        'class_name': class_name,
                        'confidence': confidence,
                        'start_pos': start_pos,
                        'end_pos': end_pos,
                        'color': color,
                        'fc_val': fc_val if i < fc_values_len else None
                    })
            
            # 更新图表标题以反映推理结果
            if valid_segments:
                ax1.set_title(f'时域信号点值图 - 检测到 {len(valid_segments)} 个信号段')
                # 时域信号图保持原始标题，不显示信号段信息
                ax2.set_title('时域信号图')
                # 更新频谱图标题以显示中心频率标记信息
                fc_info = self.current_metadata.get('fc', 2.4e9) if hasattr(self, 'current_metadata') else 2.4e9
                fs_info = self.current_metadata.get('fs', 1e6) if hasattr(self, 'current_metadata') else 1e6
                ax3.set_title(f'频谱图 [fc= {fc_info/1e6:.1f}MHz  fs= {fs_info/1e6:.1f}MHz] - 显示 {len(valid_segments)} 个信号段中心频率')
                
                # 为频谱图添加图例，显示在空白区域
                if len(valid_segments) > 0:
                    # 调整图例位置和样式
                    legend = ax3.legend(loc='upper right', fontsize=7, framealpha=0.9, 
                                      fancybox=True, shadow=True, ncol=1)
                    legend.get_frame().set_facecolor('white')
                    legend.get_frame().set_edgecolor('gray')
                
                # 更新时频图标题以显示标记信息
                ax4.set_title(f'时频图 [fc= {fc_info/1e6:.1f}MHz  fs= {fs_info/1e6:.1f}MHz] - 显示 {len(valid_segments)} 个信号段时频特征')
                
                # 为第一个图添加简化的图例（避免太拥挤）
                if len(valid_segments) <= 5:  # 只有少量信号段时才显示图例
                    ax1.legend(loc='upper right', fontsize=8, framealpha=0.8)
            
            # 重新绘制图表
            self.canvas.draw()
            

            
        except Exception as e:
            pass  # 忽略标记错误

    def find_adaptive_frequency_ticks(self, freqs_mhz, fftdata):
        """
        快速生成自适应频率刻度（简化高效版本）
        
        参数:
            freqs_mhz: 频率数组 (MHz)
            fftdata: FFT频谱数据（未使用，保持接口兼容）
        
        返回:
            list: 频率刻度点列表 (MHz)
        """
        try:
            freq_min = freqs_mhz[0]
            freq_max = freqs_mhz[-1] 
            freq_span = freq_max - freq_min
            
            if freq_span <= 0:
                return [freq_min]
            
            # 快速计算合适的刻度间隔和数量
            return self.generate_optimal_ticks(freq_min, freq_max, freq_span)
            
        except Exception as e:
            print(f"频率刻度计算失败: {e}")
            # 简单回退：5个均匀刻度
            return np.linspace(freqs_mhz[0], freqs_mhz[-1], 5).tolist()
    
    def generate_optimal_ticks(self, freq_min, freq_max, freq_span):
        """
        快速生成最优的均匀频率刻度
        
        参数:
            freq_min: 最小频率 (MHz)
            freq_max: 最大频率 (MHz)
            freq_span: 频率跨度 (MHz)
            
        返回:
            list: 优化的刻度点列表
        """
        # 根据频率跨度快速确定刻度间隔
        if freq_span <= 1:
            tick_interval = 0.1
            max_ticks = 8  
        elif freq_span <= 5:
            tick_interval = 0.5
            max_ticks = 8
        elif freq_span <= 20:
            tick_interval = 2
            max_ticks = 10
        elif freq_span <= 50:
            tick_interval = 5
            max_ticks = 10
        elif freq_span <= 100:
            tick_interval = 10
            max_ticks = 10
        elif freq_span <= 200:
            tick_interval = 20
            max_ticks = 10
        else:
            # 大跨度：动态计算间隔
            tick_interval = freq_span / 8  # 固定8个刻度间隔
            max_ticks = 9  # 9个刻度点
        
        # 计算起始点（向上取整到合适的刻度）
        if tick_interval >= 1:
            start_tick = np.ceil(freq_min / tick_interval) * tick_interval
        else:
            start_tick = np.ceil(freq_min / tick_interval) * tick_interval
        
        # 生成刻度点
        ticks = []
        current_tick = start_tick
        tick_count = 0
        
        while current_tick <= freq_max and tick_count < max_ticks:
            ticks.append(round(current_tick, 2 if tick_interval < 1 else 1))
            current_tick += tick_interval
            tick_count += 1
        
        # 确保包含最大值附近的刻度
        if len(ticks) > 0 and abs(ticks[-1] - freq_max) > tick_interval * 0.5:
            if len(ticks) < max_ticks:
                ticks.append(round(freq_max, 2 if tick_interval < 1 else 1))
        
        return ticks
    
    def calculate_adaptive_ylim(self, signal_i, signal_q):
        """
        根据信号数据自适应计算Y轴范围，优先使用整百或半百刻度
        
        参数:
            signal_i: I路信号数据
            signal_q: Q路信号数据
            
        返回:
            tuple: (y_min, y_max) Y轴范围
        """
        try:
            # 计算I、Q路信号的统计信息
            i_min, i_max = np.min(signal_i), np.max(signal_i)
            q_min, q_max = np.min(signal_q), np.max(signal_q)
            
            # 取绝对值的最大值作为参考
            abs_max = max(abs(i_min), abs(i_max), abs(q_min), abs(q_max))
            
            # 如果信号幅度很小，使用默认范围
            if abs_max < 10:
                return (-50, 50)
            
            # 计算合适的范围（留20%余量）
            range_with_margin = abs_max * 1.2
            
            # 优先选择整百刻度
            if range_with_margin <= 50:
                y_limit = 50
            elif range_with_margin <= 100:
                y_limit = 100
            elif range_with_margin <= 150:
                y_limit = 150  # 半百
            elif range_with_margin <= 200:
                y_limit = 200
            elif range_with_margin <= 250:
                y_limit = 250  # 半百
            elif range_with_margin <= 300:
                y_limit = 300
            elif range_with_margin <= 400:
                y_limit = 400
            elif range_with_margin <= 500:
                y_limit = 500
            elif range_with_margin <= 600:
                y_limit = 600
            elif range_with_margin <= 750:
                y_limit = 750  # 半百
            elif range_with_margin <= 1000:
                y_limit = 1000
            elif range_with_margin <= 1500:
                y_limit = 1500  # 半百
            elif range_with_margin <= 2000:
                y_limit = 2000
            else:
                # 对于很大的值，计算最接近的整百或半百
                if range_with_margin <= 5000:
                    # 5000以下，使用250的倍数（半百系列）
                    y_limit = int(np.ceil(range_with_margin / 250) * 250)
                else:
                    # 5000以上，使用500的倍数（整百系列）
                    y_limit = int(np.ceil(range_with_margin / 500) * 500)
            
            return (-y_limit, y_limit)
            
        except Exception as e:
            print(f"计算自适应Y轴范围失败: {e}")
            # 失败时返回默认范围
            return (-500, 500)

def main():
    """
    主函数 - 程序入口点
    
    创建Tkinter根窗口和DataViewer应用实例，启动GUI事件循环
    """
    root = tk.Tk()  # 创建根窗口
    app = DataViewer(root)  # 创建应用实例
    root.mainloop()  # 启动GUI事件循环

if __name__ == "__main__":
    main() 