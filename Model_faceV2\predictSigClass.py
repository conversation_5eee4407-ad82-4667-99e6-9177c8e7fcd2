# =======================================================================================================================
#   Function    ：PredictSigClass.py
#   Description : 模型预测推理代码
#                 传入hdf5文件路径，并对modelTrain.py调用
# 
#   Parameter   : ../data/predict/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2024-08-27
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from nets.arcface import Arcface
from usrlib.usrlib import *
import sys
#print(sys.argv)
import getopt
from utils.dataloader import SigNbDataset, LSWDataset, SigNbDataset_collate
from torch.utils.data import DataLoader

if __name__ == "__main__":
    modeltype = 0 #模型类别 0:分类 1:角向量
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, server_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    dataset_file = "" # 数据文件路径
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)


    # 命令行方式获取测试文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['modelname=','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modelname'):
            fname_model = opt_value
            print("The modelname is ", fname_model)
        elif opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something


    #1. 路径及模型参数
    model_path = './logs/history-0609/Mtype0-ep099-loss0.007-val_loss0.006.pth'

    batch_size = 64 #256  

    #2. 读取数据文件
    t0 = time.time()
    if dataset_file=="":
        dataset_file = annotation_path_test
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))

    #wav文件格式数据集
    lines_test   = read_dataset_lines(dataset_file, ['base'])
    np.random.seed(10101)
    np.random.shuffle(lines_test)
    np.random.seed(None)
    test_dataset = SigNbDataset(lines_test)
    test_loader  = DataLoader(test_dataset, batch_size=batch_size,  collate_fn=SigNbDataset_collate)

    
    if sys.platform.startswith('win'):
        print("当前系统是Windows系统")
    if server_path!=windows_path_local: # 判断windows路径和本地windows路径是否相同
        windows_to_local_path(server_path, windows_path_local, lines_test)
    elif sys.platform.startswith('linux'):
        print("当前系统是Linux系统") #路径转换
        windows_to_linux_path(server_path, linux_path, lines_test)
    else:
        print("当前系统不是Windows也不是Linux系统")  


    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    # 3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))


    output_all = []
    classid_gt = []
    with torch.no_grad():
        for idx, (data, labels) in enumerate(test_loader):
            y = data
            y = y.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput.softmax(dim=1))
            classid_gt.append(labels)
            break #只取batchsize个数据
    output_all = torch.cat(output_all, dim=0)
    classid_gt = torch.cat(classid_gt, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    print("True Labels:\n{0}".format(classid_gt.reshape(1,-1)))

    val_predict = torch.argmax(output_all,dim=1)
    val_predict1 = torch.argsort(output_all,dim=1,descending=True)
    print("The sorted predictions:\n{0}".format(val_predict.reshape(1,-1).cpu().numpy()))
    val_label = torch.Tensor(classid_gt).cuda()
    val_label=val_label.squeeze()
    val_score = torch.where(val_predict == val_label, 1.0, 0.0)
    val_acc = torch.mean(val_score)
    val_acc = val_acc.cpu().numpy()
    t4 = time.time()
    print("count score：{:.2f}s".format(t4 - t3))
    print('predict_result: acc= {:.2f}%'.format(val_acc*100))
    nCount = val_predict.shape[0]
    if nCount>10:
        nCount=10

    print('list the top predict results:')
    for i in np.arange(nCount):
        print("predict val:{0} {1}".format(val_predict[i].item(),cls_names[val_predict[i]]))

    total_params_deep = "{:,}".format(sum(p.numel() for p in Model.parameters()))
    print(f"Model parameters: {total_params_deep}")