from usrlib.usrlib import *
import torch 
from net.modelDesign import *

clsdef_dir = './../data/' #信号路径
clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
cls_ids, cls_names, cls_count = get_classes(clsdef_file)
print('total classes count:',cls_count)

modeltype = 1 #模型类别 0:分类 1:角向量

#文件方式读入，加入
dsdef_file = os.path.join(clsdef_dir, 'vector_ds.txt')
file_subpaths, posset, N_rec = get_dsdata(dsdef_file)

#1. 路径及模型参数
model_dir = './PretrainedModel/'
if modeltype == 0:
    fname_model = "DroneSigCls.pth"
else:    
    fname_model = "DroneSigArc.pth"
model_saved = os.path.join(model_dir, fname_model)
#3.读取模型文件
Model = DroneSig_classifier(nc=cls_count, ModelType=modeltype)
Init_model(Model, True, model_saved)

Model.eval()

ViewArcVectorDB() #查看向量库

for i in range(N_rec):
    spos,epos = posset[i].replace('[','').replace(']','').split(',')
    clippos = [int(spos), int(epos)]
    file_subpath = file_subpaths[i]
    data_file = os.path.join('E:\\ftproot\\signalDB\\', file_subpath)
    sigdata = Read_sigfile(data_file, clippos)
    print('要解析信号的结构：{0}'.format(sigdata.shape))
    sigdata = torch.from_numpy(sigdata)
    sigdata = sigdata.cuda()
    #4.生成向量,加库
    vec1 = Model(sigdata,bpredict=True)
    vec1 = vec1.squeeze()
    clsname = file_subpath.split('\\')[0]
    index = cls_names.index(clsname)
    clsid = cls_ids[index]
    AddArcVector(vec1.detach().cpu().numpy(), int(clsid), clsname, file_subpath)

ViewArcVectorDB() 