from torch.utils.data import Dataset
import h5py
import os
import numpy as np
import torch

class MDataHandler(Dataset):
    def __init__(self, rx_signal,class_id,class_name,fs_value, bw_value):
        self.rx_signal = rx_signal
        self.class_id = class_id
        self.class_name = class_name
        self.fs_value = fs_value        
        self.bw_value = bw_value

    def __getitem__(self, index):
        return torch.tensor(self.rx_signal[index]), torch.tensor(self.fs_value[index]), torch.tensor(self.bw_value[index]),torch.tensor(self.class_id[index])

    def __len__(self):
        return len(self.class_id)

class Epoch_params():
    def __init__(self, epoch, total_epoch, num_train,num_val,batch_size):
        self.epoch = epoch
        self.total_epoch = total_epoch
        self.batch_size = batch_size
        self.epoch_step = num_train // batch_size
        self.epoch_step_val = num_val // batch_size


# Data Loader Class Defining
class DatasetFolder_eval(Dataset):
    def __init__(self, y, fs_value, bw_value):
        self.y = y.astype(np.float32)
        self.fs_value = fs_value.astype(np.float32)
        self.bw_value = bw_value.astype(np.float32)

    def __getitem__(self, index):
        return torch.tensor(self.y[index]), torch.tensor(self.fs_value[index]), torch.tensor(self.bw_value[index])

    def __len__(self):
        return len(self.y)
        
# 无人机信号数据集        
class DatasetDroneSig(Dataset):
    def __init__(self, y):
        self.y = y.astype(np.float32)
        self.datalen = 160000
        
    def __getitem__(self, index):        
        return torch.tensor(self.y[index])

    def __len__(self):
        return len(self.y)

def LoadHdfsDataset(dataset_file):
    '''
    加载单个hdfs数据集
    '''
    if os.path.exists(dataset_file)==False:
        print("{0}:文件不存在".format(dataset_file))
        return
    
    #LenNorm = 220000 #3ms for 61.44M
    LenNorm = 1024*58+512# 应该<=48000 #narrow band,10ms for 4M

    f = h5py.File(dataset_file, 'r')
    rx_signals  = f['rx_signal'][:]
    class_ids   = f['class_id'][:]
    class_names = f['class_name'][:]
    fs_values = f['fs'][:]
    bw_values = f['bw'][:]
    f.close()

    # 裁剪数据集的size
    N_row =  rx_signals.shape[0]
    N_col =  rx_signals.shape[1]
    if N_col<LenNorm:
        rx_signals = np.pad(rx_signals,((0,0),(0,LenNorm-N_col),(0,0)), 'constant')
    else:
        rx_signals = np.resize(rx_signals,(N_row, LenNorm, 2))
    
    

    print('rx_signal:', rx_signals.shape, rx_signals.dtype)
    print('class_id:', class_ids.shape, class_ids.dtype)
    return  rx_signals, class_ids, class_names, fs_values, bw_values

def LoadMultiHdfsDataset(folders:list[str]):
    '''
    加载多个数据集
    '''
    dataset_files = []
    ext_name = 'hdf5'
    for folder in folders:
        for file in os.listdir( folder ):
            if file.endswith( ext_name ):# os.path.splitext(name)[1] == suffix:
                dataset_file = os.path.join(folder, file)
                dataset_files.append(dataset_file) #数据集
    
    rx_signals = []
    class_ids = []
    class_names = []
    fs_values = []
    bw_values = []
    for dataset_file in dataset_files:
        rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file) # rx_signal: nRows, length, I/Q
        rx_signals.append(rx_signal)
        class_ids.append(class_id)
        class_names.append(class_name)
        fs_values.append(fs_value)
        bw_values.append(bw_value)        

    rx_signals = np.concatenate(rx_signals,axis=0)
    class_ids = np.concatenate(class_ids,axis=0)
    class_names = np.concatenate(class_names,axis=0)
    fs_values = np.concatenate(fs_values,axis=0)
    bw_values = np.concatenate(bw_values,axis=0)

    return rx_signals, class_ids, class_names, fs_values, bw_values

def LoadSpecialMultiHdfsDataset(folders:list[str], sProp):
    '''
    加载多个数据集
    '''
    dataset_files = []
    ext_name = 'hdf5'
    for folder in folders:
        for file in os.listdir( folder ):
            if file.endswith( ext_name ) and sProp in file:# os.path.splitext(name)[1] == suffix:
                dataset_file = os.path.join(folder, file)
                dataset_files.append(dataset_file) #数据集
    
    rx_signals = []
    class_ids = []
    class_names = []
    fs_values = []
    bw_values = []
    for dataset_file in dataset_files:
        rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file) # rx_signal: nRows, length, I/Q
        rx_signals.append(rx_signal)
        class_ids.append(class_id)
        class_names.append(class_name)
        fs_values.append(fs_value)
        bw_values.append(bw_value)     

    rx_signals = np.concatenate(rx_signals,axis=0)
    class_ids = np.concatenate(class_ids,axis=0)
    class_names = np.concatenate(class_names,axis=0)
    fs_values = np.concatenate(fs_values,axis=0)
    bw_values = np.concatenate(bw_values,axis=0)

    return rx_signals, class_ids, class_names, fs_values, bw_values