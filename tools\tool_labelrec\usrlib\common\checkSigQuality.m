function []=checkSigQuality(folderpath, StartID)
%  Function    ：checkSigQuality
%  Description : 检查文件夹下的bvsp信号质量
%  Parameter   : foldername       -- 文件夹路径名称
%                
%  Return      :
%
%  Author      : Liuzhiguo
%  Date        : 2025-06-13

if exist(folderpath,"file")<=0
    error("文件夹%s:不存在\n",folderpath);
end

lblfile = fullfile(folderpath,"Train_records_sorted.txt");
if exist(lblfile,"file")>0
    Table_train = readtable(lblfile,'Format','%s [%d,%d] %f %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc","snr"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    %Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", lblfile);
end

folder_B1 = fullfile(folderpath,"B1");%强干扰
if exist(folder_B1,"dir")<=0
    mkdir(folder_B1);
end

folder_B2 = fullfile(folderpath,"B2");%弱干扰
if exist(folder_B2,"dir")<=0
    mkdir(folder_B2);
end

folder_B3 = fullfile(folderpath,"B3");%低信噪比
if exist(folder_B3,"dir")<=0
    mkdir(folder_B3);
end

%% 逐条验证
for iRow = StartID : height(Table_train)
    sigfsubname          = char(Table_train.filename(iRow));%文件名称
    % 使用fileparts函数提取文件名
    [~, filename, ext] = fileparts(sigfsubname);
    sigfsubname = [filename, ext];
    %clsname          = split(sigfsubname,'\',1);       %文件类别名名称

    sigfname         = fullfile(folderpath, sigfsubname);    %bvsp文件路径
    nStartpos_t      = Table_train.startpos(iRow);      %起始点
    nEndpos_t        = Table_train.endpos(iRow);      %结束点
    nb_fc               = Table_train.fc(iRow);      %中心频率
    lenPoints        = Table_train.lenPoints(iRow);%长度

    if exist(sigfname,"file")<=0
        fprintf("%s:不存在，跳过\n", sigfname);
        continue;
    end

    metafsubname = [filename, '.meta'];
    metafname        = fullfile(folderpath, metafsubname);    
    
    Stitle = sprintf('记录号:%d,文件:%s,数据点:[%d,%d],中心频率=%.3f MHz\n',iRow, sigfsubname,nStartpos_t,nEndpos_t,nb_fc/1e6);
    disp(Stitle);
    if nb_fc < 100e6
        error("[错误]中心频率应该>100MHz");
    elseif nb_fc > 10e9
        error("[错误]中心频率应该<10GHz");
    end
    if lenPoints < 61.44e6*0.0005
        error("数据长度应大于0.5ms");
    end
    bRepeat = true;
    while bRepeat
        [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
        showWBSig(wb_signal,wb_fc,wb_fs,Stitle,[nStartpos_t nEndpos_t],nb_fc);

        input('展示细节，按回车');
        [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
        %[rising_edges_count, falling_edges_count] = DetectEdgeCount(real(wb_signal(nStartpos_t:nEndpos_t)));
        showWBSig(wb_signal(nStartpos_t:nEndpos_t),wb_fc,wb_fs,Stitle);
        bOK = DetectSigSpread(wb_signal(nStartpos_t:nEndpos_t), wb_fs, wb_fc, nb_fc);
        %pause();
        x = input('继续下一个，按回车，其他选择：1-起始位置干扰, 2-中间干扰, 3-低信噪比, 8-查看全貌, 9-退出\n');
        if x == 8
            continue;
        else
            break;
        end
    end

    if x==9
        break;
    elseif x==1 %起始位置误判
        newFilePath = fullfile(folder_B1, sigfsubname);
        movefile(sigfname, newFilePath);
        newFilePath = fullfile(folder_B1, metafsubname);        
        movefile(metafname, newFilePath);
        fprintf("移动到:%s 文件夹下\n",folder_B1);
    elseif x==2 %中间干扰
        newFilePath = fullfile(folder_B2, sigfsubname);
        movefile(sigfname, newFilePath);
        newFilePath = fullfile(folder_B2, metafsubname);        
        movefile(metafname, newFilePath);
        fprintf("移动到:%s 文件夹下\n",folder_B2);
    elseif x==3 %低信噪比
        newFilePath = fullfile(folder_B3, sigfsubname);
        movefile(sigfname, newFilePath);  
        newFilePath = fullfile(folder_B3, metafsubname);        
        movefile(metafname, newFilePath);
        fprintf("移动到:%s 文件夹下\n",folder_B3);
    elseif x==8
        continue;
    end
end


fprintf('文件夹%s:检查完成!\n', folderpath);
end
