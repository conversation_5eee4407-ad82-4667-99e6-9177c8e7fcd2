function [nSigChecked] = func_scansignal(filename_in, cls_id, clip_ms,nb_bw_def,startPosOffset, bshowdgraph)
%  Function    ：func_scansignal
%  Description : 根据输入文件filename_in，标记信号的开始及结束位置，中心频率等信息，并保存为hdfs文件（fname_dataset）
%
%  Parameter   : filename_in   -- 输入文件
%                cls_id        -- 类别标识(方便AI信号检测用)
%                clip_ms       -- 限制的最大时间长度，可选输入
%                nb_bw_def     -- 信号带宽，可选输入
%                startPosOffset -- 起始位置偏差补偿值，可选输入
%                bshowdgraph    -- 是否显示过程中的图形
%
%  Return      :
%                nSigChecked   -- 检测到信号标识
%
%  Author      : Liuzhiguo
%  Date        : 2025-02-06
%

%% 1. 读取信号
[~,~,ext_fin] = fileparts(filename_in);
if ext_fin == ".wav"
    [wb_data, wb_fs] = audioread(filename_in);
    wb_signal = wb_data(:,1)+1j*wb_data(:,2);
    wb_fc = 2.4e9;%默认设置值
    wb_bw = 20e6;
elseif ext_fin == ".hdfv" % 自定义波形文件
    [wb_signal, metadata] = readSignalFromHDF5(filename_in);
    wb_fs = metadata.fs;
    wb_fc = metadata.fc;
    wb_bw = metadata.bw;
else
    %[nb_rxSig,wb_rxSig] = readCapture(file,ischannelized,nb_bw,nb_fs,nb_fc,isengine);
    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filename_in);
end

% wb_signal = awgn(wb_signal,10,'measured');
if exist('bshowdgraph','var')<=0
    bshowdgraph=false;
end

curstartPosOffset=0;
if exist('startPosOffset','var')>0
    curstartPosOffset = startPosOffset;
end

if bshowdgraph
    figure(111);
    plot(real(wb_signal))
    hold on
    plot(imag(wb_signal));title('采集信号');
    hold off
end

if exist("clip_ms","var")<=0
    clip_ms = 2;%1.73;%nb433
end
const_ms = 6;
if  clip_ms > const_ms
    clip_ms = const_ms;
end

%生成输出文件路径
% outpath = 'E:\project\tool_dataset\outdataset\test\sam\';
% outname = 'fchanscan';
% fname_dataset = strcat(outpath,outname,'-S1','.hdf5');
InitMyParams;%初始化系统路径等参数
fname_dataset = myconfig.CaptureParseOut;
[outpath, outname, ext] = fileparts(fname_dataset);
if exist(fname_dataset,"file")>0
    delete(fname_dataset);
elseif exist(outpath,"dir")<=0
    fprintf("路径%s不存在,请重新设置!\n", outpath);
    return;
end

%% 2 功率谱 [频率fft，时间]
data = wb_signal;
fft_len = 2048;%2048;       %fft长度

overlap_mode = true;%重叠模式
if overlap_mode==true
    fft_overlap = fft_len/2;
    % data_x = xcorr(data,"biased");
    % lendata = length(data);
    % data_x = data_x(floor(lendata/2):floor(lendata/2)+lendata-1);
    % window = hamming(fft_len,"periodic");
    window = blackman(fft_len,"periodic");
    
    psd_value_out = stft(data,wb_fs,"Window",window,"OverlapLength",fft_overlap,"FFTLength",fft_len,"FrequencyRange","centered");%
    col = length(psd_value_out(1,:));
    psd_value_out = abs(psd_value_out)/fft_len;%[f, t]
    %
    % 注：stft已经做了频域shift和/fft_len归一化
    %[psd_value,f,t] = stft(data,wb_fs,"Window",hamming(fft_len),"OverlapLength",fft_overlap,"FFTLength",fft_len);
    %验证：the1fft = abs(fft(data(1:fft_len).*hamming(fft_len)))/fft_len;the1fftp = fftshift(the1fft);
    %'periodic'（周期窗）
    %在进行频谱分析时，为了避免频谱泄漏，通常需要对信号进行加窗处理。使用周期窗可以确保加窗后的信号在进行离散傅里叶变换（DFT）时，能够正确地表示信号的周期性，从而得到更准确的频谱估计。
else
    len = length(data);
    signal = data(1:floor(len/fft_len)*fft_len); %将数据长度变为ft_len的整倍数
    signal = reshape(signal,[fft_len,floor(len/fft_len)]);
    [~,col] = size(signal);%

    bwithwindow = true;%加窗处理,使频率信号向中心集中
    if bwithwindow==true
        w = hamming(fft_len,"periodic");%信号
        for n=1:col
            signal(:,n) = signal(:,n).*w;
        end
    end
    psd_value_out = abs(fft(signal))/fft_len; %用fft长度进行归一化
    psd_value_out = fftshift(psd_value_out, 1); %频率点上功率值翻转，dim=1 表示沿着第一个dim翻转,谱域翻转 [f,t]
end

for n=1:col            %归一化
    psd_value_out(:,n) = 20*log10(psd_value_out(:,n))-56.8;%等效到前端功率值
end

%% 3 频域滤波，消除毛刺
%fir filter
M = size(psd_value_out, 1);
N = size(psd_value_out, 2);
L_filter = 48;
b = fir1(L_filter, 0.05); % 0.05
psd_filtered = zeros(M, N);
for i = 1:N
    psd_filtered(:, i) = filter(b, 1, psd_value_out(:, i));% [f, t]
end

if fft_len==2048
    sc = 90;%频域留白，截取中心频率
elseif fft_len==1024
    sc = 50;
end
t  = 1:col;%[freqs, time]
f  = (-fft_len/2+sc:1:fft_len/2-1-sc)*(wb_fs/fft_len)+wb_fc;
psd_filtered_cliped = psd_filtered(sc+1+L_filter/2:end-sc+L_filter/2,t);

if bshowdgraph
    figure(112)
    subplot(211)
    surf(t,f,psd_value_out(sc+1:end-sc,t),'edgecolor','none'); axis tight;
    view(0,90);title('功率谱密度图 3D');
    subplot(212)
    surf(t,f,psd_filtered_cliped,'edgecolor','none'); axis tight;
    view(0,90);title('滤波后功率谱密度图 3D');
end

%% 4 通过阈值，二值化
if overlap_mode==true 
    snr_threhold = (max(psd_filtered_cliped,[],"all")-mean(psd_filtered_cliped,"all"))*0.5;%计算，并滤除小信号
else
    snr_threhold = 25;
end
%snr_tuned = snr_threhold;
nLen_symbols = length(psd_filtered_cliped(1,:)); %[freqs, time]
all_psd = zeros(4,nLen_symbols);
for n=1:nLen_symbols
    meanpsd = mean(psd_filtered_cliped(:,n));     % 平均功率
    maxpsd = max(psd_filtered_cliped(:,n));
    %minpsd = min(psd_mean(:,n));
    % if maxpsd > (snr_threhold+meanpsd)*1.5
    %     snr_tuned = snr_threhold;
    % end
    snr_tuned = (maxpsd - meanpsd)*0.75;%0.85;%调整后的snr，目的为保证带宽估计比较精确
    if snr_tuned < snr_threhold %小于阈值
        snr_tuned = snr_threhold;
    else
        if bshowdgraph
            %fprintf("snr_tuned=%.3f dBm\n",snr_tuned);
        end
    end
    all_psd(1,n) = meanpsd;
    all_psd(2,n) = maxpsd;
    all_psd(3,n) = maxpsd-meanpsd;
    all_psd(4,n) = snr_tuned;
    psd_filtered_cliped(:,n) = psd_filtered_cliped(:,n) > (meanpsd+snr_tuned);
end

fprintf('[chanscan] snr_threhold=%d min_psd=%.2f max_psd=%.2f\n',snr_threhold, min(all_psd(1,:)),max(all_psd(1,:)));

%对原有图像进行膨胀算法
% 定义结构元素
se = strel("rectangle", [1,4]); % 其中，'rectangle' 表示要创建的结构元素形状为矩形，
% [m n] 是一个包含两个元素的向量，m 表示矩形的行数，n 表示矩形的列数。
% 进行膨胀操作
psd_value_out = imdilate(psd_filtered_cliped, se);
%psd_value_out = customImDilate(psd_filtered_cliped, se);
disp(sum(psd_value_out(:))) %所有为1的元素和

if bshowdgraph
    figure(113);
    subplot(4,1,1);plot(all_psd(1,:));title('时间-功率谱密度 mean');
    subplot(4,1,2);plot(all_psd(2,:));title('时间-功率谱密度 max');
    subplot(4,1,3);plot(all_psd(3,:));title('时间-功率谱密度 max-mean');
    subplot(4,1,4);plot(all_psd(4,:));title('时间-功率谱密度 tuned');

    figure(114);
    subplot(211);
    surf(t,f,psd_value_out,'edgecolor','none'); axis tight;
    view(0,90);title('二值化后 3D功率谱密度图');
end

%% 5 计算频率联通区域
if overlap_mode
    fft_step = fft_overlap;
else
    fft_step = fft_len;
end
%可以做
[label] = calunionregion(psd_value_out,wb_signal,t,f,wb_fs/fft_len,fft_step,wb_fc,bshowdgraph);

%% 6 根据label提取待检测信号，并保存为数据文件hdf5格式

sigfsubname = split(filename_in,'\');
sigfsubname = sigfsubname(end);
indexPointer = 0;

min_clippower = findMinPower(wb_signal);%功率阈值
powerthreshold_region = min_clippower*2.1;

for n=1:length(label)
    if(label(n).valid)
        % 6.1 判断信号开始及结束位置
        if label(n).start_idx <= 10 || label(n).stop_idx >= length(wb_signal) - 10
            fprintf("文件:%s [%d, %d] 信号被截断,跳过该条\n", "h9364Capture.dat", label(n).start_idx, label(n).stop_idx);
            continue;
        end


        lbl_startpos = label(n).start_idx+1+curstartPosOffset;
        if lbl_startpos <= 0
            lbl_startpos = 1;
        end
        lbl_endpos = label(n).stop_idx-curstartPosOffset;

        % 6.2 修正起始位置
        if curstartPosOffset==0 %起始位置为默认值, 可修正
            [lbl_startpos] = findAccuractPos(wb_signal, lbl_startpos, 2*fft_len);
            % [lbl_startpos] = findAccuractPos(wb_signal, lbl_startpos, fft_len);

            %重新标定起始点,结束点,对于信号叠加同频干扰时，单纯幅度判别，会引起误判,所以注释掉起始位置修正代码
            % [lbl_startpos] = findTheStartPos(wb_signal, lbl_startpos, lbl_endpos);
            % if lbl_startpos <= 10 || lbl_endpos >= length(wb_signal) - 10
            %     fprintf("文件:%s [%d, %d] 信号被截断,跳过该条\n", "h9364Capture.dat", lbl_startpos, lbl_endpos);
            %     continue;
            % end
            % [lbl_endpos] = findTheEndPos(wb_signal, lbl_startpos, lbl_endpos);
            % if lbl_startpos <= 10 || lbl_endpos >= length(wb_signal) - 10
            %     fprintf("文件:%s [%d, %d] 信号被截断,跳过该条\n", "h9364Capture.dat", lbl_startpos, lbl_endpos);
            %     continue;
            % end
        end

        clip_len = floor(wb_fs*clip_ms/1e3);
        max_len  = floor(wb_fs*const_ms/1e3);
        if lbl_endpos>lbl_startpos+max_len
            lbl_endpos=lbl_startpos+max_len;
        elseif lbl_endpos<lbl_startpos+floor(clip_len/2) %不足一半长度(1ms)
            fprintf("[chanscan] 序号:%d \t 采集文件:%s [%d,%d] \t 持续时间为%.2f ms << clip_ms=%.2f ms, 长度太短, 不处理\n", ...
                n,cell2mat(sigfsubname),lbl_startpos,lbl_endpos, (lbl_endpos-lbl_startpos)*1e3/wb_fs, clip_ms/2);
            continue;
        else %按照实际长度
            %lbl_endpos=lbl_startpos+clip_len;
        end
        if lbl_endpos < length(wb_signal) % 不能超出信号范围
            signal = wb_signal(lbl_startpos:lbl_endpos);
        else
            signal = wb_signal(lbl_startpos:end);
        end

        %6.3 判断是否越界
        myclip_sig = signal;
        if ext_fin ~= ".hdfv" && isOverThresholdM(myclip_sig)
            fprintf("文件:%s [%d, %d] 数据点值超过2047,跳过该条\n", "h9364Capture.dat", lbl_startpos, lbl_endpos);
            continue;
        end

        avgpower_cursig = sqrt(mean(abs(myclip_sig).^2));%平均功率
        if avgpower_cursig < powerthreshold_region
            fprintf("文件:%s [%d, %d] 切片功率弱于1.1平均信号RMS,跳过该条\n", "h9364Capture.dat", lbl_startpos, lbl_endpos);
            continue;      
        end

        nb_bw = label(n).bw;
        % 6.4 滤除宽带信号
        if nb_bw > 1e6 %宽带带宽
            fprintf("[chanscan] 序号:%d \t 采集文件:%s [%d,%d] \t 带宽%.2f MHz > 1MHz, 带宽过大, 不处理\n", ...
                n,cell2mat(sigfsubname),label_start(n),label_end(n), nb_bw / 1e6);
            continue
        end
		
        nb_bw = nb_bw*0.8; %change 1.4
        if nb_bw>4e6
            nb_bw = 4e6;%截取带宽
        end
        %nb_bw = 80e3;
        nb_bw_prev = nb_bw;
        nb_fc = label(n).centor_freq;
        if exist('nb_bw_def','var')>0
            nb_bw = nb_bw_def;
        else %带宽为默认值是，可修正
            [nb_fc, nb_bw] = findAccuracteBW(signal, nb_fc, nb_bw, wb_fc, wb_fs, fft_len);
        end

        % 方式 (1) 输出固定长度
        % nb_len_set = 6e4;%如果为4M采样率，则点数=4e6*len_signal_bw/wb_fs
        % nb_fs   = 2*nb_bw;   % 窄带信号的采样率
        % [nb_signal] = ExtractNBSig(signal, wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs,nb_len_set);%信道化后数据
        %  nb_fs_effct = nb_len_set*(wb_fs/len_signal_bw);%有效采样率

        % 6.5 方式 (2) 输出固定采样率
        nb_fs = 4e6;
        [nb_signal] = ExtractNBSig(signal, wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs);%信道化后数据
        nb_fs_effct = nb_fs;
        len_clip_nb = length(nb_signal);
        if len_clip_nb==0
            warning("invalid len_clip_nb=%d\n", len_clip_nb);
            continue;
        end
        % 6.6 保存文件
        indexPointer = indexPointer+1;

        fprintf("[chanscan] 序号:%d (id=%d) \t 采集文件:%s \t [%d,%d]\t fs_wb=%.2f M fc=%.2f MHz \t fs_nb=%.2f M \t BW=%.2f(%.2f) K \t 分类ID:%d \t 数据点数:%d \t 持续时间为%.2f ms\n", ...
            n,indexPointer,cell2mat(sigfsubname),lbl_startpos,lbl_endpos, wb_fs/1e6,...
            nb_fc/1e6,nb_fs_effct/1e6, nb_bw/1e3, nb_bw_prev/1e3, cls_id, len_clip_nb, len_clip_nb*1e3/nb_fs_effct);
        WrTrainSig(fname_dataset, nb_signal, cls_id, sigfsubname, nb_fc, nb_fs_effct, nb_bw, indexPointer);  % 生成数据集，hdf文件格式

        % figure
        % plot(real(nb_signal))
        % hold on
        % plot(imag(nb_signal))
        % hold off
        % 按 6W点折算采样率
        if bshowdgraph
            %nb_fs_effct = 6e4*wb_fs/len_signal_bw;
            %stitle = sprintf("文件:%s \n 截取窄带信号数据点数为%d，持续时间为%.2f ms\r\n",filename_in,length(nb_signal), len_signal_bw*1e3/wb_fs);
            %ShowTimeAndSpectral(nb_signal,  nb_fc, nb_fs_effct, stitle, 1024);
        end

    end
end
nSigChecked = indexPointer;
end

function [min_clippower]=findMinPower(wave_data)
%
% 以切片方式寻找最小rms片段功率值
%
window_size = 1000; % 窗口采样点数
num_windows = floor(length(wave_data) / window_size);%窗口格式
min_clippower = 0;
for i=1:num_windows
    start_idx = (i-1) * window_size + 1;
    end_idx = i * window_size;
    window = wave_data(start_idx:end_idx);
    rms_clippower = sqrt(mean(abs(window).^2));%RMS
    if i==1
        min_clippower = rms_clippower;
    else
        if rms_clippower < min_clippower
            min_clippower = rms_clippower;
        end
    end
end

end

%第二重保障--统计切片信号中越界点数，暂时以越界点数超过1/3信号长度来判断数据越界
%是否大多数越界
function flag = isOverThresholdM(clip_signal)
signal_real = real(clip_signal);
signal_imag = imag(clip_signal);
flag = 0;
count_real = 0;
count_imag = 0;
signal_real_value = abs(signal_real);
signal_imag_value = abs(signal_imag);
for n_real = 1 :length(signal_real_value)
    if signal_real_value(n_real) > 2000
        count_real = count_real + 1;
    end
end
% disp(count_real)
for n_imag = 1 :length(signal_imag_value)
    if signal_imag_value(n_imag) > 2000
        count_imag = count_imag + 1;
    end
end
%有1/8点越界
percent = 1/8;
if count_real >= length(signal_real)*percent || count_imag >= length(signal_real_value)*percent || (count_imag+count_real)>200
    flag = 1;
end
end
