
function generateMultiClassHDF5_fixed_labels(dataset_root, output_hdf5, varargin)
    % 生成调制识别信号HDF5数据集（固定标签顺序）
    % 强制标签顺序: 0:BPSK, 1:QPSK, 2:16QAM, 3:G<PERSON><PERSON>, 4:LoRa, 5:Noise
    % 参数：
    %   dataset_root   - 数据集根目录（包含按调制类型分类的子文件夹）
    %   output_hdf5    - 输出HDF5文件路径
    % 可选参数：
    %   'TargetLength'       - 信号长度
    %   'MaxSamplesPerClass' - 每类最大样本数
    %   'MaxSamplesPerRecord'- 单条记录最大截取数
    % 示例：
    %   generateMultiClassHDF5_fixed_labels('data/modulations', 'dataset.h5', ...
    %       'MaxSamplesPerClass', 500, 'TargetLength', 1024);

    p = inputParser;
    addParameter(p, 'TargetLength', 3072, @isnumeric);
    addParameter(p, 'MaxSamplesPerClass', 200, @isnumeric);
    addParameter(p, 'MaxSamplesPerRecord', 7, @isnumeric);
    addParameter(p, 'RecordFile', 'val.txt', @ischar);
    % 窄带化参数
    addParameter(p, 'Narrowband', true, @islogical);
    addParameter(p, 'NB_Bandwidth', 2000e3, @isnumeric);
    addParameter(p, 'NB_SampleRate', 3.84e6, @isnumeric);
    parse(p, varargin{:});

    % 获取窄带化参数
    do_narrowband = p.Results.Narrowband;
    nb_bw = p.Results.NB_Bandwidth;
    nb_fs = p.Results.NB_SampleRate;

    %% 初始化数据结构
    all_signals = {};
    all_labels = {};
    all_snrs = [];
    all_freqs = [];

    % 预定义调制类型顺序
    %fixed_mod_types = {'BPSK', 'QPSK', '16QAM', 'GFSK', 'LoRa','Noise'};
    fixed_mod_types = {'BPSK', 'QPSK', '16QAM', 'GFSK', 'LoRa'};
    class_counts = containers.Map(fixed_mod_types, zeros(1,5));

    %% 主循环处理
    for mod_idx = 1:length(fixed_mod_types)
        mod_name = fixed_mod_types{mod_idx};
        mod_folder = fullfile(dataset_root, mod_name);
        train_file = fullfile(mod_folder, p.Results.RecordFile);

        if ~exist(train_file, 'file')
            warning('跳过 %s (缺少记录文件)', mod_name);
            continue;
        end

        fprintf('\n[%d/%d] 处理 %s (目标样本数: %d)\n', ...
            mod_idx, length(fixed_mod_types), mod_name, p.Results.MaxSamplesPerClass);

        % 读取记录文件获取中心频率信息
        [signals, labels, snrs, freqs, record_infos] = readTrainRecordsWithInfo(...
            train_file, mod_folder, mod_name, ...
            p.Results.TargetLength, p.Results.MaxSamplesPerRecord, ...
            p.Results.MaxSamplesPerClass,dataset_root);

        % 如果需要窄带化处理

        if do_narrowband
            fprintf('正在进行窄带化处理 (使用记录的中心频率, 带宽=%.1fkHz)...\n', nb_bw/1e3);
            
            % 创建标记数组
            valid_indices = true(length(signals), 1);
            failed_count = 0;
            
            for i = 1:length(signals)
                try
                    nb_bw_actual = nb_bw;
                    
                    % 窄带化处理
                    [nb_signal] = ExtractNBSig(...
                        signals{i}, ...
                        record_infos{i}.wb_fc, record_infos{i}.wb_bw, record_infos{i}.wb_fs, ...
                        record_infos{i}.fc, nb_bw_actual, nb_fs, 192);
                    
                    fprintf('信号 %d - 窄带化前点数: %d, 窄带化后点数: %d\n', ...
                        i, length(signals{i}), length(nb_signal));
                    
                    signals{i} = nb_signal;
                catch ME
                    warning('窄带化失败[%s]: %s', mod_name, ME.message);
                    valid_indices(i) = false;
                    failed_count = failed_count + 1;
                end
            end
            
            % 移除失败的样本
            if failed_count > 0
                fprintf('移除 %d 个窄带化失败的样本\n', failed_count);
                signals = signals(valid_indices);
                labels = labels(valid_indices);
                snrs = snrs(valid_indices);
                freqs = freqs(valid_indices);
                record_infos = record_infos(valid_indices);
                
                % 补充新样本
                if length(signals) < p.Results.MaxSamplesPerClass
                    needed = p.Results.MaxSamplesPerClass - length(signals);
                    fprintf('需要补充 %d 个样本\n', needed);
                    
                    % 调用补充样本函数
                    [new_signals, new_labels, new_snrs, new_freqs, new_infos] = ...
                        supplementSamples(train_file, mod_folder, mod_name, ...
                        p.Results.TargetLength, needed, dataset_root, do_narrowband, ...
                        nb_bw, nb_fs);
                    
                    % 合并新样本
                    signals = [signals; new_signals];
                    labels = [labels; new_labels];
                    snrs = [snrs; new_snrs];
                    freqs = [freqs; new_freqs];
                    record_infos = [record_infos; new_infos];
                end
            end
        end

        % 合并数据
        all_signals = [all_signals; signals];
        all_labels = [all_labels; labels];
        all_snrs = [all_snrs; snrs];
        all_freqs = [all_freqs; freqs];
        class_counts(mod_name) = length(signals);
    end

    %% 数据标准化处理
    uniform_signals = zeros(length(all_signals), p.Results.TargetLength);
    for i = 1:length(all_signals)
        sig = all_signals{i};
        sig_len = min(length(sig), p.Results.TargetLength);
        uniform_signals(i, 1:sig_len) = sig(1:sig_len);
    end

    signal_data_3d = zeros(size(uniform_signals,1), 2, p.Results.TargetLength);
    signal_data_3d(:,1,:) = real(uniform_signals);
    signal_data_3d(:,2,:) = imag(uniform_signals);

    %% 标签数值化
    numeric_labels = zeros(length(all_labels), 1, 'uint8');
    for i = 1:length(all_labels)
        idx = find(strcmp(fixed_mod_types, all_labels{i}));
        numeric_labels(i) = uint8(idx - 1);
    end

    %% 写入HDF5文件
    if exist(output_hdf5, 'file')
        delete(output_hdf5);
    end

    h5create(output_hdf5, '/signal_data', size(signal_data_3d), 'Datatype', 'single');
    h5write(output_hdf5, '/signal_data', single(signal_data_3d));

    h5create(output_hdf5, '/labels', size(numeric_labels), 'Datatype', 'uint8');
    h5write(output_hdf5, '/labels', numeric_labels);

    h5create(output_hdf5, '/SNRs', size(all_snrs), 'Datatype', 'single');
    h5write(output_hdf5, '/SNRs', single(all_snrs));

    h5create(output_hdf5, '/center_freqs', size(all_freqs), 'Datatype', 'single');
    h5write(output_hdf5, '/center_freqs', single(all_freqs));

    %% 添加元数据属性
    label_meaning = {'0:BPSK', '1:QPSK', '2:16QAM', '3:GFSK', '4:LoRa'};
    h5writeatt(output_hdf5, '/', 'modulations', fixed_mod_types);
    h5writeatt(output_hdf5, '/', 'LabelMeaning', label_meaning);
    h5writeatt(output_hdf5, '/', 'sample_rate', nb_fs); % 使用窄带采样率（如果窄带化）
    h5writeatt(output_hdf5, '/', 'snr_range', [min(all_snrs), max(all_snrs)]);
    h5writeatt(output_hdf5, '/', 'center_freq_range', [min(all_freqs), max(all_freqs)]);



    fprintf('\nHDF5数据集生成完成: %s\n', output_hdf5);
    % if do_narrowband
    %     fprintf('窄带化参数: 中心频率=%.1fMHz, 带宽=%.1fkHz, 采样率=%.1fMHz\n', ...
    %         nb_fc/1e6, nb_bw/1e3, nb_fs/1e6);
    % end
end

function [signals, labels, snrs, freqs, record_infos] = ...
         readTrainRecordsWithInfo(train_records_file, mod_folder, mod_name, ...
                                 target_length, max_per_record, max_per_class, dataset_root)
    % 初始化
    signals = cell(min(max_per_class, 1000), 1);
    labels = cell(size(signals));
    snrs = zeros(size(signals));
    freqs = zeros(size(signals));
    record_infos = cell(size(signals));
    sample_count = 0;

    % 读取记录文件
    fid = fopen(train_records_file, 'r');
    record_list = textscan(fid, '%s', 'Delimiter', '\n');
    fclose(fid);
    record_list = record_list{1};
    record_list = record_list(randperm(length(record_list)));

    % 处理每条记录
    for record_idx = 1:length(record_list)
        if sample_count >= max_per_class
            break;
        end

        line = record_list{record_idx};
        parts = strsplit(line, ';');
        if length(parts) < 4
            continue;
        end

        % 修正路径拼接：直接使用parts{1}，避免重复子目录
        filepath = fullfile(dataset_root, parts{1});
        seg_range = sscanf(parts{2}, '[%d,%d]');
        fc = str2double(parts{3});
        snr = str2double(parts{4});

        % 加载信号并存储宽带参数
        try
            [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filepath);
            full_segment = wb_signal(seg_range(1):seg_range(2))';

            % 存储记录信息（包括宽带参数）
            info.filename = parts{1};
            info.startpos = seg_range(1);
            info.endpos = seg_range(2);
            info.fc = fc;
            info.snr = snr;
            info.wb_fc = wb_fc;
            info.wb_bw = wb_bw;
            info.wb_fs = wb_fs;
        catch ME
            warning('文件 %s 读取失败: %s', filepath, ME.message);
            continue;  % 跳过当前文件继续处理下一个
        end

        % 截取信号段
        samples_to_take = min(max_per_record, max_per_class - sample_count);
        for k = 1:samples_to_take
            % 计算可用范围（中间1/2部分）
            full_length = length(full_segment);
            quarter_point = round(full_length/4);
            three_quarter_point = round(3*full_length/4);

            % 确保有足够的长度
            available_length = three_quarter_point - quarter_point;
            if available_length < target_length
                warning('信号中间部分长度不足: %s (需要%d，实际%d)', filepath, target_length, available_length);
                break;
            end

            % 在中间1/2范围内随机选择起始位置
            max_start_pos = three_quarter_point - target_length;
            idx_start = randi([quarter_point, max_start_pos]);
            idx_end = idx_start + target_length - 1;

            sample_count = sample_count + 1;
            signals{sample_count} = full_segment(idx_start:idx_end);
            labels{sample_count} = mod_name;
            snrs(sample_count) = snr;
            freqs(sample_count) = fc;
            record_infos{sample_count} = info; % 包含宽带参数
        end
    end

    % 裁剪多余空间
    signals = signals(1:sample_count);
    labels = labels(1:sample_count);
    snrs = snrs(1:sample_count);
    freqs = freqs(1:sample_count);
    record_infos = record_infos(1:sample_count);
end

function [signals, labels, snrs, freqs, record_infos] = ...
    supplementSamples(train_file, mod_folder, mod_name, target_length, ...
                    needed_samples, dataset_root, do_narrowband, nb_bw, nb_fs)
    
    % 初始化
    signals = cell(needed_samples, 1);
    labels = cell(needed_samples, 1);
    snrs = zeros(needed_samples, 1);
    freqs = zeros(needed_samples, 1);
    record_infos = cell(needed_samples, 1);
    sample_count = 0;
    
    % 读取记录文件
    fid = fopen(train_file, 'r');
    record_list = textscan(fid, '%s', 'Delimiter', '\n');
    fclose(fid);
    record_list = record_list{1};
    record_list = record_list(randperm(length(record_list)));
    
    % 处理每条记录直到补充足够的样本
    for record_idx = 1:length(record_list)
        if sample_count >= needed_samples
            break;
        end
        
        line = record_list{record_idx};
        parts = strsplit(line, ';');
        if length(parts) < 4
            continue;
        end
        
        filepath = fullfile(dataset_root, parts{1});
        seg_range = sscanf(parts{2}, '[%d,%d]');
        fc = str2double(parts{3});
        snr = str2double(parts{4});
        
        try
            [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filepath);
            full_segment = wb_signal(seg_range(1):seg_range(2))';
            
            % 存储记录信息
            info.filename = parts{1};
            info.startpos = seg_range(1);
            info.endpos = seg_range(2);
            info.fc = fc;
            info.snr = snr;
            info.wb_fc = wb_fc;
            info.wb_bw = wb_bw;
            info.wb_fs = wb_fs;
            
            % 截取信号段
            full_length = length(full_segment);
            quarter_point = round(full_length/4);
            three_quarter_point = round(3*full_length/4);
            
            if (three_quarter_point - quarter_point) < target_length
                continue;
            end
            
            max_start_pos = three_quarter_point - target_length;
            idx_start = randi([quarter_point, max_start_pos]);
            idx_end = idx_start + target_length - 1;
            
            sample_count = sample_count + 1;
            signals{sample_count} = full_segment(idx_start:idx_end);
            labels{sample_count} = mod_name;
            snrs(sample_count) = snr;
            freqs(sample_count) = fc;
            record_infos{sample_count} = info;
            
            % 窄带化处理
            if do_narrowband
                try
                    [nb_signal] = ExtractNBSig(...
                        signals{sample_count}, ...
                        info.wb_fc, info.wb_bw, info.wb_fs, ...
                        info.fc, nb_bw, nb_fs, target_length);
                    signals{sample_count} = nb_signal;
                catch ME
                    warning('补充样本窄带化失败: %s', ME.message);
                    sample_count = sample_count - 1; % 回退计数
                end
            end
        catch
            continue;
        end
    end
    
    % 裁剪多余空间
    signals = signals(1:sample_count);
    labels = labels(1:sample_count);
    snrs = snrs(1:sample_count);
    freqs = freqs(1:sample_count);
    record_infos = record_infos(1:sample_count);
end