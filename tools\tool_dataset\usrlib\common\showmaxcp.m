function [vmaxcp, pos_cp] = showmaxcp(signal_td, N_fft, cp_max)
%  Function    ：showmaxcp
%  Description : 计算相关值，并显示图表
%                主要目的为：将cp设置的较长，出现多个相关峰，确定合适的长度
%                原理：
%                     如果cp_max取足够长如100，就会出现多个相关峰情况，峰间隔为fft+cp
%
%  Parameter   : signal_td    -- 复信号(时域)
%                    N_fft    -- FFT长度
%
%  output      : vmaxcp       -- 最大cp值
%                pos_cp       -- 最大cp值对应位置
%
%  Author      : Liuzhiguo
%  Date        : 2024-07-01
figure;
[corr_param] = getcpcorrvals(signal_td, N_fft, cp_max,length(signal_td)-2*N_fft);%数据滑动长度变为length(signal_td)-2*N_fft
plot(corr_param);
[vmaxcp, pos_cp] = max(corr_param);
xlabel('滑动指针位置');
ylabel('对应的cp长度向量相关值');

stitle = sprintf('相关长度为%d时,滑动相关值', cp_max);
title(stitle);
end

