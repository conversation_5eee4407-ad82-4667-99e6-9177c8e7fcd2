function [] = ShowSpectrogram(a_channel, fs)
%SHOWSPECTROGRAM 此处显示有关此函数的摘要
%   此处显示详细说明
window = 4096;
noverlap = window/2;
nfft = window;
g = hann(nfft,"periodic");%,window
%centered
[S,F,T,P]=spectrogram(a_channel, window, noverlap, nfft, fs,'yaxis');       %频谱 瀑布图

% F = (-2048:1:2047)*0.015+wb_fc/1e6;%0.015=fs/4096/1e6;
figure;
surf(T*1000,F,10*log10(circshift(P,2048)),'edgecolor','none'); axis tight;
view(0,90);
xlabel('Time (ms)'); ylabel('MHz');
title(['  fs= ' num2str(fs/1000000) 'Mhz']);

end

