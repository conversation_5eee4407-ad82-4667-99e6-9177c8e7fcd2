# =======================================================================================================================
#   Function    ：DroneSigNet.py
#   Description : 无人机信号网络检测模型
#                 
# 
#   Parameter   : DroneSigNet 
#   Author      : Liuzhiguo
#   Date        : 2024-10-27
# =======================================================================================================================
import torch
import torch.nn as nn
import torch.nn.functional as F
from nets.Module_attentions import CBAM,  SE, DAM
import math

def autopad(k, p=None, d=1):  # kernel, padding, dilation
    """Pad to 'same' shape outputs."""
    if d > 1:
        k = d * (k - 1) + 1 if isinstance(k, int) else [d * (x - 1) + 1 for x in k]  # actual kernel-size
    if p is None:
        p = k // 2 if isinstance(k, int) else [x // 2 for x in k]  # auto-pad
    return p

class Conv(nn.Module):
    """Standard convolution with args(ch_in, ch_out, kernel, stride, padding, groups, dilation, activation)."""

    default_act = nn.SiLU()  # default activation

    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, d=1, act=True):
        """Initialize Conv layer with given arguments including activation."""
        super().__init__()
        self.conv = nn.Conv2d(c1, c2, k, s, autopad(k, p, d), groups=g, dilation=d, bias=False)
        self.bn = nn.BatchNorm2d(c2)
        self.act = self.default_act if act is True else act if isinstance(act, nn.Module) else nn.Identity()

    def forward(self, x):
        """Apply convolution, batch normalization and activation to input tensor."""
        return self.act(self.bn(self.conv(x)))

    def forward_fuse(self, x):
        """Perform transposed convolution of 2D data."""
        return self.act(self.conv(x))
    
class Bottleneck(nn.Module):
    """Standard bottleneck."""

    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        """Initializes a bottleneck module with given input/output channels, shortcut option, group, kernels, and
        expansion.
        """
        super().__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, k[0], 1)
        self.cv2 = Conv(c_, c2, k[1], 1, g=g)
        self.add = shortcut and c1 == c2

    def forward(self, x):
        """'forward()' applies the YOLO FPN to input data."""
        return x + self.cv2(self.cv1(x)) if self.add else self.cv2(self.cv1(x))
    
#
#  特征聚合：通过将不同Bottleneck模块的输出和原始特征图拼接在一起，C2F模块能够更好地聚合多尺度信息。
#  模型压缩：C2F模块中的卷积操作可以有效地压缩特征图，减少计算量，同时保持或增强模型的表达能力。
#     
class C2f(nn.Module):
    """Faster Implementation of CSP Bottleneck with 2 convolutions."""

    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        """Initialize CSP bottleneck layer with two convolutions with arguments ch_in, ch_out, number, shortcut, groups,
        expansion.
        """
        super().__init__()
        self.c = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, 2 * self.c, 1, 1)
        self.cv2 = Conv((2 + n) * self.c, c2, 1)  # optional act=FReLU(c2)
        self.m = nn.ModuleList(Bottleneck(self.c, self.c, shortcut, g, k=((3, 3), (3, 3)), e=1.0) for _ in range(n))

    def forward(self, x):
        """Forward pass through C2f layer."""
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        return self.cv2(torch.cat(y, 1))

    def forward_split(self, x):
        """Forward pass using split() instead of chunk()."""
        y = list(self.cv1(x).split((self.c, self.c), 1))
        y.extend(m(y[-1]) for m in self.m)
        return self.cv2(torch.cat(y, 1))
        
class DroneSigNet(nn.Module):
    '''
    无人机信号网络模型
    '''
    def __init__(self, embedding_size,  **kwargs):
        '''
            nc         : 分类个数
            ModelType  : 模型类别 0:分类 1:角向量
        '''
        super(DroneSigNet, self).__init__(**kwargs)

        self.subcarriers = 1024 #4096 # 61.44e6/4096=15e3, 对于窄带 4M/1024=3.9K
        self.timesymbols = 151  # 根据实际会有变化
        self.streams = 1        # 流个数
        #信号分成3段，分别赋予不同权重
        #self.weights = torch.tensor([0.5, 0.4, 0.1], requires_grad=True)
        self.weights = [0.5, 0.4, 0.1]
        #self.weights = self.weights.cuda()

        self.in_ch = 8 # 划分块数
        self.out_ch = 16#64#128 #channel数

        #注意力机制网络，目前未加入

        #信号网络
        self.blocks = nn.Sequential()
        #self.prebn = nn.BatchNorm2d(1)
    
        #conv1 = Conv(c1=self.in_ch+2, c2=self.out_ch, k=[3,3]) #c1 划分块数+fs+bw
        conv1 = Conv(c1=self.in_ch+1, c2=self.out_ch, k=[3,3]) #c1 划分块数+fs+bw
        #conv1 = ConvPreBN(c1=self.in_ch+2, c2=self.out_ch, k=[3,3]) #c1 划分块数+fs+bw
        block_index = 0
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv1)

        #AttenBlock = SE(self.out_ch, 4)
        #AttenBlock = AxialImageTransformer(dim = self.out_ch, depth = 3,  reversible = True)
        AttenBlock = CBAM(self.out_ch)
        #AttenBlock = DAM(self.out_ch) #需要大内存
        block_index = 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=AttenBlock)

        for i in range(1):
            block = C2f(c1=16,c2=16,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        conv2 = Conv(c1=16, c2=32, k=[3,3], s=[2,2]) #1/2
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv2)

        for i in range(1):
            block = C2f(c1=32,c2=32,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block)           

        conv3 = Conv(c1=32, c2=64, k=[3,3], s=[2,2]) #1/4
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv3)           

        for i in range(1):
            block = C2f(c1=64,c2=64,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block)   

        conv4 = Conv(c1=64, c2=128, k=[3,3], s=2) #1/8
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv4) 

        for i in range(3):
            block = C2f(c1=128,c2=128,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        conv5 = Conv(c1=128, c2=256, k=[3,3], s=2) #1/16x
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv5) 

        for i in range(3):
            block = C2f(c1=256,c2=256,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        conv6 = Conv(c1=256, c2=512, k=[3,3], s=2) #1/32
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv6) 

        for i in range(1):
            block = C2f(c1=512,c2=512,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        self.last_channel = embedding_size #1280
        self.conv_last = Conv(512, self.last_channel, k=1, s=1)
        self.pool_last = nn.AdaptiveAvgPool2d(1)  # to x(b,c_,1,1)

        self.fc         = nn.Linear(512, embedding_size)
        self.features   = nn.BatchNorm1d(self.last_channel, eps=1e-05)

    def normalize_complex_tensor(self, tensor):
        # 获取复数张量的模
        nRow = tensor.shape[0]
        for i in range(nRow): #复数归一化
            magnitudes = torch.abs(tensor[i,:])
            # 计算模的最大值
            max_magnitude = torch.max(magnitudes)
            # 避免除以零
            if max_magnitude>0:
                tensor[i,:] =  tensor[i,:] / max_magnitude
        return tensor

    def preProcess(self, rx_signals):
        # 1. 计算stft
        N_fft = self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 115, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]

        # 2 在时间维度，数据分为3块，分别设置不同权重
        chunks = torch.chunk(z, 3, dim=-1)

        #信号分成3段，分别赋予不同权重
        # 为每块赋予权重
        weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 重新组合
        z = torch.cat(weighted_chunks, dim=-1)
        nbatch, _ , nCarriers, nTimesym = z.shape
        n_subcount = math.floor(N_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym))
        return z

        
    def forward(self, x):
        #z = self.preProcess(x)
        # 特征提取网络
        z = self.blocks(x)    # [batch 512 fft_size/64 timeframe/64]
        z = self.conv_last(z) # [batch 640 fft_size/64 timeframe/64]
        z = self.pool_last(z) # to x(b,c_,1,1) 为 [batch 640]
        z = z.view(z.size()[0], -1) # [batch 640]
        z = self.features(z)
        return z