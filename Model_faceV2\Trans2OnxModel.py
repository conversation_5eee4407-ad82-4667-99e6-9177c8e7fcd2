# =======================================================================================================================
#   Function    ：Trans2OnxModel.py
#   Description : 转换pth模型到onnx格式
#                 并转换onnx到trt格式
# 
#   Parameter   : ../data/predict/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2025-02-18
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from nets.arcface import Arcface
from usrlib.usrlib import *
from usrlib.dataloader import *
import sys
print(sys.argv)
import getopt
import tensorrt as trt
from onnx.shape_inference import infer_shapes
from onnx import load_model, save_model
from utils.dataloader import getLenNorm

if __name__ == "__main__":
    #print(trt.__version__)
    modeltype = 0 #模型类别 0:分类 1:角向量
    input_shape     = getLenNorm() 
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    dataset_file = "" # 数据文件路径
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    #1. 路径及模型参数
    model_path = "logs/Mtype0-ep096-loss0.085-val_loss0.215.pth"
    output_onnx = './model_data/Mtype0-output0213.onnx'

    batch_size = 64 #256  

    #2. 读取数据文件
    t0 = time.time()
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))

    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=0)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    
    dummy_input     = torch.randn(1, input_shape, 2).cuda()
    torch_out = torch.onnx.export(Model, dummy_input, output_onnx, export_params=True, verbose=False)
    #torch_out = torch.onnx.export(Model, dummy_input, output_onnx, export_params=True, verbose=False, keep_initializers_as_inputs=True,operator_export_type=torch.onnx.OperatorExportTypes.ONNX_ATEN_FALLBACK) # input_names=input_names, output_names=output_names
    #torch.onnx.errors.UnsupportedOperatorError: Exporting the operator 'aten::complex' to ONNX opset version 17
    model=torch.onnx.load(output_onnx)
    model=infer_shapes.infer_shapes(model)

    t3 = time.time()
    print("model trans to onnx：{:.2f}s".format(t3 - t2))
    #简化onnx模型
    #onnxsim.exe .\model_data\Mtype0-output0213.onnx fp16 .\model_data\model_sim.onnx
    #trtexec --onnx=.\model_data\model_sim.onnx --saveEngine=.\model_data\model_sim_pytorch.trt