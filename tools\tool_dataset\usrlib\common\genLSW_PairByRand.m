function []=genLSW_PairByRand(testfile_list)
%    function   : genLSW_PairByRand
%    Description: 随机生成标记数据对 LSW(Labeled Signal Wild)
%                 调用样例：genLSW_PairByRand('E:\lz_signaldB\datafiles\PreprocessData\2025-04-29\base\Test_ds_gen.txt');
%                 输出：.\output\lsw_pair.txt，每类 生成2条相同类别对比；两条不同类别对比
%
%    author     : 刘智国
%    Date       : 2025-05-06
lines = readlines(testfile_list);
InitMyParams;
fname_out = fullfile(myconfig.folder_outputDS, 'base','lsw_pair.txt');
[filepath, ~, ~] = fileparts(fname_out);
if exist(fname_out,"file")>0
    delete(fname_out);
elseif exist(filepath,"dir")<=0
    warning("路径:%s 不存在，请创建", filepath)
    return;
end

nLinesCount = length(lines);
prevClsID = "startL";
nClsLocation = zeros(100,3);
nClsIndex = 0;
nStartPos = 1;
% 1 寻找每一类的起始结束行位置
for idRow = 1:nLinesCount
    strRow = lines(idRow);
    strList = split(strRow,";");
    newClsID = strList(1);
    if prevClsID=="" %末尾空行
        break;
    end
    if strcmp(prevClsID,newClsID)==false %不同行
        if idRow==1
            nStartPos = idRow;
        else %每一类：记录行开始及结束

            nClsIndex = nClsIndex + 1;
            nClsLocation(nClsIndex,1) = str2double(prevClsID);
            nClsLocation(nClsIndex,2) = nStartPos;
            nClsLocation(nClsIndex,3) = idRow-1;
            nStartPos = idRow;
        end
        prevClsID = newClsID;
    end
end
% 2 选择相同行及不同行
for idCls = 1: nClsIndex
    nStartPos = nClsLocation(idCls,2);
    nEndPos = nClsLocation(idCls,3);
    % 2.1  相同类别行
    idRows = randi([nStartPos, nEndPos], 4,1);
    strline1 = strcat(lines(idRows(1)),";",lines(idRows(2)));   % 相同类别行
    writelines(strline1, fname_out,'WriteMode','append');
    strline1 = strcat(lines(idRows(3)),";",lines(idRows(4)));   % 相同类别行
    writelines(strline1, fname_out,'WriteMode','append');
    % 2.2 不同类别行
    idRow1 = randi([nStartPos, nEndPos],2,1);%不同类别行
    if nStartPos==1 %头部
        idRowN = randi([nEndPos+1, nLinesCount-1],2,1);%选择不同类别行
        strline2 = strcat(lines(idRow1(1)),";",lines(idRowN(1)));   % 不同类别行
        writelines(strline2, fname_out,'WriteMode','append');
        strline2 = strcat(lines(idRow1(2)),";",lines(idRowN(2)));   % 不同类别行
        writelines(strline2, fname_out,'WriteMode','append');        
    elseif idCls==nClsIndex %末尾
        idRowN = randi([1, nStartPos-1],2,1);%选择不同类别行
        strline2 = strcat(lines(idRow1(1)),";",lines(idRowN(1)));   % 不同类别行
        writelines(strline2, fname_out,'WriteMode','append');
        strline2 = strcat(lines(idRow1(2)),";",lines(idRowN(2)));   % 不同类别行
        writelines(strline2, fname_out,'WriteMode','append');                
    else %其它
        idRowN1 = randi([1, nStartPos-1],1,1);%选择不同类别行
        strline2 = strcat(lines(idRow1(1)),";",lines(idRowN1));   % 不同类别行
        writelines(strline2, fname_out,'WriteMode','append');

        idRowN2 = randi([nEndPos+1, nLinesCount-1],1,1);%选择不同类别行
        strline2 = strcat(lines(idRow1(2)),";",lines(idRowN2));   % 不同类别行
        writelines(strline2, fname_out,'WriteMode','append');
    end
end
fprintf("成功生成:%s\n",fname_out);
end