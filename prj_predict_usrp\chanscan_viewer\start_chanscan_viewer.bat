@echo off
chcp 65001
echo =========================================
echo    信道扫描分段查看工具
echo =========================================
echo.
echo 正在启动程序...
echo.

REM 配置conda环境名（如需修改请编辑下一行）
set CONDA_ENV=py39

echo 正在激活conda环境: %CONDA_ENV%
call conda activate %CONDA_ENV%

if %errorlevel% neq 0 (
    echo.
    echo 警告: conda环境激活失败，使用系统Python
    echo 请确认conda环境名是否正确: %CONDA_ENV%
    echo.
) else (
    echo conda环境激活成功
    echo.
)

echo 正在检查依赖模块...
python -c "import tkinter, numpy, matplotlib, scipy; print('基础依赖检查通过')" 2>nul
if %errorlevel% neq 0 (
    echo.
    echo 警告: 某些基础依赖可能缺失
    echo 请确保已安装: numpy, matplotlib, scipy, tkinter
    echo.
)

echo 启动信道扫描分段查看工具...
python chanscan_viewer.py

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出现错误！
    echo 请检查以下问题：
    echo 1. Python 环境是否正确安装
    echo 2. 必需的库是否已安装：numpy, scipy, matplotlib, tkinter
    echo 3. 项目模块路径是否正确（chanlib, usrlib等）
    echo 4. chanscan_viewer.py 文件是否存在
    echo.
    echo 如需查看详细错误信息，请直接运行:
    echo python chanscan_viewer.py
    echo.
    pause
) else (
    echo.
    echo 程序已正常退出。
)