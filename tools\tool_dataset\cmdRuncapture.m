%
%    function    : cmdRuncapture.m
%    description : 命令行文件（开启捕获采集app）
%
%  Author      : Liuzhiguo
%  Date        : 2024-12-06
%

clear;
close all;

%可放在命令行中执行
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量

capture;%采集程序
%
% 显示生成的信号
% DispDatasetRecByChart(".\outdataset\test\sam\fchanscan-S1.hdf5",1,"原始")
%
% [nSigChecked] = func_scansignal('.\sam_evo\433\ch9364Capture_433_7.dat',0,2,256e3);
% [nSigChecked] = func_scansignal('ch9364Capture.dat',0);
%
% [nSigChecked] = func_scansignal('.\sam_evo\433\ch9364Capture433-5.dat',0,2,256e3,1000);
% [nSigChecked] = func_scansignal('.\sam_evo\FUTABA\ch9364Capture_FUT_1.dat',19, 1.3,200e3,1700);
% [nSigChecked] = func_scansignal('.\sam_evo\RFD900X\ch9364Capture_RFD_3.dat',6,3.34,150e3);
% [nSigChecked] = func_scansignal('.\sam_evo\HITEC\ch9364Capture_HITEC_1.dat',23,1.7,200e3);
%[nSigChecked] = func_scansignal('.\sam_evo\RFD900X\ch9364Capture_RFD_1.dat',6,3.34,150e3,-2000);
%[nSigChecked] = func_scansignal('.\sam_evo\flysky_pl18new_rc\10m\40.bvsp',25,0.5,15e5);
% 文件格式转换
% Transhdfv2dat("E:\project\prj_predict_usrp\saved_hdfs\20250512-145746-677.hdfv")
% showSingleWave("E:\project\prj_predict_usrp\saved_hdfs\00-20250515-102619-202.hdfv")
% [nSigChecked] = func_scansignal('E:\project\prj_predict_usrp\saved_hdfs\00-20250515-102619-202.hdfv',0,6,256e3,0,true);
% [nSigChecked] = func_scansignal('E:\lz_signaldB\datafiles\WMisclassifiedData\FalsePositive\2025_05_19\02-20250518-192248-226.hdfv',0,2,256e3,0,true);