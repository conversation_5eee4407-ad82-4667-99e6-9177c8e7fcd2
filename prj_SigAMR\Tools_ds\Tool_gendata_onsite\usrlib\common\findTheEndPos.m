%求最终的结束位置
function [end_position] = findTheEndPos(wave_data, clip_start_pos, clip_end_pos)
%% 参数设置
threshold_ratio = 0.5;    % 阈值比例 (RMS的10%)


%% 计算幅度阈值
wav_clip = wave_data(clip_start_pos:clip_end_pos);
rms_value = sqrt(mean(abs(wav_clip).^2));
threshold = rms_value * threshold_ratio;

%% 滑动窗口检测
window_size = 100; %round(window_length * fs);  % 窗口采样点数
clip_end_pos_new = clip_end_pos - floor((clip_end_pos-clip_start_pos)/10);
num_windows = floor(length(wave_data(clip_end_pos_new:end)) / window_size)-2;%留有余量
sigwins_count = 0;
end_position = -1;

for i = 1:num_windows
    start_idx = clip_end_pos_new+(i-1) * window_size + 1;
    end_idx = clip_end_pos_new+i * window_size;
    window = wave_data(start_idx:end_idx);
    
    % 计算窗口RMS
    window_rms = sqrt(mean(abs(window).^2));
    
    % 判断是否为信号窗口
    if window_rms > threshold
        sigwins_count = sigwins_count + 1;
        
    else
        end_position = end_idx;%已发现下降沿
        break;
    end
end

if i==1 %刚刚开始就退出了，应该前向寻找尾部上升沿
    end_position = findTheEndPosForward(wave_data, clip_start_pos, clip_end_pos);
end


%% 输出结果
if end_position <0 %找到结束位置也未能发现尾部下降沿
    end_position = 0;% clip_end_pos;
end
end


%寻找尾部前面的上升沿
function [end_position] = findTheEndPosForward(wave_data, clip_start_pos, clip_end_pos)
%% 参数设置
threshold_ratio = 0.45;    % 阈值比例 (RMS的10%)


%% 计算幅度阈值
wav_clip = wave_data(clip_start_pos:clip_end_pos);
rms_value = sqrt(mean(abs(wav_clip).^2));
threshold = rms_value * threshold_ratio;

%% 滑动窗口检测, 前向位置
window_size = 100; %round(window_length * fs);  % 窗口采样点数
halfLength = floor((clip_end_pos-clip_start_pos)/2);
num_windows = floor(length(wave_data(clip_end_pos-halfLength:clip_end_pos)) / window_size);%留有余量
sigwins_count = 0;
end_position = -1;

for i = 1:num_windows
    start_idx = clip_end_pos-i * window_size+1;%向前滑动
    end_idx = clip_end_pos-(i-1) * window_size;
    window = wave_data(start_idx:end_idx);
    
    % 计算窗口RMS
    window_rms = sqrt(mean(abs(window).^2));
    
    % 判断是否为信号窗口
    if window_rms > threshold %已发现尾部上升沿
        end_position = start_idx;%已发现上升沿
        break;        
    else
        sigwins_count = sigwins_count + 1;
    end
end

%% 输出结果
if end_position <0 %搜索了一半位置，还未发现上升沿
    end_position = clip_end_pos;
end

end