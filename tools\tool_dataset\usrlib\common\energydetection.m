function [Sig] = energydetection(x,fs,fc)
%  Function    ：energydetection
%  Description : 识别信号及中心频率信息
%  Parameter   : x       -- 信号
%                fs      -- 
%                fc      -- 
%  Return      : 解析频谱信息
%
%  Author      : zhouxiong
%  Date        : 2024-08-24

% packet = load_sigcap(data_filepath);
% wb_fc = packet.header.center_freq;
% wb_fs = packet.header.samp_rate;
% a_channel = packet.data;


a_channel = x;
wb_fc = fc;
wb_fs = fs;

window = 4096;
noverlap = 2048;
nfft = 4096;
[S,f,t,p]=spectrogram(a_channel,window,noverlap,nfft,wb_fs,'yaxis');   %STFT
p = circshift(p,nfft/2);
f = (0:1:nfft-1)*wb_fs/1e6/nfft+wb_fc/1e6-wb_fs/1e6/2;
% f = (-2048:1:2047)/wb_fs*1e6+wb_fc/1e6;
S = abs(S).^2;            % 计算功率
S = circshift(S,nfft/2);    % S = fftshift(S,1);
% figure;
% surf(t*1000,f,10*log10(S),'edgecolor','none'); axis tight;

%%%% 沿时间方向合并五个点  0.5ms*61.44/2048
mergefactor = 5;
numMergedPoints = floor(length(t) / mergefactor);
Sumpow = zeros(size(S,1),numMergedPoints);
for i = 1:numMergedPoints
    startIdx = (i-1) * mergefactor + 1;
    endIdx = i * mergefactor;
    Sumpow(:, i) = sum(S(:, startIdx:endIdx), 2);
end

t_merged = t(mergefactor/2:mergefactor:mergefactor/2+(numMergedPoints-1)*mergefactor);
figure;
surf(t_merged*1000,f,10*log10(Sumpow),'edgecolor','none'); axis tight;
view(0,90);

S = Sumpow;
t = t_merged;


% 中值滤波
smooth_factor = 20;

% S_med = medfilt1(S,smooth_factor,[],1,'omitnan','truncate');
% figure;
% S = S_med;
% surf(t*1000,f,10*log10(S_med),'edgecolor','none');
% view(0,90);

% 滑动平均滤波
S_mean = movmean(S,smooth_factor,1);
S = S_mean;
figure;
surf(t*1000,f,10*log10(S_mean),'edgecolor','none');
view(0,90);


% 计算平均功率
mean_power = mean(S, 1);      % 列平均
meanp = mean(mean_power);     % 平均功率
threshold = meanp;            % 门限值

fprintf('平均功率：%f\n',threshold );

% threshold = 3e7;
% threshold = max(threshold,5e7);

%k = 1.6;
k = 3.2;
twovalue = S > threshold*k; 
figure;
surf(t*1000,f,double(twovalue),'edgecolor','none');
view(0,90);

% 形态学滤波
% se = strel("diamond",1);
se = strel("rectangle",[8 1]);
twovalue = imopen(twovalue,se);
% se = strel("square",2);
% twovalue = imclose(twovalue,se);
% twovalue = bwmorph(twovalue,'open');    %窄带信号先开后闭
% twovalue = bwmorph(twovalue,'close');
% twovalue = bwmorph(twovalue,'close');   %宽带信号先闭后开
% twovalue = bwmorph(twovalue,'open');
figure;
surf(t*1000,f,double(twovalue),'edgecolor','none');
view(0,90);
cc = bwconncomp(twovalue,8);
stats = regionprops(cc, 'BoundingBox', 'Centroid','Area');
% 提取面积
areas = [stats.Area];
% 获取按面积大小排序的索引
[~, sortedIndices] = sort(areas, 'descend');
% 按面积大小对连通区域进行排序
sortedStats = stats(sortedIndices);

Sig = struct('begin',{},'end',{},'freq_point',{},'bandwidth',{},'beginPoint',{},'endPoint',{});
idx = 1;

% 读取排序后连通区域的数据
% for i = 1:cc.NumObjects
for i = 1:length(sortedStats)
    bbox = sortedStats(i).BoundingBox;
    top = ceil(bbox(2));
    bottom = ceil(bbox(2) + bbox(4) - 1);
    left = ceil(bbox(1));
    right = ceil(bbox(1) + bbox(3) - 1);
    cen = sortedStats(i).Centroid;
    if bottom > top && right > left
    % if f(bottom) - f(top) > 0.1
        flag = 0;   %% 判断该区域是否属于信号列表里的区域
        integer_part = floor(cen(2));      % 整数部分
        decimal_part = cen(2) - integer_part; % 小数部分
        freq_point = f(integer_part) + decimal_part * wb_fs/1e6/nfft;   % 质心
        % freq_point = (f(top)+f(bottom))/2;  % 取中心
        % bandwidth = f(bottom)-f(top); 
        bandwidth = min(freq_point-f(top),f(bottom)-freq_point) *2;
        if idx > 1 
            for j = 1:idx-1
                if t(left)*1e3 >= Sig(j).begin && t(right)*1e3 <= Sig(j).end ...
                        && f(top) >= Sig(j).freq_point - Sig(j).bandwidth/2  ...
                        && f(bottom) <=  Sig(j).freq_point + Sig(j).bandwidth/2
                    flag = 1;
                    if flag == 1
                        break;
                    end
                end
                if  abs(t(left)*1e3 - Sig(j).begin) <= (t(2)-t(1))*1e3 && abs(t(right)*1e3 -Sig(j).end) <= (t(2)-t(1))*1e3...
                        && min(bandwidth,Sig(j).bandwidth) >= max(bandwidth,Sig(j).bandwidth) * 0.8
                   if abs(freq_point - Sig(j).freq_point) <= max(bandwidth,Sig(j).bandwidth)*1.5
                       %先把原来的加进去
                       % if bandwidth > 0.1
                           Sig(idx).begin = t(left)*1e3;
                           Sig(idx).end = t(right)*1e3;
                           Sig(idx).beginPoint = t(left) * wb_fs;
                           Sig(idx).endPoint = t(right) * wb_fs;
                           Sig(idx).freq_point = freq_point;   % 质心
                           Sig(idx).bandwidth = bandwidth;  
                           idx = idx + 1;
    
                           %在加合并后的
                           bandwidth = (bandwidth + Sig(j).bandwidth)/2 + abs(freq_point - Sig(j).freq_point);
                           freq_point = (freq_point + Sig(j).freq_point)/2;
                       % end
                   end
                end
            end
        end
        if flag == 0
            % if bandwidth > 0.1
                Sig(idx).begin = t(left)*1e3;
                Sig(idx).end = t(right)*1e3;
                Sig(idx).beginPoint = t(left) * wb_fs;
                Sig(idx).endPoint = t(right) * wb_fs;
                Sig(idx).freq_point = freq_point;   % 质心
                Sig(idx).bandwidth = bandwidth;  
                idx = idx + 1;
            % end
        end
    end
end

if ~isempty(Sig)
    begins = [Sig.begin];
    ends = [Sig.end];
    freqs = [Sig.freq_point];
    [~, sortIdx] = sortrows([begins',-ends',freqs'], [1 2 3]);
    Sig = Sig (sortIdx);
end

end

 