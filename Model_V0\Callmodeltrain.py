# =======================================================================================================================
#   Function    ：callmodelTrain.py
#   Description : 调用训练代码
#                 传入hdf5文件路径，并对modelTrain.py调用
# 
#   Parameter   : ../data/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2024-08-23
# =======================================================================================================================
import os
#子路径：base lteCH multipath noised
datasettype = 'train'
subdir = 'noised' # base,noised,multipath
dst_path = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)
# subdir = 'noised'
# dst_path = 'E:\\project\\prj_sigClassify\\Data\\v1\\{}'.format(subdir)

ext_name = 'hdf5'
for file in os.listdir( dst_path ):
    if file.endswith( ext_name ):
        print( file )
        dataset_file = os.path.join(dst_path, file)
        #os.system('python ClsModelTrain.py -p {}'.format(dataset_file)) #python 训练文件调用
        os.system('python ModelTrain.py -p {0} -m {1}'.format(dataset_file, 0)) #python 训练文件调用 模型类别 0:分类 1:角向量


