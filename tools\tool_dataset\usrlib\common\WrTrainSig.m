function [] = WrTrainSig(fname_wr, rxSig_clip, class_id, class_name,  fc, fs, bw,  startbatch)
%  Function    ：WrTrainSig
%  Description : 生成写入训练数据
%  Parameter   : fname_wr       -- 写入文件名称
%                rxSig_clip     -- 信号 (务必保证为列向量)
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13

%创建结构
%N_MaxPoints = 61.44e6*20e-3;
N_1msPoints = fs*1e-3;
nMaxSize = [2 Inf Inf]; %[I/Q N-points Batch]
if exist(fname_wr,"file")>0
    if startbatch==1%第1条数据
        %delete(fname_wr)
		fprintf("已有训练数据文件：%s\n",fname_wr);
    end
else
    h5create(fname_wr,"/rx_signal",nMaxSize,'Datatype','single','ChunkSize',[2 N_1msPoints 1]);%,'Deflate',9
    h5create(fname_wr,"/class_id",[1 Inf],'Datatype','int32','ChunkSize',[1 1]);
    h5create(fname_wr,"/fc",[1 Inf],'Datatype','single','ChunkSize',[1 1]);
    h5create(fname_wr,"/fs",[1 Inf],'Datatype','single','ChunkSize',[1 1]);
    h5create(fname_wr,"/bw",[1 Inf],'Datatype','single','ChunkSize',[1 1]);
    h5create(fname_wr,"/class_name",[1 Inf],'Datatype','string','ChunkSize',[1 1]);
end

%写入数据
N_points = length(rxSig_clip);
nCurSize = [2 N_points 1];
cc=size(rxSig_clip);
if cc(1)>cc(2) %列向量,转置为行向量
    rxSig_clip=rxSig_clip.';
end
wr_sig = reshape([real(rxSig_clip);imag(rxSig_clip)],nCurSize);
h5write(fname_wr,"/rx_signal",single(wr_sig),[1 1 startbatch],nCurSize);
%class_id = typecast(class_id,'int32');
class_id_B = reshape(class_id,[1 1]);
h5write(fname_wr,"/class_id",int32(class_id_B), [1 startbatch], [1 1]);
h5write(fname_wr,"/fc",single(fc), [1 startbatch], [1 1]);
h5write(fname_wr,"/fs",single(fs), [1 startbatch], [1 1]);
h5write(fname_wr,"/bw",single(bw), [1 startbatch], [1 1]);

class_name_B = reshape([string(class_name)], [1 1]);
%class_name_B = class_name;
h5write(fname_wr,"/class_name",class_name_B, [1 startbatch], [1 1]);
%h5disp(fname_wr);
fprintf("写入训练数据文件：%s\n",fname_wr);