function cropped_signal = random_crop1(signal, crop_length)
% 随机截取固定长度的信号片段（不考虑信息完整性）
% 输入：
%   signal: 原始信号（向量，长度可能为nn、22565等）
%   crop_length: 截取长度（如768）
% 输出：
%   cropped_signal: 截取的768点片段

    signal_length = length(signal);
    
    if signal_length <= crop_length
        % 如果信号长度 <= 目标长度，直接返回原信号（或补零）
        cropped_signal = signal(:); % 转为列向量
        if signal_length < crop_length
            cropped_signal = [cropped_signal; zeros(crop_length - signal_length, 1)]; % 补零
        end
    else
        % 随机选择起始点
        start_idx = randi(signal_length - crop_length + 1);
        % 截取片段
        cropped_signal = signal(start_idx : start_idx + crop_length - 1);
    end
end