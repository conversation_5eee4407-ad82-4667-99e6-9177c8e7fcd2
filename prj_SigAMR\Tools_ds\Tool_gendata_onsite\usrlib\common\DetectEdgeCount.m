function [rising_edges_count, falling_edges_count] = DetectEdgeCount(y)
%
%  Function    ：DetectEdgeCount
%  Description : 检测上升，下级沿的个数
%  Parameter   : y       -- 输入信号
%                
%  Return      : rising_edges_count    -- 上升沿个数
%                falling_edges_count   -- 下降沿个数
%
%  Author      : Liuzhiguo
%  Date        : 2025-06-16

window_size = 2000;  % 10ms的窗口大小
threshold = 0.08 * max(abs(y));   % 阈值设为信号最大值的10%

% 计算信号的差分（近似导数）
diff_signal = diff(y);

% 平滑差分信号以减少噪声影响
diff_smooth = movmean(abs(diff_signal), window_size);

% 检测上升沿和下降沿
rising_edges = find(diff_smooth > threshold & [0; diff_smooth(1:end-1)] <= threshold);
falling_edges = find(diff_smooth > threshold & [diff_smooth(2:end); 0] <= threshold);

rising_edges_count = 0;
falling_edges_count = 0;
% 结果输出
if ~isempty(rising_edges)
    fprintf('检测到 %d 个上升沿\n', length(rising_edges));
    rising_edges_count = length(rising_edges);
else
    fprintf('未检测到上升沿\n');
end

if ~isempty(falling_edges)
    fprintf('检测到 %d 个下降沿\n', length(falling_edges));
    falling_edges_count = length(falling_edges);
else
    fprintf('未检测到下降沿\n');
end


end

