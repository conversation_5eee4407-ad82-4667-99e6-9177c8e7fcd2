function [] = DispDatasetRecByChart(fname_dataset,rowid,sNoiseType)
%  Function    ：DispDatasetRecByChart
%  Description : 以图表方式显示训练集记录
%  Parameter   : fname_dataset    -- 数据集文件名称
%                rowid            -- 行id （1开始）
%                sNoiseType       -- 噪声类型
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-21
%DISPDATASETRECBYCHART 此处显示有关此函数的摘要
%   此处显示详细说明
[rd_sig,class_id,class_name, arrayfc, arrayfs, arraybw] = RdTrainSig(fname_dataset);
fs = arrayfs(rowid);
fc = arrayfc(rowid);
bw = arraybw(rowid);
signal_in = rd_sig(1,:,rowid)+1j*rd_sig(2,:,rowid);

len_clip = length(signal_in);

fs = double(fs);fc = double(fc);bw = double(bw);
% t = (0:1:len_clip-1)*(1/fs);
% signal_in = signal_in.*exp(-j*2*pi*fc*t);
sfilename = class_name(rowid);
sfilename = replace(sfilename,'\','\\');
sfilename = replace(sfilename,'_','\_');
%fname_dataset = replace(fname_dataset,'\',' ');
stitle = sprintf("%s文件:%s \n类别:%d 截取信号: 数据点数为%d，持续时间为%.2f ms\r\n",sNoiseType,sfilename,class_id(rowid),len_clip,len_clip*1e3/fs);
ShowTimeAndSpectral(signal_in,  fc, fs, stitle,1024);
end

