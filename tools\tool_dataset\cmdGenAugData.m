%    function    : cmdGenAugData.m
%    description : 生成增强数据集文件
%
%  Author      : Liuzhiguo
%  Date        : 2025-03-19

clear;
close all;

%可放在命令行中执行
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量

clip_ms = 6;%好像与3ms差距不大
% 1 只对CJ0_1M的信号做数据增强, 保存为分散文件数据集（没有路径参数outname的情况下，为分散文件形式）
% 1.1 根据列表文件生成基本集（可以从原始列表文件中选取80条，生成Train_records-CJ0.txt）
WriteHdfsbase_nb("E:\ftproot\sampleData\Train_records-CJ0.txt",".\outds\train-files\test\",clip_ms);%分散文件格式
% 1.2 噪声增强
%依赖于基本集文件夹, 生成，注意：输入为基本集列表
gennoisedDS_bylist("E:\project\tool_dataset\outds\train-files\test\Train_records-CJ0_list.txt",".\outds\train-files\test_noised\");
% 1.3 数据dp，dw，df增强
WriteHdfsAugment_nb("E:\ftproot\sampleData\Train_records-CJ0.txt",".\outds\train-files\test_augment\",clip_ms);
