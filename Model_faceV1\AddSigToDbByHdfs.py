# =======================================================================================================================
#   Function    ：AddSigToDbByHdfs.py
#   Description : 添加信号到信号库中，config/sig-vectordB.cfg
#                 可在命令行中输入: python AddSigToDbByHdfs.py
#                                 或者 python AddSigToDbByHdfs.py -p E:\project\tool_dataset\outdataset\train\base\nb-dataset-val-S1.hdf5 
#
#   Parameter   : -p 后面为文件路径 hdf格式
#   Author      : Liuzhiguo
#   Date        : 2024-1-20
# =======================================================================================================================
from usrlib.usrlib import *
import torch 
from nets.arcface import Arcface
from usrlib.dataloader import *
import sys
print(sys.argv)
import getopt

if __name__ == "__main__":
    #文件方式读入，加入
    clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件
    modeltype = 1 #模型类别 0:分类 1:角向量
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('total classes count:',cls_count)

    train_dataset_dir = dataset_dir[0] #'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)
    dataset_nb = "nb-dataset-val-S1.hdf5"
    dataset_file = os.path.join(train_dataset_dir, dataset_nb)
    
    # 命令行方式获取测试文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['modelname=','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modelname'):
            fname_model = opt_value
            print("The modelname is ", fname_model)
        elif opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something

    rx_signal,class_id,class_name, fs_value, bw_value   = LoadHdfsDataset(dataset_file)

    N_rec = class_id.shape[0]

    #每类只取第一个记录
    rx_signal_sel = []
    class_id_sel = []
    class_name_sel = []
    fs_value_sel = []
    bw_value_sel = []

    for aid in  cls_ids:
        for i in range(N_rec):
            if int(aid)==class_id[i]:
                rx_signal_sel.append(np.expand_dims(rx_signal[i,:,:],axis=0))
                class_id_sel.append(np.expand_dims(class_id[i,:],axis=0))
                class_name_sel.append(np.expand_dims(class_name[i,:],axis=0))
                fs_value_sel.append(np.expand_dims(fs_value[i,:],axis=0))
                bw_value_sel.append(np.expand_dims(bw_value[i,:],axis=0))
                break

    rx_signal_sel = np.concatenate(rx_signal_sel, axis=0)
    class_id_sel = np.concatenate(class_id_sel, axis=0)
    class_name_sel = np.concatenate(class_name_sel, axis=0)
    fs_value_sel = np.concatenate(fs_value_sel, axis=0)
    bw_value_sel = np.concatenate(bw_value_sel, axis=0)

    N_rec = class_id_sel.shape[0]

    #1. 路径及模型参数
    #model_path = "logs/Mtype1-ep095-loss0.000-val_loss2.050.pth" #192
    #model_path = "logs/Mtype1-ep081-loss0.007-val_loss1.631.pth" #96 acc= 47.50%
    #model_path = "logs/Mtype1-ep046-loss0.061-val_loss1.434.pth" #96 acc= 47.50%
    #model_path = "logs/Mtype1-ep098-loss0.000-val_loss2.219.pth"  #96 acc= 34.17%
    #model_path = "logs/Mtype1-ep095-loss0.000-val_loss1.817.pth"  #64 acc= 55%
    #model_path = "logs/Mtype1-ep063-loss0.016-val_loss1.441.pth"  #64 acc= 44.17%
    model_path = "logs/Mtype1-ep100-loss0.000-val_loss1.867.pth"  #64 acc= 55%

    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()

    ViewArcVectorDB() #查看向量库

    for i in range(N_rec):
        sigdata = rx_signal_sel[i]
        file_subpath = class_name_sel[i][0].decode('utf-8')
        clsname = file_subpath.split('\\')[0]

        print('要解析信号的结构：{0} {1}'.format(file_subpath, sigdata.shape))
        sigdata = torch.from_numpy(sigdata)
        sigdata = sigdata.cuda()
        fs_value = torch.from_numpy(fs_value_sel[i]).cuda()
        bw_value = torch.from_numpy(bw_value_sel[i]).cuda()
        
        if len(sigdata.shape)==2:
            sigdata = sigdata.unsqueeze(0)
        #4.生成向量,加库
        vec1 = Model(sigdata)
        vec1 = vec1.squeeze()
        #file_subpath = str(class_name_sel[i][0])


        AddArcVector(vec1.detach().cpu().numpy(), class_id_sel[i], clsname, file_subpath, fs_value_sel[i], bw_value_sel[i])

    ViewArcVectorDB() 