# =======================================================================================================================
#   Function    ：func_scansig.py
#   Description : 扫描信号及信道，并保存为文件
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import numpy as np
from scipy.signal import stft, windows, get_window
import matplotlib.pyplot as plt
from scipy.signal import firwin, lfilter
from mpl_toolkits.mplot3d import Axes3D
from skimage.morphology import dilation, rectangle
import matplotlib
from chanlib.calregions import calunionregion, extract_and_save_signals
import torch
import os

def plot_signal(bshowdgraph, wb_signal):
    if bshowdgraph:
        #plt.ioff() # 禁用交互模式
        plt.figure(111)
        eps = 1e-5
        # plt.plot(10*np.log10(np.abs(np.real(wb_signal))+eps))
        # plt.plot(10*np.log10(np.abs(np.imag(wb_signal))+eps))
        plt.plot(np.real(wb_signal))
        plt.plot(np.imag(wb_signal))
        
        plt.title('采集信号')
        plt.grid(False)
        plt.show()

def plot_power_spectrum(bshowdgraph, t, f, psd_value_out, sc, psd_filtered_cliped):
    if bshowdgraph:
        # 创建一个新的图形窗口
        fig = plt.figure(112, figsize=(12, 8))

        # 创建第一个子图
        ax1 = fig.add_subplot(211, projection='3d')
        # 提取数据
        psd_part = psd_value_out[sc: -sc, :]
        T, F = np.meshgrid(t, f)
        # 绘制 3D 曲面图，使用鲜明的色彩映射
        surf1 = ax1.plot_surface(T, F, psd_part, cmap='viridis', edgecolor='none')
        # 设置坐标轴范围
        ax1.axis('tight')
        # 设置视角
        ax1.view_init(elev=90, azim=0)
        # 设置标题
        ax1.set_title('功率谱密度图 3D')
        # 添加颜色条
        fig.colorbar(surf1, ax=ax1, shrink=0.5, aspect=10)

        # 创建第二个子图
        ax2 = fig.add_subplot(212, projection='3d')
        # 绘制滤波后的 3D 曲面图，使用鲜明的色彩映射
        surf2 = ax2.plot_surface(T, F, psd_filtered_cliped, cmap='viridis', edgecolor='none')
        # 设置坐标轴范围
        ax2.axis('tight')
        # 设置视角
        ax2.view_init(elev=90, azim=0)
        # 设置标题
        ax2.set_title('滤波后功率谱密度图 3D')
        # 添加颜色条
        fig.colorbar(surf2, ax=ax2, shrink=0.5, aspect=10)

        # 调整子图之间的间距，扩大显示区域
        #plt.subplots_adjust(hspace=0.1)

        plt.show()

def plot_psd(bshowdgraph, all_psd, t, f, psd_value_out):
    if bshowdgraph:
        # 创建图形窗口 113
        plt.figure(113)

        # 第一个子图
        plt.subplot(4, 1, 1)
        plt.plot(all_psd[0, :])
        plt.title('时间-功率谱密度 mean')

        # 第二个子图
        plt.subplot(4, 1, 2)
        plt.plot(all_psd[1, :])
        plt.title('时间-功率谱密度 max')

        # 第三个子图
        plt.subplot(4, 1, 3)
        plt.plot(all_psd[2, :])
        plt.title('时间-功率谱密度 max-mean')

        # 第四个子图
        plt.subplot(4, 1, 4)
        plt.plot(all_psd[3, :])
        plt.title('时间-功率谱密度 tuned')

        # 创建图形窗口 114
        plt.figure(114)

        # 第一个子图（3D 图）
        ax = plt.subplot(211)
        ax.imshow(psd_value_out, cmap='gray')
        ax.set_title('二值化后 3D功率谱密度图')

        #ax = plt.subplot(211, projection='3d')
        # T, F = np.meshgrid(t, f)
        # ax.plot_surface(T, F, psd_value_out, edgecolor='none')
        # ax.axis('tight')
        # ax.view_init(elev=90, azim=0)
        # ax.set_title('二值化后 3D功率谱密度图')

        # fig = plt.figure()
        # axe = fig.add_subplot(1, 2, 1)
        # axe.imshow(psd_value, cmap='gray')
        # axe.set_title('Original Image')

        plt.show()

def psd(nfft, samples):
    """Return the power spectral density of `samples`"""
    window = np.hamming(nfft)
    result = np.multiply(window, samples)
    result = np.fft.fftshift(np.fft.fft(result, nfft))
    result = np.square(np.abs(result))
    result = np.nan_to_num(10.0 * np.log10(result))
    result = np.abs(result)
    return result


def proc_wbsig(signal, wb_fs, wb_fc, wb_bw):
    fft_len = 2048 #%2048;       %fft长度
    
    # 定义Hamming窗
    window_length = fft_len
    #window = hamming(window_length,)
    
    #bshowdgraph = True
    bshowdgraph = False
    
    # 智能配置matplotlib字体，避免Linux下的字体警告
    import platform
    if platform.system() == 'Linux':
        # Linux系统使用可用的中文字体
        import matplotlib.font_manager as fm
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        font_candidates = [
            'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
            'Source Han Sans SC', 'Droid Sans Fallback', 'Liberation Sans'
        ]
        selected_font = 'DejaVu Sans'  # 默认值
        for font in font_candidates:
            if font in available_fonts:
                selected_font = font
                break
        matplotlib.rc("font", family=selected_font)
    elif platform.system() == 'Windows':
        matplotlib.rc("font", family='Microsoft YaHei')
    else:
        matplotlib.rc("font", family='Liberation Sans')
    #matplotlib.use('Agg') # 使用非交互式后端
    #matplotlib.use('TkAgg')  # 可以根据自己的环境选择合适的交互式后端

    plot_signal(bshowdgraph, signal)
    # 2 进行STFT
    #window_h = np.hamming(fft_len)
    
    fft_overlap = window_length//2
    data = signal

    # window_h = windows.hamming(fft_len, sym=False)
    #window_h = get_window("hamm", fft_len)
    ## z_values = stft_basic(data, window_h)
    #f, t_stft, z_values = stft(data, fs=wb_fs, window=window_h, noverlap=fft_overlap, nfft=window_length,boundary=None, padded=False, nperseg=fft_len, return_onesided=False, scaling='spectrum') #,scaling="psd"   
    #z_values = z_values*np.sqrt(np.sum(window_h**2)) #差一个系数但是不知道如何得到
    #z_values = z_values*np.sum(window_h**2)
    #shifted_stft_result0  = np.roll(z_values, fft_overlap-1, axis=0) #为了和matlab统一
    # col = len(t_stft)
    
    complex_signal = torch.from_numpy(data).to(torch.complex64)
    #w_hamm = torch.hamming_window(fft_len,periodic=True)
    w_blackman = torch.blackman_window(fft_len, periodic=True)
    # 进行短时傅里叶变换 [f, t]
    stft_result = torch.stft(input=complex_signal,window=w_blackman, n_fft=fft_len, hop_length=fft_overlap, win_length=fft_len, normalized=False, center=False, return_complex=True)
    # # 重排频谱结果
    #shifted_stft_result = torch.fft.fftshift(stft_result, dim=0)
    shifted_stft_result  = torch.roll(stft_result, fft_overlap-1, dims=0) #为了和matlab统一
    z_values = shifted_stft_result.numpy() 

    col = z_values.shape[1]
    # [f, t]

    zOut = np.abs(z_values)/np.float32(fft_len)        # 归一化
    psd_value_out = 20*np.log10(zOut)-56.8 # 等效到前端功率值

    # 3 频域滤波，消除毛刺 
    # fir filter
    M, N = psd_value_out.shape
    L_filter = 48
    b = firwin(L_filter+1, 0.05)  # matlab在fir1里，指定的是滤波器的阶数；而在firwin中，需要指定滤波器的抽头数，抽头数等于阶数加 1。所以numtaps设置为48 + 1。
    psd_filtered = np.zeros((M, N))
    for i in range(N):
        psd_filtered[:, i] = lfilter(b, 1, psd_value_out[:, i]) #[f, t]

    if fft_len == 2048:
        sc = 90  # 频域留白，截取中心频率
    elif fft_len == 1024:
        sc = 50
    elif fft_len == 4096:
        sc = 120

    t = np.arange(0, col)  # [freqs, time]
    f = np.arange(-fft_len // 2 + sc, fft_len // 2 - sc) * (wb_fs / np.float32(fft_len)) + wb_fc
    psd_filtered_cliped = psd_filtered[sc + L_filter // 2: -(sc - L_filter // 2), :] # 截取中心区域


    plot_power_spectrum(bshowdgraph, t, f, psd_value_out, sc, psd_filtered_cliped) #显示图像
    # 4 通过阈值，二值化
    max_psd_cliped = np.max(psd_filtered_cliped)
    mean_psd_cliped = np.mean(psd_filtered_cliped)
    MaxMeangap = max_psd_cliped-mean_psd_cliped
    if MaxMeangap > 45.0 : # 部分解决，不完美
        snr_threhold = MaxMeangap * 0.41  # 计算，并滤除小信号,for lora信号
    else : #(max_psd_cliped-mean_psd_cliped) > 20.0 & < 40.0 
        snr_threhold = MaxMeangap * 0.5   # gfsk信号


    nLen_symbols = psd_filtered_cliped.shape[1]  # [freqs, time]
    all_psd = np.zeros((4, nLen_symbols))

    for n in range(nLen_symbols):
        meanpsd = np.mean(psd_filtered_cliped[:, n])  # [f, t]平均功率
        maxpsd = np.max(psd_filtered_cliped[:, n])
        # minpsd = np.min(psd_mean[:, n])
        # if maxpsd > (snr_threhold + meanpsd) * 1.5:
        #     snr_tuned = snr_threhold
        snr_tuned = (maxpsd - meanpsd) * 0.75  # 0.85; 调整后的snr，目的为保证带宽估计比较精确
        if snr_tuned < snr_threhold:  # 小于阈值
            snr_tuned = snr_threhold

        all_psd[0, n] = meanpsd
        all_psd[1, n] = maxpsd
        all_psd[2, n] = maxpsd - meanpsd
        all_psd[3, n] = snr_tuned
        psd_filtered_cliped[:, n] = psd_filtered_cliped[:, n] > (meanpsd + snr_tuned)

    print(f'[chanscan] snr_threhold={snr_threhold:.2f} min_psd={np.min(all_psd[0, :]):.2f} max_psd={np.max(all_psd[0, :]):.2f} MaxMeangap={MaxMeangap:.2f}')

    # %对原有图像进行膨胀算法
    # % 定义结构元素
    se = rectangle(1, 4)
    # % [m n] 是一个包含两个元素的向量，m 表示矩形的行数，n 表示矩形的列数。
    # % 进行膨胀操作
    # 调用函数进行膨胀操作
    psd_value_out = dilation(psd_filtered_cliped, se)

    plot_psd(bshowdgraph, all_psd, t, f, psd_value_out)

   # 5 计算频率联通区域
    fft_step = fft_overlap
    # %可以做
    wb_signal = signal
    label = calunionregion(psd_value_out, wb_signal, t, f, wb_fs/np.float32(fft_len), fft_step, wb_fc, bshowdgraph)

    # BShow_Image = True
    # if BShow_Image:
    #     # 绘制STFT结果
    #     plt.pcolormesh(t_stft, f, zOut_log, shading='gouraud')
    #     plt.title('STFT Magnitude')
    #     plt.xlabel('Time [sec]')
    #     plt.ylabel('Frequency [Hz]')
    #     plt.colorbar(label='Magnitude')
    #     plt.show()

    #print("show Image")

    filename_in = "outsig.bvsp"

    # 6 根据label提取待检测信号，并保存为数据文件hdf5格式
    curstartPosOffset= 0 #1000 #起始位置偏差补偿值，可选输入
    clip_ms = 1.0 #限制的最大时间长度，可选输入
    nb_bw_def = 4e6 #for nb433 #4.0e6  # -- 信号带宽，可选输入
    const_ms = 6.0
    cls_id = 0                           #-- 类别标识(方便AI信号检测用)
    fname_dataset =  "fchanscan-S1.hdf5" # 生成输出文件路径
    if os.path.exists(fname_dataset):
        os.remove(fname_dataset)

    extract_and_save_signals(filename_in, label, wb_signal, curstartPosOffset, wb_fc, wb_fs, clip_ms, const_ms, wb_bw, fft_len, cls_id, fname_dataset, bshowdgraph, nb_bw_def=None)
