function []=GenADSig_dat(sf_gen, nDroneType, to28ms)
%  Function    ：GenADSig_dat
%  Description : 生成AD信号 dat格式
%                先生成基带信号-->信道-->接收抽样-->AD采样-->保存dat
%  Parameter   : 
%                sf_gen      -- 生成dat文件名称
%                nDroneType  -- 无人机类型
%                to28ms      -- 转换为28ms长度
%
%  Author      : Liuzhiguo
%  Date        : 2024-09-13

%clear;
close all;

addpath("lib");             %库函数路径
addpath("usrlib\common");   %用户自定义路径
addpath("usrlib\nb_433\"); 

switch(nDroneType)
    case 0 %nb433
        %1 产生信号
        [sig_base_in, fs_nb, fc, bw] = GenBBSig_nb433();
    otherwise
        return;
end

%2 信道参数
% COST207
v = 10;         %  移动速度(m/s)
c = 3e8;                    % 光速
fd = v*fc/c;
delay = [0 0.1 0.2 0.3 0.4 0.5]*1e-6;  % 时延 (s)
gain = [0 -4 -8 -12 -16 -20];  % 路径增益
raylchannel = comm.RayleighChannel('PathDelays', delay, 'AveragePathGains', gain,...
    'NormalizePathGains',true,'MaximumDopplerShift',fd,'SampleRate',fs_nb);


v = 10;         % 移动速度(m/s) 10m/s = 36km/h
fd = v*fc/c;    % 最大多普勒频移
ricianchan = comm.RicianChannel('PathDelays', delay, 'AveragePathGains', gain,...
    'NormalizePathGains',true,'MaximumDopplerShift',fd,'SampleRate',fs_nb);
dt = 0.5e-3;%0.5ms time offset
df = 0;%1e6;%4K freq offset

SNR=40;%11;
GenSignalFile(sf_gen, sig_base_in, dt, fc,  df, fs_nb, raylchannel, SNR, to28ms);
end

function GenSignalFile(sfname_gen, sig_base_in, dt, fc,  df, fs_nb, chan, SNR, to28ms)
L_header = round(fs_nb*dt);
L_Tail = round(fs_nb*dt);
headerpading = randn(L_header,1)+1j*randn(L_header,1);
tailpading = randn(L_Tail,1)+1j*randn(L_Tail,1);

fs_wb =61.44e6;
%
% sig_base=resample(sig_base_in,fs_wb,fs_nb);
sig_base = sig_base_in;

%eyediagram(sig_base,8);

fc = fc + df;
%%3  基带信号上变频


bw_wb = 52e6;%fs;
t=(0:length(sig_base)-1)'*(1/fs_nb);
sig_base_up = sig_base.*exp(1j*2*pi*fc*t);

%sig_base_up = [headerpading*0.01; sig_base_up; tailpading*0.01];%添加热噪声信号

%% 4 信道
%Sig_noised = chan(sig_base_up); %多径
Sig_noised = sig_base_up;
Sig_noised = Sig_noised/max(abs(Sig_noised));
rxGFskSig_ch=awgn(Sig_noised, SNR,'measured');%噪声
%rxMskSig_ch=Sig_noised;

%% 验证信噪比
% 计算信号的功率
P_sig = sum(abs(Sig_noised).^2) / length(Sig_noised);
% 计算噪声的功率
P_noise = sum(abs(rxGFskSig_ch - Sig_noised).^2) / length(rxGFskSig_ch);
% 计算信噪比（SNR）的dB值
SNR_dB = 10 * log10(P_sig / P_noise);
sOut = sprintf("The SNR of signal is %0.2f dB",SNR_dB);
disp(sOut);

%%5 接收抽样
up_data=resample(rxGFskSig_ch,fs_wb,fs_nb,10,20);
%up_data = rxGFskSig_ch;

if to28ms
    up_data = [zeros(0.002*fs_wb,1);up_data;zeros(0.002*fs_wb,1);up_data;zeros(0.024*fs_wb-2*length(up_data),1)];%补充0到28ms
end
%6 模拟AD采样
hd_data = zeros(length(up_data)*2,1);
hd_data(1:2:end) = real(up_data).*2048/max(abs(up_data));%2048为AD采样的归一化值，12bit AD
hd_data(2:2:end) = imag(up_data).*2048/max(abs(up_data));
hd_data = round(hd_data,0);
%padding到ms


%7 保存文件
% spath = '.\outdataset\test\gendataset\nb_433M\';
% sfname= 'N3.dat';
% soutfname = sprintf('%s%s',spath,sfname);
data_bytes = typecast(int16(hd_data), 'uint8');%转化为字节
data_bytes = data_bytes';
creatEngineFile('version.mat',sfname_gen,data_bytes,fs_wb,bw_wb,fc/1e3);

spath=fullfile(sfname_gen);
sSub = split(spath,'\');
ncount = length(sSub);

sout = sprintf('%s\\%s;[%d, %d];%0.3e\n',cell2mat(sSub(ncount-1)),cell2mat(sSub(ncount)), 1, length(up_data),fc);
disp('文件类别标识:');
disp(sout);
fprintf("the duration time is %.2f ms\n",length(up_data)*1e3/61.44e6);

spath = '.\outdataset\test\gendataset\';
sGenDsfile = sprintf('%s%s',spath,'Gen_ds.txt');
fid = fopen(sGenDsfile,"wt+");%"at+" 
fwrite(fid, sout);
fclose(fid);
% outSig=[outSig;outSig1(:,ii);zeros(0.028*samp_rate_hz-length(outSig1(:,ii)),1)];%28毫秒
% ii=ii+1;

end