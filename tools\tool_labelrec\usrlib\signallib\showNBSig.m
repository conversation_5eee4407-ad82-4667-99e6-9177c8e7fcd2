function []=showNBSig(i_chan,sync_sig_td,nb_fc, nb_fs, sfiguretitle)
%    function:    showNBSig
%    Description: 显示窄带信号（时域、频域）
%
%    Params： i_chan,      -- 信道
%             sync_sig_td, -- I/Q信号
%             nb_fc,    -- 中心频率
%             nb_fs,    -- 采样率
%

% 3.2.3 对于当前chanel的信号，分别显示时域、频域信息
%figure(10+i_chan)
if exist("sfiguretitle","var")>0
    figure('numbertitle','off','name',sfiguretitle); 
else
    figure(11)
end

subplot(411)
% （1） 当前chanel的信号 时域图
N_sig = length(sync_sig_td);
signal_time = (0:N_sig-1)*(1/nb_fs);
signal_time_ms = signal_time*1000;
plot(signal_time_ms, real(sync_sig_td))
xlabel('Time (ms)'); ylabel('I/Q Value');
hold on
plot(signal_time_ms, imag(sync_sig_td))
xlabel('Time (ms)');
hold off

subplot(412);
plot(real(sync_sig_td)) %I路
xlabel('Points'); ylabel('I/Q Value');
hold on
plot(imag(sync_sig_td)) %Q路
xlabel('Points');
hold off

% （2） 当前chanel的信号 时域图
subplot(413)
sig = sync_sig_td;
sig_len = length(sig);
nb_samps = sig_len;
fftdata = fftshift(abs(fft(sig))/sig_len);
nb_freqs=nb_fc - nb_fs/2 +(0 : nb_samps - 1)*nb_fs/nb_samps;
nb_freqs=nb_freqs/1e6;%转换为M
plot(nb_freqs,fftdata);
subtitle(['fc= ' num2str(nb_fc/1000000) 'Mhz' '  fs= ' num2str(nb_fs/1000000) 'Mhz']);

% （3） 瀑布图显示
subplot(414)
if sig_len<4096*2 && sig_len>2048
    window = 1024;%2048;
elseif sig_len<=2048
    window = 256;%2048;
else
    window = 4096;%2048;
end
noverlap = window/2;
nfft = window;
a_channel = sig;
winfilter = hann(window,"periodic");%"periodic" "symmetric"
%winfilter = blackman(window,"periodic");%"periodic" "symmetric"

if length(a_channel)>window
    [S,F,T,P]=spectrogram(a_channel,winfilter,noverlap,nfft,nb_fs,'yaxis');       %频谱 瀑布图
    F = (-window/2:1:window/2-1)*nb_fs/window+nb_fc;%0.015=fs/4096/1e6;
    F = F/1e6;%转换为M
    if length(T)<=1
        return
    end
    surf(T*1000,F,10*log10(circshift(P,window/2)),'edgecolor','none'); axis tight;
    view(0,90);
    xlabel('Time (ms)'); ylabel('MHz');
    title(['fc= ' num2str(nb_fc/1e6) 'Mhz' '  fs= ' num2str(nb_fs/1e6) 'Mhz' ' FFTLen=' num2str(nfft)]);
end

end