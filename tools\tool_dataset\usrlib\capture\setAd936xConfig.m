function status = setAd936xConfig(client,param,value)
    if nargin <=2
        return ;
    end
    send_buf = [param '=' value];
    fprintf(client,'%s\r\n',send_buf);
    data_len = 0;
    sendbuf_len = length(send_buf); 
    rx_data = [];
    while(data_len ~= sendbuf_len+2)
    pause(0.01);
    BytesLen = client.BytesAvailable;
    if  BytesLen >= sendbuf_len+2          
        data=fread(client,sendbuf_len+2,'uchar');       
        rx_data = [rx_data,data'];     
        data_len = data_len + length(data);
    end
    end
    recv_str = char(rx_data);
    status = strncmp(send_buf,recv_str,sendbuf_len);
    if status==0
        disp('tcpip send err');
    end
    fprintf('%s',rx_data);
end