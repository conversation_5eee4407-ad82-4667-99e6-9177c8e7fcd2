% 
% function [] = AutogenNoiseSamples(foldername, duration_ms, noise_clsname, max_noise_per_file)
% % 功能: 自动生成噪声样本切片
% % 参数:
% %   foldername - BVSP文件所在文件夹
% %   duration_ms - 噪声切片时长(毫秒)，默认6ms
% %   noise_clsname - 噪声类别名称，默认'noise'
% %   max_noise_per_file - 每个文件最多生成噪声切片数，默认3
% %
% % 输出:
% %   将噪声样本追加到 train_records.txt
% 
% % 参数默认值处理
% if ~exist('duration_ms', 'var') || isempty(duration_ms)
%     duration_ms = 6;
% end
% 
% if ~exist('noise_clsname', 'var') || isempty(noise_clsname)
%     noise_clsname = 'noise';
% end
% 
% if ~exist('max_noise_per_file', 'var') || isempty(max_noise_per_file)
%     max_noise_per_file = 3;
% end
% 
% % 检查文件夹是否存在
% if ~exist(foldername, 'dir')
%     warning('文件夹：%s不存在\n', foldername);
%     return;
% end
% 
% % 加载正样本标注文件
% label_file = fullfile(foldername, 'Train_records.txt');
% if ~exist(label_file, 'file')
%     error('正样本标注文件不存在，请先运行 AutogenLblsByMeta');
% end
% 
% % 读取标注文件内容
% fid = fopen(label_file, 'r');
% labels = textscan(fid, '%s', 'Delimiter', '\n');
% fclose(fid);
% labels = labels{1};
% 
% % 使用 containers.Map 替代结构体，避免无效字段名问题
% file_labels = containers.Map('KeyType', 'char', 'ValueType', 'any');
% file_frequencies = containers.Map('KeyType', 'char', 'ValueType', 'double'); % 存储每个文件的中心频率
% 
% for i = 1:length(labels)
%     parts = strsplit(labels{i}, ';');
%     if numel(parts) < 2
%         continue; % 跳过无效行
%     end
% 
%     filepath = parts{1};
% 
%     % 提取文件名（不含路径）
%     [~, filename, ext] = fileparts(filepath);
%     bvsp_file = [filename, ext];  % 文件名带扩展名
% 
%     % 解析位置信息 [start,end] 和频率
%     pos_str = parts{2}(2:end-1);
%     positions = sscanf(pos_str, '%d,%d')';
% 
%     % 如果有频率信息，提取频率
%     freq = NaN;
%     if numel(parts) >= 3
%         freq_str = parts{3};
%         if ~isempty(freq_str)
%             freq = str2double(freq_str);
%         end
%     end
% 
%     if numel(positions) == 2
%         % 添加到映射
%         if isKey(file_labels, bvsp_file)
%             file_labels(bvsp_file) = [file_labels(bvsp_file); positions];
%             if ~isnan(freq)
%                 file_frequencies(bvsp_file) = freq; % 更新为最新频率
%             end
%         else
%             file_labels(bvsp_file) = positions;
%             if ~isnan(freq)
%                 file_frequencies(bvsp_file) = freq;
%             end
%         end
%     end
% end
% 
% % 打开标注文件追加模式
% fout = fopen(label_file, 'a');
% 
% % 处理每个BVSP文件
% bvsp_files = dir(fullfile(foldername, '*.bvsp'));
% for i = 1:length(bvsp_files)
%     bvsp_file = bvsp_files(i).name;
% 
%     % 加载BVSP文件获取默认中心频率
%     filepath = fullfile(foldername, bvsp_file);
%     try
%         [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filepath);
%     catch
%         warning('文件加载失败: %s', filepath);
%         continue;
%     end
% 
%     if isempty(wb_signal)
%         warning('文件内容为空: %s', filepath);
%         continue;
%     end
% 
%     % 确定该文件的中心频率
%     if isKey(file_frequencies, bvsp_file)
%         file_fc = file_frequencies(bvsp_file); % 使用有效信号的频率
%     else
%         file_fc = wb_fc; % 使用文件默认频率
%     end
% 
%     nSig_len = length(wb_signal);
% 
%     % 计算切片长度
%     segLen = round(wb_fs * duration_ms / 1000);
%     if segLen < 10 || segLen > nSig_len
%         warning('无效切片长度: %d (文件长度: %d)', segLen, nSig_len);
%         continue;
%     end
% 
%     % 获取该文件的所有正样本区间（如果有）
%     if isKey(file_labels, bvsp_file)
%         signal_intervals = file_labels(bvsp_file);
%     else
%         signal_intervals = [];
%         fprintf('文件无有效标注，将使用文件中心频率: %.3f MHz\n', file_fc/1e6);
%     end
% 
%     % 计算背景噪声功率阈值
%     min_clippower = findpowerthredhold(wb_signal);
%     [fftdata, ~] = getfftData(wb_signal, file_fc, wb_fs); % 使用确定的中心频率
%     if isempty(fftdata)
%         warning('FFT计算失败: %s', bvsp_file);
%         continue;
%     end
% 
%     % 更宽松的频域阈值
%     powerthreshold_fc = max(fftdata) * 0.8; % 使用最大功率的80%作为阈值
% 
%     % 生成候选噪声区间
%     noise_gaps = [];
%     safe_gap = 150; % 安全间隔
% 
%     if ~isempty(signal_intervals)
%         % 文件起始到第一个信号
%         first_start = min(signal_intervals(:,1));
%         if first_start > segLen + safe_gap
%             noise_gaps = [noise_gaps; 1, first_start - safe_gap];
%         end
% 
%         % 信号之间的间隙
%         for k = 1:size(signal_intervals,1)-1
%             gap_start = signal_intervals(k,2) + safe_gap;
%             gap_end = signal_intervals(k+1,1) - safe_gap;
% 
%             if gap_end > gap_start && (gap_end - gap_start) >= segLen
%                 noise_gaps = [noise_gaps; gap_start, gap_end];
%             end
%         end
% 
%         % 最后一个信号到文件结尾
%         last_end = max(signal_intervals(:,2));
%         if nSig_len - last_end > segLen + safe_gap
%             noise_gaps = [noise_gaps; last_end + safe_gap, nSig_len];
%         end
%     else
%         % 没有信号标注，尝试在整个文件中随机采样
%         noise_gaps = [1, nSig_len];
%         fprintf('警告: 文件 %s 没有信号标注，将随机采样\n', bvsp_file);
%     end
% 
%     % 在候选区间生成噪声切片
%     noise_count = 0;
%     for g = 1:size(noise_gaps,1)
%         gap_start = noise_gaps(g,1);
%         gap_end = noise_gaps(g,2);
%         gap_len = gap_end - gap_start;
% 
%         % 尝试多个随机位置
%         attempts = min(5, max_noise_per_file); % 增加尝试次数
%         for attempt = 1:attempts
%             if noise_count >= max_noise_per_file
%                 break;
%             end
% 
%             % 随机选择起始位置
%             if gap_len > segLen
%                 rand_offset = randi([0, max(0, gap_len - segLen)]);
%             else
%                 rand_offset = 0; % 如果间隙小，从起始开始
%             end
% 
%             candidate_start = gap_start + rand_offset;
%             candidate_end = candidate_start + segLen;
% 
%             % 确保不超出文件范围
%             if candidate_end > nSig_len
%                 fprintf('  位置超出范围: %d > %d\n', candidate_end, nSig_len);
%                 continue;
%             end
% 
%             % 确保不重叠（如果有信号标注）
%             if ~isempty(signal_intervals) && overlapsAnySignal(candidate_start, candidate_end, signal_intervals, safe_gap)
%                 fprintf('  与信号重叠: [%d-%d]\n', candidate_start, candidate_end);
%                 continue;
%             end
% 
%             candidate_sig = wb_signal(candidate_start:candidate_end);
% 
%             % 更宽松的噪声条件检查
%             if ~isValidNoiseSample(candidate_sig, file_fc, wb_fs, min_clippower, powerthreshold_fc)
%                 fprintf('  未通过噪声检查: [%d-%d]\n', candidate_start, candidate_end);
%                 continue;
%             end
% 
%             % 计算噪声功率和SNR
%             noise_power = sqrt(mean(abs(candidate_sig).^2));
%             snr_db = 10*log10(max(noise_power^2 / min_clippower^2, 1e-10));
% 
%             % 写入噪声样本
%             slabel_clip = fullfile(noise_clsname, bvsp_file);
%             % 使用文件的中心频率（可能是信号频率或文件默认频率）
%             file_fc_scientific = sprintf('%.3fe6', file_fc/1e6); % 转换为科学计数法
%             slable = sprintf("%s;[%d,%d];%s;%.2f\n", slabel_clip, candidate_start, candidate_end, file_fc_scientific, snr_db);
%             fwrite(fout, slable);
% 
%             noise_count = noise_count + 1;
%             fprintf('  生成噪声切片: [%d-%d] 长度=%d, 功率=%.4f, 中心频率=%.3f MHz\n',...
%                     candidate_start, candidate_end, segLen, noise_power, file_fc/1e6);
%         end
%     end
% 
%     if noise_count == 0
%         fprintf('  警告: 未找到有效噪声切片\n');
%     else
%         fprintf('  成功生成 %d 个噪声切片\n', noise_count);
%     end
% end
% 
% fclose(fout);
% fprintf('噪声样本生成完成。结果已追加到: %s\n', label_file);
% end
% 
% 
% function valid = isValidNoiseSample(signal, wb_fc, wb_fs, min_clippower, powerthreshold_fc)
% % 检查是否为有效的噪声样本 - 更宽松的条件
% valid = true;
% 
% % 1. 检查时域功率 - 更宽松的条件
% avg_power = sqrt(mean(abs(signal).^2));
% if avg_power > min_clippower * 2.0 % 从1.5放宽到2.0
%     valid = false;
%     return;
% end
% 
% % 2. 检查频域峰值 - 更宽松的条件
% try
%     [fftdata, ~] = getfftData(signal, wb_fc, wb_fs);
%     if max(fftdata) >= powerthreshold_fc * 1.2 % 增加20%容差
%         valid = false;
%         return;
%     end
% catch
%     valid = false;
%     return;
% end
% 
% % 3. 检查越界点 - 更宽松的条件
% if isOverThresholdM(signal)
%     valid = false;
%     return;
% end
% end
% 
% function overlap = overlapsAnySignal(start_pos, end_pos, signal_intervals, buffer)
% % 检查是否与任何信号区间重叠
% overlap = false;
% if isempty(signal_intervals)
%     return;
% end
% 
% for i = 1:size(signal_intervals,1)
%     sig_start = signal_intervals(i,1) - buffer;
%     sig_end = signal_intervals(i,2) + buffer;
% 
%     if ~(end_pos < sig_start || start_pos > sig_end)
%         overlap = true;
%         return;
%     end
% end
% end
% 
% function min_clippower = findpowerthredhold(wb_signal)
% % 寻找功率阈值 - 实际实现
% % 使用信号的最小功率作为背景噪声估计
% min_power = inf;
% num_segments = 20;
% segment_length = floor(length(wb_signal) / num_segments);
% 
% for i = 1:num_segments
%     start_idx = (i-1)*segment_length + 1;
%     end_idx = i*segment_length;
%     segment = wb_signal(start_idx:end_idx);
%     segment_power = sqrt(mean(abs(segment).^2));
% 
%     if segment_power < min_power
%         min_power = segment_power;
%     end
% end
% 
% min_clippower = max(min_power, 1e-6); % 防止为0
% end
% 
% function [fftdata, nb_freqs] = getfftData(wb_signal, wb_fc, wb_fs)
% % 整个信号长度的fft变换
% try
%     nSig_len = length(wb_signal);
%     fftdata = fftshift(abs(fft(wb_signal))/nSig_len); 
%     nb_freqs = wb_fc - wb_fs/2 + (0:nSig_len-1)*wb_fs/nSig_len;
% catch
%     fftdata = [];
%     nb_freqs = [];
% end
% end
% 
% function flag = isOverThresholdM(clip_signal)
% % 检查信号是否包含越界点 - 简化实现
% max_val = max([max(abs(real(clip_signal))), max(abs(imag(clip_signal)))]);
% flag = (max_val > 2000);
% end

function [] = AutogenNoiseSamples(foldername, duration_ms, noise_clsname, max_noise_per_file)
% 功能: 自动生成噪声样本切片
% 参数:
%   foldername - BVSP文件所在文件夹
%   duration_ms - 噪声切片时长(毫秒)，默认6ms
%   noise_clsname - 噪声类别名称，默认'noise'
%   max_noise_per_file - 每个文件最多生成噪声切片数，默认3
%
% 输出:
%   将噪声样本写入 Noise_records.txt 文件

% 参数默认值处理
if ~exist('duration_ms', 'var') || isempty(duration_ms)
    duration_ms = 6;
end

if ~exist('noise_clsname', 'var') || isempty(noise_clsname)
    noise_clsname = 'noise';
end

if ~exist('max_noise_per_file', 'var') || isempty(max_noise_per_file)
    max_noise_per_file = 3;
end

% 检查文件夹是否存在
if ~exist(foldername, 'dir')
    warning('文件夹：%s不存在\n', foldername);
    return;
end

% 加载正样本标注文件(仅用于获取信号位置信息)
label_file = fullfile(foldername, 'Train_records.txt');
if ~exist(label_file, 'file')
    error('正样本标注文件不存在，请先运行 AutogenLblsByMeta');
end

% 创建新的噪声记录文件
noise_label_file = fullfile(foldername, 'Noise_records.txt');
if exist(noise_label_file, 'file')
    % 如果已存在，询问是否覆盖
    answer = input('Noise_records.txt已存在，是否覆盖? (y/n): ', 's');
    if lower(answer) ~= 'y'
        fprintf('操作已取消\n');
        return;
    end
end

% 读取标注文件内容
fid = fopen(label_file, 'r');
labels = textscan(fid, '%s', 'Delimiter', '\n');
fclose(fid);
labels = labels{1};

% 使用 containers.Map 存储文件信息
file_labels = containers.Map('KeyType', 'char', 'ValueType', 'any');
file_frequencies = containers.Map('KeyType', 'char', 'ValueType', 'double');

for i = 1:length(labels)
    parts = strsplit(labels{i}, ';');
    if numel(parts) < 2
        continue; % 跳过无效行
    end
    
    filepath = parts{1};
    
    % 提取文件名（不含路径）
    [~, filename, ext] = fileparts(filepath);
    bvsp_file = [filename, ext];  % 文件名带扩展名
    
    % 解析位置信息 [start,end] 和频率
    pos_str = parts{2}(2:end-1);
    positions = sscanf(pos_str, '%d,%d')';
    
    % 如果有频率信息，提取频率
    freq = NaN;
    if numel(parts) >= 3
        freq_str = parts{3};
        if ~isempty(freq_str)
            freq = str2double(freq_str);
        end
    end
    
    if numel(positions) == 2
        % 添加到映射
        if isKey(file_labels, bvsp_file)
            file_labels(bvsp_file) = [file_labels(bvsp_file); positions];
            if ~isnan(freq)
                file_frequencies(bvsp_file) = freq; % 更新为最新频率
            end
        else
            file_labels(bvsp_file) = positions;
            if ~isnan(freq)
                file_frequencies(bvsp_file) = freq;
            end
        end
    end
end

% 打开噪声记录文件(新建)
fout = fopen(noise_label_file, 'w');
if fout == -1
    error('无法创建噪声记录文件: %s', noise_label_file);
end

% 写入文件头
fprintf(fout, '# Noise samples generated by AutogenNoiseSamples\n');
fprintf(fout, '# Format: filename;[start,end];frequency(MHz);SNR(dB)\n');

% 处理每个BVSP文件
bvsp_files = dir(fullfile(foldername, '*.bvsp'));
for i = 1:length(bvsp_files)
    bvsp_file = bvsp_files(i).name;
    
    % 加载BVSP文件获取默认中心频率
    filepath = fullfile(foldername, bvsp_file);
    try
        [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filepath);
    catch
        warning('文件加载失败: %s', filepath);
        continue;
    end
    
    if isempty(wb_signal)
        warning('文件内容为空: %s', filepath);
        continue;
    end
    
    % 确定该文件的中心频率
    if isKey(file_frequencies, bvsp_file)
        file_fc = file_frequencies(bvsp_file); % 使用有效信号的频率
    else
        file_fc = wb_fc; % 使用文件默认频率
    end
    
    nSig_len = length(wb_signal);
    
    % 计算切片长度
    segLen = round(wb_fs * duration_ms / 1000);
    if segLen < 10 || segLen > nSig_len
        warning('无效切片长度: %d (文件长度: %d)', segLen, nSig_len);
        continue;
    end
    
    % 获取该文件的所有正样本区间（如果有）
    if isKey(file_labels, bvsp_file)
        signal_intervals = file_labels(bvsp_file);
    else
        signal_intervals = [];
        fprintf('文件无有效标注，将使用文件中心频率: %.3f MHz\n', file_fc/1e6);
    end
    
    % 计算背景噪声功率阈值
    min_clippower = findpowerthredhold(wb_signal);
    [fftdata, ~] = getfftData(wb_signal, file_fc, wb_fs);
    if isempty(fftdata)
        warning('FFT计算失败: %s', bvsp_file);
        continue;
    end
    
    % 频域阈值
    powerthreshold_fc = max(fftdata) * 0.8;
    
    % 生成候选噪声区间
    noise_gaps = [];
    safe_gap = 150; % 安全间隔
    
    if ~isempty(signal_intervals)
        % 文件起始到第一个信号
        first_start = min(signal_intervals(:,1));
        if first_start > segLen + safe_gap
            noise_gaps = [noise_gaps; 1, first_start - safe_gap];
        end
        
        % 信号之间的间隙
        for k = 1:size(signal_intervals,1)-1
            gap_start = signal_intervals(k,2) + safe_gap;
            gap_end = signal_intervals(k+1,1) - safe_gap;
            
            if gap_end > gap_start && (gap_end - gap_start) >= segLen
                noise_gaps = [noise_gaps; gap_start, gap_end];
            end
        end
        
        % 最后一个信号到文件结尾
        last_end = max(signal_intervals(:,2));
        if nSig_len - last_end > segLen + safe_gap
            noise_gaps = [noise_gaps; last_end + safe_gap, nSig_len];
        end
    else
        % 没有信号标注，尝试在整个文件中随机采样
        noise_gaps = [1, nSig_len];
        fprintf('警告: 文件 %s 没有信号标注，将随机采样\n', bvsp_file);
    end
    
    % 在候选区间生成噪声切片
    noise_count = 0;
    for g = 1:size(noise_gaps,1)
        gap_start = noise_gaps(g,1);
        gap_end = noise_gaps(g,2);
        gap_len = gap_end - gap_start;
        
        % 尝试多个随机位置
        attempts = min(5, max_noise_per_file);
        for attempt = 1:attempts
            if noise_count >= max_noise_per_file
                break;
            end
            
            % 随机选择起始位置
            if gap_len > segLen
                rand_offset = randi([0, max(0, gap_len - segLen)]);
            else
                rand_offset = 0;
            end
            
            candidate_start = gap_start + rand_offset;
            candidate_end = candidate_start + segLen;
            
            % 确保不超出文件范围
            if candidate_end > nSig_len
                fprintf('  位置超出范围: %d > %d\n', candidate_end, nSig_len);
                continue;
            end
            
            % 确保不重叠（如果有信号标注）
            if ~isempty(signal_intervals) && overlapsAnySignal(candidate_start, candidate_end, signal_intervals, safe_gap)
                fprintf('  与信号重叠: [%d-%d]\n', candidate_start, candidate_end);
                continue;
            end
            
            candidate_sig = wb_signal(candidate_start:candidate_end);
            
            % 噪声条件检查
            if ~isValidNoiseSample(candidate_sig, file_fc, wb_fs, min_clippower, powerthreshold_fc)
                fprintf('  未通过噪声检查: [%d-%d]\n', candidate_start, candidate_end);
                continue;
            end
            
            % 计算噪声功率和SNR
            noise_power = sqrt(mean(abs(candidate_sig).^2));
            snr_db = 10*log10(max(noise_power^2 / min_clippower^2, 1e-10));
            
            % 写入噪声样本到新文件
            slabel_clip = fullfile(noise_clsname, bvsp_file);
            file_fc_scientific = sprintf('%.3fe6', file_fc/1e6); % 转换为科学计数法
            slable = sprintf("%s;[%d,%d];%s;%.2f\n", slabel_clip, candidate_start, candidate_end, file_fc_scientific, snr_db);
            fwrite(fout, slable);
            
            noise_count = noise_count + 1;
            fprintf('  生成噪声切片: [%d-%d] 长度=%d, 功率=%.4f, 中心频率=%.3f MHz\n',...
                    candidate_start, candidate_end, segLen, noise_power, file_fc/1e6);
        end
    end
    
    if noise_count == 0
        fprintf('  警告: 未找到有效噪声切片\n');
    else
        fprintf('  成功生成 %d 个噪声切片\n', noise_count);
    end
end

fclose(fout);
fprintf('噪声样本生成完成。结果已保存到: %s\n', noise_label_file);
end


function valid = isValidNoiseSample(signal, wb_fc, wb_fs, min_clippower, powerthreshold_fc)
% 检查是否为有效的噪声样本 - 更宽松的条件
valid = true;

% 1. 检查时域功率 - 更宽松的条件
avg_power = sqrt(mean(abs(signal).^2));
if avg_power > min_clippower * 2.0 % 从1.5放宽到2.0
    valid = false;
    return;
end

% 2. 检查频域峰值 - 更宽松的条件
try
    [fftdata, ~] = getfftData(signal, wb_fc, wb_fs);
    if max(fftdata) >= powerthreshold_fc * 1.2 % 增加20%容差
        valid = false;
        return;
    end
catch
    valid = false;
    return;
end

% 3. 检查越界点 - 更宽松的条件
if isOverThresholdM(signal)
    valid = false;
    return;
end
end

function overlap = overlapsAnySignal(start_pos, end_pos, signal_intervals, buffer)
% 检查是否与任何信号区间重叠
overlap = false;
if isempty(signal_intervals)
    return;
end

for i = 1:size(signal_intervals,1)
    sig_start = signal_intervals(i,1) - buffer;
    sig_end = signal_intervals(i,2) + buffer;

    if ~(end_pos < sig_start || start_pos > sig_end)
        overlap = true;
        return;
    end
end
end

function min_clippower = findpowerthredhold(wb_signal)
% 寻找功率阈值 - 实际实现
% 使用信号的最小功率作为背景噪声估计
min_power = inf;
num_segments = 20;
segment_length = floor(length(wb_signal) / num_segments);

for i = 1:num_segments
    start_idx = (i-1)*segment_length + 1;
    end_idx = i*segment_length;
    segment = wb_signal(start_idx:end_idx);
    segment_power = sqrt(mean(abs(segment).^2));

    if segment_power < min_power
        min_power = segment_power;
    end
end

min_clippower = max(min_power, 1e-6); % 防止为0
end

function [fftdata, nb_freqs] = getfftData(wb_signal, wb_fc, wb_fs)
% 整个信号长度的fft变换
try
    nSig_len = length(wb_signal);
    fftdata = fftshift(abs(fft(wb_signal))/nSig_len); 
    nb_freqs = wb_fc - wb_fs/2 + (0:nSig_len-1)*wb_fs/nSig_len;
catch
    fftdata = [];
    nb_freqs = [];
end
end

function flag = isOverThresholdM(clip_signal)
% 检查信号是否包含越界点 - 简化实现
max_val = max([max(abs(real(clip_signal))), max(abs(imag(clip_signal)))]);
flag = (max_val > 2000);
end
