import math

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import Module, Parameter

from nets.iresnet import (iresnet18, iresnet34, iresnet50, iresnet100,
                          iresnet200)
from nets.mobilefacenet import get_mbf
from nets.mobilenet import get_mobilenet
from nets.DroneSigNet import DroneSigNet 
from nets.swin_transformer import swin_transformer_tiny
from nets.Module_attentions import SE_Row

class Arcface_Head(Module):
    def __init__(self, embedding_size=128, num_classes=10575, s=64., m=0.5):
        super(Arcface_Head, self).__init__()
        self.s = s
        self.m = m
        self.weight = Parameter(torch.FloatTensor(num_classes, embedding_size))
        nn.init.xavier_uniform_(self.weight)

        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, input, label):
        cosine  = F.linear(input, F.normalize(self.weight))
        sine    = torch.sqrt((1.0 - torch.pow(cosine, 2)).clamp(0, 1))
        phi     = cosine * self.cos_m - sine * self.sin_m
        phi     = torch.where(cosine.float() > self.th, phi.float(), cosine.float() - self.mm)

        one_hot = torch.zeros(cosine.size()).type_as(phi).long()
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        output  = (one_hot * phi) + ((1.0 - one_hot) * cosine) 
        output  *= self.s
        return output

class Arcface(nn.Module):
    def __init__(self, num_classes=None, backbone="mobilefacenet", pretrained=False, mode="train", ModelType = 0):
        super(Arcface, self).__init__()
        self.in_ch = 8
        #self.weights = torch.tensor([0.5, 0.4, 0.1], requires_grad=True)
        self.weights = [0.2, 0.4, 0.3, 0.1]
        #self.max_pooling_layer = nn.MaxPool2d(kernel_size=(self.in_ch, 1), stride=(self.in_ch, 1))
        #self.max_pooling_layer = nn.AdaptiveMaxPool2d([128, 116])
        self.conv_pooling_layer = nn.Conv2d(1, 1, kernel_size=[8,1], stride=[8, 1], bias=False)
        self.se_row_layer = SE_Row(in_chnls=4, ratio=1)
        
        if backbone=="mobilefacenet":
            embedding_size  = 128
            s               = 32
            self.arcface    = get_mbf(embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="mobilenetv1":
            embedding_size  = 512
            s               = 64
            self.arcface    = get_mobilenet(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet18":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet18(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet34":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet34(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet50":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet50(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet100":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet100(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)

        elif backbone=="iresnet200":
            embedding_size  = 512
            s               = 64
            self.arcface    = iresnet200(dropout_keep_prob=0.5, embedding_size=embedding_size, pretrained=pretrained)
        elif backbone=="DroneSigNet":
            embedding_size  = 64#96#512
            s               = 64
            self.arcface    = DroneSigNet(embedding_size=embedding_size) 
        elif backbone=="swin_transformer_tiny":
            embedding_size  = 512
            s               = 64
            self.arcface    = swin_transformer_tiny(input_shape=[1024, 116+12],pretrained=False,num_classes=embedding_size)           
        else:
            raise ValueError('Unsupported backbone - `{}`, Use mobilefacenet, mobilenetv1.'.format(backbone))

        self.mode = mode
        if mode == "train":#匹配头
            self.head = Arcface_Head(embedding_size=embedding_size, num_classes=num_classes, s=s)
        
        #分类头 (分类用)
        self.ClsHead = nn.Sequential(
            nn.Dropout(p=0),#0.2
            nn.Linear(embedding_size, num_classes),
        )

        self.HeaderType = ModelType

    def preProcess(self, rx_signals):
        # 1. 计算stft
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]

        # 缩小化抽取
        #z_sub = F.interpolate(z,size=[nCarriers//8, nTimesym],mode='bilinear')
        z_sub = self.conv_pooling_layer(z)

        # 2 在时间维度，数据分为4块，分别设置不同权重
        chunks = torch.chunk(z, 4, dim=-1)
        #信号分成3段，分别赋予不同权重
        # 为每块赋予权重
        weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 重新组合
        z = torch.cat(weighted_chunks, dim=-1)

        nbatch, _ , nCarriers, nTimesym = z.shape
        n_subcount = math.floor(N_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym))

        z = torch.cat((z,z_sub), dim=1)#增加一个全局维度

        return z

    def preProcessExt(self, rx_signals):
        # 1. 计算stft
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]

        # 缩小化抽取
        #z_sub = F.interpolate(z,size=[nCarriers//8, nTimesym],mode='bilinear')
        z_sub = self.conv_pooling_layer(z)

        # 2 在时间维度，数据分为4块，分别设置不同权重
        chunks = torch.chunk(z, 4, dim=-1)
        chunks = torch.cat(chunks, dim=1)
        
        #信号分成4段，分别赋予不同权重
        # 为每块应用注意力机制
        weighted_chunks = self.se_row_layer(chunks)
        weighted_chunks = torch.chunk(weighted_chunks, 4, dim=1)
        #weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 重新组合
        z = torch.cat(weighted_chunks, dim=-1)
        
        nbatch, _ , nCarriers, nTimesym = z.shape
        n_subcount = math.floor(N_fft/self.in_ch)
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym))

        z = torch.cat((z,z_sub), dim=1)#增加一个全局维度

        return z
    
    def preProcessBig(self, rx_signals):
        # 1. 计算stft
        N_fft = 1024 #self.subcarriers
        N_window = N_fft  
        N_overlap = math.floor(N_fft/2) 
        if len(rx_signals.shape)>3:
            y = rx_signals.squeeze() # [64, 1, 59392, 2] --> [64, 59392, 2]
        else:
            y = rx_signals
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数 [64, 59392]
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)
        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]

        z = torch.abs(z).unsqueeze(-1) # torch.Size([64, 1024, 116, 1])
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]
        z_last4col = z[:,:,:,-12:]
        z = torch.cat((z,z_last4col),dim=3)# torch.Size([64, 1024, 128, 1])

        return z

    def forward(self, x, y = None, mode = "predict"):
        #x = self.preProcess(x) # （128 ，116）   #basic
        #x = self.preProcessBig(x) # （1024，116）#swTrans
        x = self.preProcessExt(x) # （128 ，116）#basic improved
        
        x = self.arcface(x)
        x = x.view(x.size()[0], -1)


        if self.HeaderType == 0:# 分类模型
            x = F.normalize(x)
            x = self.ClsHead(x) # [batch self.nclass] 
        else:
            if mode == "predict":
                return x
            else:
                x = F.normalize(x)
                x = self.head(x, y)
        return x
