%% This load_sigcap handles sigpkt version 6 and later.

function packet = load_sigcap(file_path)
v = uint8(read_byte_binary(file_path));

magic_flag = typecast(v(1:2), 'uint16');
version =  double(typecast(v(3), 'uint8'));

%% Check magic flag and version
if magic_flag ~= 20565
    error('Invalid magic flag: %x', magic_flag);
end

if version < 6
    error('Too old sigpkt version %d. Please try load_sigcap_legacy.', version);
end

%% Parse header
header_vb = v(1:64);
segment_index =  double(typecast(header_vb(4), 'uint8'));
header_length =  double(typecast(header_vb(5:6), 'uint16'));
num_mimo_ant =  double(typecast(header_vb(7), 'uint8'));
packet_type = double(typecast(header_vb(8), 'uint8'));
total_length =  double(typecast(header_vb(9:16), 'uint64'));
timestamp =  double(typecast(header_vb(17:24), 'uint64'));
ticks_per_second =  double(typecast(header_vb(25:28), 'uint32'));
packet_index = double(typecast(header_vb(29:32), 'uint32'));

sensor_id_bytes = header_vb(33:48);
idx = find(sensor_id_bytes == 0, 1);
if idx == 1
    sensor_id = '';
else
    sensor_id = native2unicode(header_vb(33:31+idx), 'US-ASCII')';
end

samp_rate = double(typecast(header_vb(49:52), 'int32'));
bandwidth = double(typecast(header_vb(53:56), 'int32'));
center_freq = double(typecast(header_vb(57:60), 'int32')) * 1000;
rx_gain = double([header_vb(61), header_vb(62), header_vb(63), header_vb(64)]);

if version >= 7
    polling_period = double(typecast(v(65:68), 'uint32'));
    default_port = double(typecast(v(69), 'uint8'));
    num_pollport = double(typecast(v(70), 'uint8'));
    sample_label_indicator = double(typecast(v(72), 'uint8'));
    
    drone_indicator = typecast(v(89:92), 'uint32');
end

if version >= 8
    request_id = typecast(v(93:96), 'uint32');
    
    polaris_detect_addr = typecast(v(73:76), 'uint32'); 
    polaris_center_freq = typecast(v(77:80), 'uint32');
    polaris_timestamp_ns = typecast(v(81:88), 'uint64');
end

%% Parse data
data_vb = v(header_length+1:end);
raw_data_u16 = typecast(data_vb, 'uint16');

% Remove 4 MSBits from raw data and convert to float
data_s16 = typecast(raw_data_u16, 'int16');
data_s16 = bitshift(data_s16, 4, 'int16');
data_s16 = bitshift(data_s16, -4, 'int16');

data_floats = double(data_s16);
data = data_floats(1:2:end) + 1j*data_floats(2:2:end);
data = reshape(data, num_mimo_ant, []).';

if version >= 7
    raw_data_ilabel = raw_data_u16(1 : 2*num_mimo_ant : end);
    raw_data_qlabel = raw_data_u16(2 : 2*num_mimo_ant : end);
    ilabel = bitshift(raw_data_ilabel, -12, 'uint16');
    qlabel = bitshift(raw_data_qlabel, -12, 'uint16');
    
    % populate switching antenna port numbers
    sw_ant_ports = ilabel;
    
    % populate timestamps along of signal samples
    preamble_cnt = 0;
    sync_offset = 0;
    for i = 1 : 64
        if qlabel(i) == 10
            preamble_cnt = preamble_cnt + 1;
        else
            if preamble_cnt == 8
                sync_offset = i - 8;
                break;
            else
                preamble_cnt = 0;
            end
        end
    end
    
    if sync_offset == 0
        % error('Error format: preamble_cnt = 0');
        inl_timestamp_ns = [];
        gain_data_lsb = [];
        gain_data_msb = [];
        gain_dB = 0;
    else
        num_syncs = floor((length(qlabel) - sync_offset + 1) / 40);
        qlabel_ticks = uint32(zeros(num_syncs, 1));
        qlabel_tps = uint32(zeros(num_syncs, 1));
        qlabel_sec = uint32(zeros(num_syncs, 1));
        for i_sync = 1:num_syncs
            sync_idx = (i_sync-1)*40 + sync_offset;
            qlabel_ticks(i_sync) = compact_hex(qlabel(sync_idx+8 : sync_idx+15));
            qlabel_tps(i_sync) = compact_hex(qlabel(sync_idx+16 : sync_idx+23));
            qlabel_sec(i_sync) = compact_hex(qlabel(sync_idx+24 : sync_idx+31));
        end
        qlabel_nanosec = uint64(qlabel_sec) * uint64(1000000000) + ...
            uint64(qlabel_ticks) * uint64(1000000000) ./ uint64(qlabel_tps);
        
        inl_timestamp_ns = uint64(zeros(length(qlabel), 1));
        for k = 1:length(inl_timestamp_ns)
            if k < sync_offset
                inl_timestamp_ns(k) = qlabel_nanosec(1) - ...
                    uint64(round((sync_offset-k) / samp_rate * 1e9));
            else
                qlabel_idx = floor((k - sync_offset)/40) + 1;
                if qlabel_idx > length(qlabel_nanosec)
                    qlabel_idx = length(qlabel_nanosec);
                end
                
                qlabel_sync_offset = sync_offset + (qlabel_idx-1) * 40;
                inl_timestamp_ns(k) = qlabel_nanosec(qlabel_idx) + ...
                    uint64(round((k - qlabel_sync_offset) / samp_rate * 1e9));
            end
        end
        
        % populate gains along of signal samples
        gain_data_msb = qlabel(sync_offset+32 : 40 : end);
        gain_data_lsb = qlabel(sync_offset+33 : 40 : end);
        gain_data_msb = gain_data_msb(1:length(gain_data_lsb));
        
        curr_table_idx = double(gain_data_msb*16 + gain_data_lsb);
        
        if center_freq <= 1300e6
            idx_step_offset = 0;
            starting_gain_dB = 1;
            gain_step_dB = 1;
        elseif center_freq <= 4000e6
            idx_step_offset = 1;
            starting_gain_dB = -4;
            gain_step_dB = 1;
        else
            idx_step_offset = 4;
            starting_gain_dB = -10;
            gain_step_dB = 1;
        end
        
        agc_gain = starting_gain_dB + (curr_table_idx - idx_step_offset) * gain_step_dB;
        agc_gain(agc_gain < starting_gain_dB) = starting_gain_dB;
        
        if sync_offset > 1
            agc_gain_1 = agc_gain(1) * ones(sync_offset-1, 1);
        else
            agc_gain_1 = [];
        end
        
        agc_gain_2 = reshape(repmat(agc_gain, 1, 40)', [], 1);
        
        if length(agc_gain_2) < length(qlabel) - length(agc_gain_1)
            agc_gain_3 = agc_gain_2(end) * ones(length(qlabel)-length(agc_gain_1)-length(agc_gain_2), 1);
        else
            agc_gain_2 = agc_gain_2(1 : length(qlabel)-length(agc_gain_1));
            agc_gain_3 = [];
        end
        
        gain_dB = [agc_gain_1; agc_gain_2; agc_gain_3];
    end
end

%% Arrange outputs
header.magic_flag_hex = dec2hex(magic_flag);
header.magic_flag = magic_flag;
header.version = version;
header.segment_index = segment_index;
header.header_length = header_length;
header.num_mimo_ant = num_mimo_ant;
header.packet_type = packet_type;
header.total_length = total_length;
header.timestamp = timestamp;
header.ticks_per_second = ticks_per_second;
header.packet_index = packet_index;
header.sensor_id = sensor_id;
header.samp_rate = samp_rate;
header.bandwidth = bandwidth;
header.center_freq = center_freq;
header.rx_gain = rx_gain;

if exist('inl_timestamp_ns', 'var') && ~isempty(inl_timestamp_ns)
    header.pkt_timestamp_ns = inl_timestamp_ns(1);
    header.cal_fs_factor = (length(data) - 1) / ...
        double(inl_timestamp_ns(end)-inl_timestamp_ns(1)) * 1e9 / samp_rate;
end

% V7 options
if version >= 7
    header.polling_period = polling_period;
    header.default_port = default_port;
    header.num_pollport = num_pollport;
    header.sample_label_indicator = sample_label_indicator;
    
    header.drone_indicator = drone_indicator;
end

% V8 options
if version >= 8
    header.request_id = request_id;
    header.polaris_detect_addr = polaris_detect_addr;
    header.polaris_center_freq = polaris_center_freq;
    header.polaris_timestamp_ns = polaris_timestamp_ns;
end

% Packet to output
packet.header = header;
packet.data = data;
packet.raw_data = raw_data_u16;

if version >= 7
    packet.sw_ant_ports = sw_ant_ports;
    packet.inl_timestamp_ns = inl_timestamp_ns;
    
    packet.gain_dB = gain_dB;
end

end

function d = compact_hex(h_arr)
d = uint32(0);
for i_hex = 1:length(h_arr)
    d = bitor(bitshift(d, 4), double(h_arr(i_hex)), 'uint32');
end
end
