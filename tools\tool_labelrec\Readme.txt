                                                             说明：
1.cmdAutolabelsByMeta.m  ----  自动标注工具，根据meta文件生成标注文件，输入：包含meta文件的类别目录 输出：标注后的txt文件，Train_records.txt
                              在自动标注的过程中会过滤无效的信号，如起始位置错误，相关中心频率无信号、低信噪比等情况。
2.calCalSNR.m            ----  计算每个类别的Train_records_byhand.txt中的snr值，并添加到Train_records.txt后面
3.cmdAddScence.m         ---- 添加标签工具，如2730.bvsp 添加场景后--> CJ0_1M_2730.bvsp，其中，CJ 表示 场景, 1M表示1米距离
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
4.cmdCheckSigQ.m           ----  根据Train_records.txt清洗数据，便于人工判断当前数据是否为起始点频域干扰、低信噪比等情况。
5.cmdlabelRecByManual.m    ----  按照步骤手动标记，记录格式为文件名称，起始结束位置，中心频率，记录保存到Train_records_byhand.txt中
6.cmdVerifyLabels.m        ----  调用verifylabelsById，输入为数据集库路径，并按照Train_records_byhand.txt验证
7.AnalyzeSNRInSubfolders.m ---- 分析数据集，并生成标注目录下DataAnalysis文件夹、统计分析文件merged_statistics.csv
8.cmdTrainRecords2CSV.m    ----  根据Train_records.txt生成__sorted.csv\__sorted.txt文件，方便查看各个Train_records.txt记录。
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
9.cmdMergeLabledfiles      ----  按照类定义文件class_def.txt，取子文件夹Train_records.txt中的数据合并生成Train_ds_gen.txt、Val_ds_gen.txt等，目前作为特性，已包含在其它功能中

                                                                                          by 刘智国
                                                                                         2025.05.30



