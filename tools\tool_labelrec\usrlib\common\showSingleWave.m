function []=showSingleWave(sfilename,nb_fc,spos,epos,nb_bw)
%  Function    ：showSingleWave
%  Description : 显示单个dat/bvsp文件的频谱
%  Parameter   : file       -- dat文件名称
%  Return      : 显示时频域信号
%
%  Author      : Liuzhiguo
%  Date        : 2024-09-13

if(~exist('sfilename','var'))
    sfilename = ' ';  % 如果未出现该变量，则对其进行赋值
end

if(~exist('nb_fc','var'))
    nb_fc = 0;  % 如果未出现该变量，则对其进行赋值
end
% 1. 初始化命令
%clc
%clear
%close all

%addpath("lib")     %库函数路径
%addpath("usrlib")  %用户自定义路径

% 2. 文件读入, 转化为窄带信号
ischannelized=1;   % 是否信道化
isengine = 1;      % 文件来源是否是引擎

if sfilename==' '
    testcase = 6;
else
    testcase = 0;
end

% nb_bw   = 4e6;      % 窄带信号的带宽
% nb_fs   = 1.2*nb_bw;   % 窄带信号的采样率
if(~exist('nb_bw','var'))
    nb_bw   = 4e6;      % 窄带信号的带宽
    nb_fs   = 1.2*nb_bw;   % 窄带信号的采样率
else
    nb_fs   = 2*nb_bw;   % 窄带信号的采样率
end


if exist(sfilename,"file")>0
    [~,~,ext] = fileparts(sfilename);
    if ext == ".wav"
        [wb_data, wb_fs] = audioread(sfilename);
        wb_signal = wb_data(:,1)+1j*wb_data(:,2);
        wb_fc = 2.4e9;%默认设置值
        wb_bw = 20e6;
    elseif ext == ".hdfv" % 自定义波形文件
        [wb_signal, metadata] = readSignalFromHDF5(sfilename);
        wb_fs = metadata.fs;
        wb_fc = metadata.fc;
        wb_bw = metadata.bw;
    else
        %[nb_rxSig,wb_rxSig] = readCapture(file,ischannelized,nb_bw,nb_fs,nb_fc,isengine);
        [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sfilename);
    end
    if(exist('spos','var')>0 && exist('epos','var')>0)
        wb_signal = wb_signal(spos:epos);  % 如果未出现该变量，则对其进行赋值
    end
    showWBSig(wb_signal, wb_fc, wb_fs,sfilename);%显示宽带信号 - 时频域

    %wb_signal = wb_signal(nstartpos:nendpos);
    if nb_fc==0
        nb_fc = wb_fc;
    else
        [nb_sig_td] = ExtractNBSig(wb_signal, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs);
        showNBSig(testcase, nb_sig_td, nb_fc, nb_fs, sfilename); %显示窄带信号 - 时频域
    end

else
    error("文件：%s 不存在！", sfilename);
    %return;
end

end
