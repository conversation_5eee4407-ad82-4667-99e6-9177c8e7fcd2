%    function   : cmdGenLSW_pair
%    Description: 生成标记数据对 LSW(Labeled Signal Wild)
%    author     : 刘智国
%    Date       : 2024-12-31
% 1. 初始化命令
clc
clear
close all
%可放在命令行中执行
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量

%genLSW_Pair('E:\project\tool_dataset\outdataset\train-files\base\Val_ds_gen_list.txt', 5);

% 采用每类 生成2条相同类别对比；2条不同类别对比
%genLSW_PairByRand('E:\lz_signaldB\datafiles\PreprocessData\2025-04-29\base\Test_ds_gen.txt');
folder_outputDS=myconfig.folder_outputDS;
testfile_path= fullfile(folder_outputDS, 'base','Test_ds_gen.txt');%base/Test_ds_gen.txt'
genLSW_PairByRand(testfile_path);
