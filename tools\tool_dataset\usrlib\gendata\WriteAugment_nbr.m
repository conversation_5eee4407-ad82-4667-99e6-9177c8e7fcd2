function []=WriteAugment_nbr(srcFolder, outpath, maxclip_ms, fname_class,fname_recs)
%  Function    ：WriteAugment_nbr
%  Description : 写入测试数据(采集得到) NB数据
%  Parameter   : fname_rd       -- 写入文件名称
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-27

InitMyParams;
%获取信号路径
% fname_recs = fullfile(srcFolder, "Train_records.txt");

if exist("maxclip_ms","var")<=0
    maxclip_ms = 3;%1.73;%nb433
end

if  maxclip_ms > 10
    maxclip_ms = 10;
end

% 2. 文件读入
% 2.1 文件目录读取
%gencase = 1;%0: 数据方式 1:生成方式


if exist(fname_recs,"file")>0
    fid = fopen(fname_recs, 'r');
    line1 = fgetl(fid);
    fclose(fid);
    
    % 计算分号数量判断格式
    semicolon_count1 = sum(line1 == ';');

    if semicolon_count1 == 2 
        Table_train = readtable(fname_recs,'Format','%s [%d,%d] %f','Delimiter',';');
        Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];

    else
        Table_train = readtable(fname_recs, 'Format', '%s [%d,%d] %f %f', 'Delimiter', ';');
        Table_train.Properties.VariableNames = ["filename", "startpos", "endpos", "fc",'snr'];
       
    end
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    Table_train.lenClipPoints = Table_train.lenPoints;

    %linux下分隔符不同，fileparts出错
    % [parentFolder, ~, ~] = fileparts(string(Table_train.filename)); 
    % Table_train.clsname = parentFolder;
    msp = split(string(Table_train.filename),'\',1);
    Table_train.clsname = msp(1,:)';

    Table_train.recindex = Table_train.lenPoints;%暂时赋值
%--------------------------------------------------------------------
    % numRows = height(Table_train);
    % if numRows >= 15
    %     indices = linspace(1, numRows, 15); % 生成均匀分布的索引
    %     indices = round(indices); % 取整
    %     Table_train = Table_train(indices, :); % 抽取数据
    % end
%--------------------------------------------------------------------
    %Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", fname_recs);
end

if exist(fname_class,"file")>0
    Table_cls = readtable(fname_class,'Format','%d %s %f %f');
    Table_cls.Properties.VariableNames = ["clsid","clsname","BW","Tms"];
else
    error("文件：%s 不存在！", fname_class);
end


%clip为相同长度,为了识别需要
curclsname = ' ';
lenclip = 0;
recindex = 1; %可能增强的条数会多，后面应该加入子标 如:1-12-S1.hdf5, fileid_recindex_subindex,1-12-1-S1.hdf5
nRows = height(Table_train);
for iRow = 1 : nRows
    clsname = Table_train.clsname(iRow);
    if clsname ~= curclsname %下一个类，重新计算长度
        recindex = 1;
        lenclip = Table_train.lenPoints(iRow);
        curclsname = clsname;
    end
    Table_train.recindex(iRow) = recindex;%避免文件名重复
    if recindex==8 %都是同一类最大限制为8
        recindex = 0;
    end
    recindex = recindex+1;
    maxlen = 61.44e3*maxclip_ms;
    if lenclip > maxlen %大于clip_ms，最多取clip_ms
        lenclip = maxlen;
    end

    if Table_train.lenClipPoints(iRow) > lenclip
        Table_train.lenClipPoints(iRow) = lenclip;
    end
end


sOutfilename_list = fullfile(outpath,"wfile_list.txt");
indexPointer_dw = 0;
indexPointer_df = 0;
indexPointer_dp = 0;
%2.2 训练数据文件读取
nRows = height(Table_train);
for iRow = 1 : nRows
    sigfsubname  = char(Table_train.filename(iRow));%文件名称
    [parentFolder, subname, subext] = fileparts(sigfsubname);
    if isempty(parentFolder) %linux system
        sigfsubname = replace(sigfsubname,'\','/');
        [parentFolder, subname, subext] = fileparts(sigfsubname);
    end
    clsname = parentFolder;
    sfilename = strcat(subname, subext);
    cls_id = Table_cls(strcmp(Table_cls.clsname,clsname),:).clsid;% 读取信号所属类别
    cls_BW = Table_cls(strcmp(Table_cls.clsname,clsname),:).BW;
    cls_Tms = Table_cls(strcmp(Table_cls.clsname,clsname),:).Tms;%持续时间 ms

    sigfname         = fullfile(srcFolder, sfilename);    %文件路径
    nStartpos_t      = Table_train.startpos(iRow);      %起始点
    nEndpos_t        = Table_train.endpos(iRow);      %结束点
    fc               = Table_train.fc(iRow);      %中心频率
    lenClipPoints    = Table_train.lenClipPoints(iRow);%clip长度

    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
    lenPointsClass = floor(wb_fs*cls_Tms/1e3);%这类的持续点数
    % if lenClipPoints > lenPointsClass %不能超过该类的点数
    %     lenClipPoints = lenPointsClass;
    % end
    %showWBSig(wb_signal, wb_fc, wb_fs);
    %wb_rxSig_clip = wb_signal(nStartpos_t:nEndpos_t); %数据切片
    wb_rxSig_clip = wb_signal(nStartpos_t:nStartpos_t+lenClipPoints-1); %数据切片

    if mod(length(wb_rxSig_clip),2)>0
        wb_rxSig_clip = wb_rxSig_clip(1:end-1);
    end
    nb_fc = fc;
    nb_bw = cls_BW;      % 从分类中读取窄带带宽
    % (1) 方式1：可变采样率
    %nb_fs   = 2*nb_bw;   % 窄带信号的采样率
    %nb_len_set = 6e4;
    % (2) 方式1：固定采样率
    nb_fs   = 4e6;

    % 数据增强后写入dfc
    nb_fc_list = getfcList(nb_fc,nb_bw);
    nSubIndex = 0;
    for nb_fc_aug = nb_fc_list
        nSubIndex = nSubIndex + 1;
        fprintf("原fc为:%.3f MHz,增强频偏后fc为:%.3f MHz\n",nb_fc/1e6,nb_fc_aug/1e6);
        [ret] = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc_aug, nb_bw, nb_fs);
        if ret ~= 1 %带宽参数无效
            warning("无效带宽输入：%s\n",sigfname);
            continue;
        end
        [nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc_aug, nb_bw, nb_fs);%信道化后数据
        %[nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc_aug, nb_bw, nb_fs, nb_len_set);%信道化后数据
        %showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号
        len_clip_nb = length(nb_signal);
        if len_clip_nb==0
            warning("invalid len_clip_nb=%d\n", len_clip_nb);
            continue;
        end
        len_clip_wb = length(wb_rxSig_clip);
        nb_fs_effct = len_clip_nb*(wb_fs/len_clip_wb);%有效采样率
        % 信号采样率
        fprintf("文件:%s 截取窄带化后信号: 数据点数为%d，带宽:%.2f kHz,持续时间为%.2f ms\r\n", sigfsubname, len_clip_nb, nb_bw/1e3, length(wb_rxSig_clip)*1e3/wb_fs);

        %cls_size = 'S1';indexPointer_df=indexPointer_df+1;
        %[cls_size, indexPointer_bw] = getFileSizeClass(len_clip_nb, cls_size,indexPointer_bw);
        sAugType = '-dfc-';

        curfileindex     = Table_train.recindex(iRow);
        fname_dataset = fullfile(outpath, strcat(subname,'-',int2str(curfileindex),sAugType,int2str(nSubIndex),'.wav'));%合成目的路径
        %subpath = strcat(outpath, clsname(1));%合成目的路径
        %curfileindex     = Table_train(iRow,8).recindex;
        %fname_dataset = strcat(subpath,'\',subname,sAugType,int2str(curfileindex),'-',int2str(nSubIndex),'-',cls_size,'.wav');
        % if exist(subpath,"dir")<=0
        %     mkdir(subpath)
        % end
        indexPointer_df = 1;%每次只生成1条
        scomment = sprintf("nb_bw=%d,fc=%d",nb_bw,fc);%写入audio文件
        audiowrite(fname_dataset, [real(nb_signal) imag(nb_signal)],floor(nb_fs_effct),'BitsPerSample',32,'Title',sigfsubname,'Artist',string(cls_id),'Comment',scomment);

        strline = strcat(int2str(cls_id),";",fname_dataset);
        writelines(strline, sOutfilename_list,'WriteMode','append');
    end

    % 数据增强后写入dbw
    [bw_list] = getdwList(nb_bw);
    nSubIndex = 0;
    for nb_bw_aug = bw_list
        nSubIndex = nSubIndex+1;
        fprintf("原bw为:%.3f kHz,增强频偏后bw为:%.3f kHz\n", nb_bw/1e3, nb_bw_aug/1e3);
        [ret] = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw_aug, nb_fs);
        if ret ~= 1 %带宽参数无效
            warning("无效带宽输入：%s\n",sigfname);
            continue;
        end
        if nb_bw_aug>nb_fs
            warning("bw>4M为无效输入");
            continue;
        end
        [nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw_aug, nb_fs);%信道化后数据
        %[nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw_aug, nb_fs, nb_len_set);%信道化后数据
        %showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号
        len_clip_nb = length(nb_signal);
        if len_clip_nb==0
            warning("invalid len_clip_nb=%d\n", len_clip_nb);
            continue;
        end
        len_clip_wb = length(wb_rxSig_clip);
        nb_fs_effct = len_clip_nb*(wb_fs/len_clip_wb);%有效采样率
        % 信号采样率
        fprintf("文件:%s 截取窄带化后信号: 数据点数为%d，带宽:%.2f kHz,持续时间为%.2f ms\r\n", sigfsubname, len_clip_nb, nb_bw_aug/1e3, length(wb_rxSig_clip)*1e3/wb_fs);

        cls_size = 'S1';indexPointer_dw=indexPointer_dw+1;
        %[cls_size, indexPointer] = getFileSizeClass(len_clip_nb, cls_size,indexPointer);
        sAugType = '-dbw-';
        curfileindex     = Table_train.recindex(iRow);
        fname_dataset = fullfile(outpath, strcat(subname,'-',int2str(curfileindex),sAugType,int2str(nSubIndex),'.wav'));%合成目的路径
        % subpath = strcat(outpath, clsname(1));%合成目的路径
        % curfileindex     = Table_train(iRow,8).recindex;
        % fname_dataset = strcat(subpath,'\',subname,sAugType,int2str(curfileindex),'-',int2str(nSubIndex),'-',cls_size,'.wav');
        % if exist(subpath,"dir")<=0
        %     mkdir(subpath)
        % end
        indexPointer_dw = 1;%每次只生成1条
        scomment = sprintf("nb_bw=%d,fc=%d",nb_bw,fc);%写入audio文件
        audiowrite(fname_dataset, [real(nb_signal) imag(nb_signal)],floor(nb_fs_effct),'BitsPerSample',32,'Title',sigfsubname,'Artist',string(cls_id),'Comment',scomment);


        strline = strcat(int2str(cls_id),";",fname_dataset);
        writelines(strline, sOutfilename_list,'WriteMode','append');
    end

    %数据增强 起始位置dp
    dpos_arrays=myconfig.dpos_arrays;
    nSubIndex = 0;
    for idx = 1:length(dpos_arrays)
        nOffset = dpos_arrays(idx); % (200:400:2000),修改起始位置可以向后移动
        nSubIndex = nSubIndex+1;
        nStartpos_tm = nStartpos_t+nOffset;
        if nStartpos_tm<=1 %已到起始位置
            break;
        end
        %判断是否超出信号长度
        if nStartpos_tm + lenClipPoints > length(wb_signal)
            fprintf('警告: 起始位置+长度超出信号范围，跳过此次循环');
            break;  % 跳过当前循环
        end
        wb_rxSig_clip = wb_signal(nStartpos_tm:nStartpos_tm+lenClipPoints-1); %只改动起始位置
        fprintf("原起始位置:%d, 增强起始位置后为: %d\n", nStartpos_t, nStartpos_tm);
        [nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs);%信道化后数据
        %[nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs, nb_len_set);%信道化后数据
        %showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号
        len_clip_nb = length(nb_signal);
        if len_clip_nb==0
            warning("invalid len_clip_nb=%d\n", len_clip_nb);
            continue;
        end        
        len_clip_wb = length(wb_rxSig_clip);
        nb_fs_effct = len_clip_nb*(wb_fs/len_clip_wb);%有效采样率
        % 信号采样率
        fprintf("文件:%s 截取窄带化后信号: 数据点数为%d，带宽:%.2f kHz,持续时间为%.2f ms\r\n", sigfsubname, len_clip_nb, nb_bw_aug/1e3, length(wb_rxSig_clip)*1e3/wb_fs);

        cls_size = 'S1';indexPointer_dp=indexPointer_dp+1;
        %[cls_size, indexPointer] = getFileSizeClass(len_clip_nb, cls_size,indexPointer);
        sAugType = '-dpos-';

        curfileindex     = Table_train.recindex(iRow);
        fname_dataset = fullfile(outpath, strcat(subname,'-',int2str(curfileindex),sAugType,int2str(nSubIndex),'.wav'));%合成目的路径

        % subpath = strcat(outpath, clsname(1));%合成目的路径
        % curfileindex     = Table_train(iRow,8).recindex;
        % fname_dataset = strcat(subpath,'\',subname,sAugType,int2str(curfileindex),'-',int2str(nSubIndex),'-',cls_size,'.wav');
        % if exist(subpath,"dir")<=0
        %     mkdir(subpath)
        % end
        indexPointer_dp = 1;%每次只生成1条
        scomment = sprintf("nb_bw=%d,fc=%d",nb_bw,fc);%写入audio文件
        audiowrite(fname_dataset, [real(nb_signal) imag(nb_signal)],floor(nb_fs_effct),'BitsPerSample',32,'Title',sigfsubname,'Artist',string(cls_id),'Comment',scomment);

        strline = strcat(int2str(cls_id),";",fname_dataset);
        writelines(strline, sOutfilename_list,'WriteMode','append');
    end

end
% varPoints = Table_train(:,3).endpos-Table_train(:,2).startpos;
% varNames  = Table_train(:,1).filename;
% selpart = {varNames; varPoints};
%3 读取hdf文件信息（测试用）
%DispDatasetRecByChart(fname_dataset,1,"原始");
end


function [clsName,indexout]=getFileSizeClass(len,prev_clsName,index)
%
%
%分割成5个区间:[0,5e4],[5e4,10e4],[10e4,20e4],[20e4,40e4],[40e4,60e4],[60e4, -]
%             对应:S1-S5,S6
%
if len < 6.1e4
    clsName = 'S1';
elseif len < 10e4
    clsName = 'S2';
elseif len < 20e4
    clsName = 'S3';
elseif len < 40e4
    clsName = 'S4';
elseif len < 60e4
    clsName = 'S5';
else
    clsName = 'S6';
end
%新文件，更换index
if index==0%第1个，没有prev_clsName
    prev_clsName = clsName;
end
if prev_clsName==clsName
    indexout = index+1;
else
    indexout = 1;
end
end