# =======================================================================================================================
#   Function    ：mainproc.py
#   Description : 模型预测推理代码
#                 （1）通过usrp读入数据，默认参数可以在parse_args中设置
#                 （2）读入数据，并过channel，解析成 'fchanscan-S1.hdf5'
#                 （3）调用模型，预测分类结果
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import numpy as np
import uhd
from chanlib.usrp_samp import parse_args, multi_usrp_rx, multi_usrp_stream, init_args
from chanlib.func_scansig import proc_wbsig, plot_signal
from usrlib.usrlib import Read_sigfile, write_sigfile, AddArcVector, ViewArcVectorDB
from usrlib.predict_proc import predict_classify_proc, load_classify_model, predict_match_proc, load_match_model, get_match_vector
from usrlib.MyHelper import copyhdfs2folder, SetStaticsPlot, save_array_to_wavfile, save_signal_to_hdf5, read_signal_from_hdf5
from chanlib.calRegionByCounter import proc_data_counter
import threading
from threading import Semaphore
import time
import matplotlib.pyplot as plt
import matplotlib
import os

# 1 创建信号量，初始值为 0，用于两个线程间同步
semaphore = Semaphore(0)
# 创建图形和坐标轴
# 智能配置matplotlib字体，避免Linux下的字体警告
import platform
if platform.system() == 'Linux':
    # Linux系统使用可用的中文字体
    import matplotlib.font_manager as fm
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    font_candidates = [
        'WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC',
        'Source Han Sans SC', 'Droid Sans Fallback', 'Liberation Sans'
    ]
    selected_font = 'DejaVu Sans'  # 默认值
    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            break
    matplotlib.rc("font", family=selected_font)
elif platform.system() == 'Windows':
    matplotlib.rc("font", family='Microsoft YaHei')
else:
    matplotlib.rc("font", family='Liberation Sans')
fig, ax = plt.subplots(figsize=(12, 8))

# 2 全局变量
src_sampdata = 0# 数据源选择 0: usrp采集 1: bvsp或dat文件
num_samp = 0     # 统计采样次数
num_modelcheck = 0 # 模型检测次数
dataset_name ='fchanscan-S1.hdf5'

# 3 模型文件
modeltype = 0 #模型类别 0:分类 1:匹配
if modeltype == 0:
    model_path = "logs/Mtype0-ep100-loss0.006-val_loss0.030.pth" # 05-23 可变长度
    model_path = "logs/Mtype0-ep096-loss0.006-val_loss0.040.pth" #05-26变长度版本，增加数据量
    model_path = "logs/Mtype0-ep071-loss0.006-val_loss0.023.pth" #05-26变长度版本，增加数据量
    #model_path = "logs/Mtype0-ep100-loss0.006-val_loss0.006.pth" # local
    #model_path = "logs/Mtype0-ep099-loss0.006-val_loss0.006.pth" # local+spectrum
    #model_path = "logs/Mtype0-ep100-loss0.008-val_loss0.007-0429.pth" # 0429

    # model_path = "logs/Mtype0-ep100-loss0.008-val_loss0.013-gfft.pth" # add global fft
    # model_path = "logs/Mtype0-ep100-loss0.008-val_loss0.020-gfft1.pth" # add global fft without embed conv
    #model_path = "logs/model0_epoch_100_lt-0.0760_lv-0.0158.pth" # Den jiao jiao model
    Model, cls_count, cls_ids, cls_names = load_classify_model(model_path) # 分类模型
else:
    model_path = "logs/Mtype1-ep087-loss0.000-val_loss0.391.pth" #05-27 匹配模型
    Model, cls_count, cls_ids, cls_names = load_match_model(model_path)    # 匹配模型

all_predicts = np.zeros(cls_count, dtype=int)                #预测发现无人机次数
if src_sampdata==0:
    threshold_classify = np.ones(cls_count, dtype=float)*0.98     #分类每类门槛值
    threshold_match    = np.ones(cls_count, dtype=float)*0.99     #匹配每类门槛值
else:
    threshold_classify = np.ones(cls_count, dtype=float)*0.7     #分类每类门槛值
    threshold_match    = np.ones(cls_count, dtype=float)*0.8     #匹配每类门槛值

# 4 usrp类
usrp = None
# args = init_args(fc=2446.0e6, fs=15.36e6, bw=12e6) #nb_futaba_sfhss
args = init_args(fc=915e6, fs=15.36e6, bw=12e6) # nb_crossfire_gfsk, nb_RFD900X
# args = init_args(fc=433e6, fs=15.36e6, bw=12e6)
# args = init_args(fc=2432.5e6, fs=15.36e6, bw=12e6) 
#args = init_args(fc=2.45e9, fs=10.24e6, bw=8e6)
if src_sampdata==0: #usrp采集
    try:
        usrp = uhd.usrp.MultiUSRP(args.args)
    except Exception as e:
        print(f"USRP 初始化错误: {e}")
        
fname_sample = R"E:\project\tool_dataset\sam_evo\433\ch9364Capture433-5.dat"

def ScanChanSig_once(usrp, src_sampdata, args):
    '''
        单次采集信号数据
    '''
    chan_id = 0
    if src_sampdata==0:
        rxDatas = multi_usrp_rx(usrp, args)
        samples= rxDatas[chan_id, :]
        samples = samples*(2**15-1)
        # samples = multi_usrp_stream(usrp, args)
    elif src_sampdata==1:
        #print(samples.shape)
        #
        #filename_wb = R"E:\lz_signaldB\datafiles\WMisclassifiedData\FalsePositive\2025_05_19\02-20250518-192248-226.hdfv"
        
        name, ext = os.path.splitext(fname_sample)
        if ext == ".dat" or ext == ".bvsp": 
            (rxDatas, args.rate, args.freq, args.bandwidth) = Read_sigfile(fname_sample, [0, -1]) #读入信号
            samples = rxDatas[chan_id,:,0]+1j*rxDatas[chan_id,:,1] #转换为复数
            #samples = samples/(2**15) #对识别结果不构成影响
            #samples = samples*(2**4)
        elif ext == ".hdfv":
            samples, metadata = read_signal_from_hdf5(fname_sample)
            args.rate = metadata['fs']
            args.freq  = metadata['fc']
            args.bandwidth = metadata['bw']

    #处理宽带读入信号
    proc_wbsig(samples, args.rate, args.freq, args.bandwidth)
    #proc_data_counter(samples, args.rate, args.freq, args.bandwidth)
    return samples

# 调试显示Usrp数据
def DebugUsrp_byShow():
    chan_id = 0
    if src_sampdata==0:
        # rxDatas = multi_usrp_rx(usrp, args)
        # 
        # samples = samples*(2**15)
        rxDatas = multi_usrp_stream(usrp, args)
        samples= rxDatas[chan_id, :]
        samples = samples*(2**15-1)
        plot_signal(True, samples)
    
    return samples
    
# 测试保存的数据
def ShowCurPredict(dataset_name = R'saved_hdfs\20250508-145048-813.hdf5'):
    if modeltype==0:
        cur_predicts = predict_classify_proc(Model, cls_count, cls_names, dataset_name, threshold_classify) #调用模型，预测分类结果
    else:
        cur_predicts = predict_match_proc(Model, cls_count, cls_names, dataset_name, threshold_match)    #调用模型，预测匹配结果

    

# 等待信号量，更新显示数据
def update_statPlot():
    global fig, ax
    sExtra = "模型类型:{0} fc={1} MHz, fs={2} M ".format(modeltype, args.freq/1e6, args.rate/1e6)
    while True:
        # 等待信号量
        semaphore.acquire()
        SetStaticsPlot(cls_count, cls_names, all_predicts, num_samp, num_modelcheck, ax, fig, sExtra) #显示统计图表

# 采集及数据处理线程，如发现无人机信号，释放信号量，通知显示线程
def proc_data():
    global all_predicts, num_samp, fname_sample, num_modelcheck
    if src_sampdata==1: 
        bvsp_files = list_bvsp_files(R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample", cls_names)

    nfile_index = 0
    semaphore.release() # 显示初始界面
    while True:
        if src_sampdata==1: 
            fname_sample = bvsp_files[nfile_index]
            print("解析采样文件:{0}\n".format(fname_sample))
            nfile_index = nfile_index + 1

        samples = ScanChanSig_once(usrp, src_sampdata, args) #读入数据，并过channel，解析成 'fchanscan-S1.hdf5'
        num_samp += 1 # 采样次数
        if os.path.exists(dataset_name):#判断生成了hdfs文件
            num_modelcheck += 1 #模型检测次数
            if modeltype==0:
                cur_predicts = predict_classify_proc(Model, cls_count, cls_names, dataset_name, threshold_classify) #调用模型，预测分类结果
            else:
                cur_predicts = predict_match_proc(Model, cls_count, cls_names, dataset_name, threshold_match)    #调用模型，预测匹配结果
            
            if sum(cur_predicts) > 0:#判断有记录
                predict_cls = np.argmax(cur_predicts>0)
                filename_hdfs = copyhdfs2folder(dataset_name, predict_cls)           #保存hdfs文件
                #filename_wav = filename_hdfs.replace("hdf5", "wav")
                #fcomment='bw={0},fc={1}'.format(4e6, args.freq)
                #save_array_to_wavfile(samples, int(args.rate), filename_wav,comment=fcomment) #保存raw数据到wav文件
                filename_wav = filename_hdfs.replace("hdf5", "hdfv") # hdfv为raw文件 
                save_signal_to_hdf5(filename_wav, samples, args.freq, args.rate, args.bandwidth)
                all_predicts += cur_predicts                            #更新预测次数
                # 释放信号量，允许人流量更新任务执行
                semaphore.release()
                time.sleep(1)  

        if src_sampdata==1: 
            if nfile_index == len(bvsp_files):
                break #文件解析过程只执行1次

# 正常main函数处理
def Normal_mainproc():
    """主函数过程"""
    # 启动采集数据线程
    weather_thread = threading.Thread(target=proc_data)
    weather_thread.daemon = True
    weather_thread.start()

    # 启动更新图表显示线程
    updateplot_thread = threading.Thread(target=update_statPlot)
    updateplot_thread.daemon = True
    updateplot_thread.start()

    # 显示图表
    plt.tight_layout()
    plt.show()
    # 打印当前识别结果    
    print("[AI信号识别结果统计] 类别个数: {0} 采集信号次数: {1}  \n无人机类别名称: {2} \n无人机识别个数: {3}".format(cls_count, num_samp, cls_names, all_predicts))
    # while True:
    #     user_input = input("请输入内容（输入 'quit' 退出）: ")
    #     if user_input == 'quit':
    #         break
    #     print(f"你输入的内容是: {user_input}")
    # print("程序已退出。")

def Test_savedhdfs(fname_hdfraw = R"saved_hdfs_2400\07-20250515-102512-664.hdfv"):
    # 1 测试channelscan
    #处理宽带读入信号
    chan_id = 0
    print("解析测试文件:{0}\n".format(fname_hdfraw))
    name, ext = os.path.splitext(fname_hdfraw)
    if ext == ".dat" or ext == ".bvsp": 
        (rxDatas, args.rate, args.freq, args.bandwidth) = Read_sigfile(fname_hdfraw, [0, -1]) #读入信号
        samples = rxDatas[chan_id,:,0]+1j*rxDatas[chan_id,:,1] #转换为复数
        #samples = samples/(2**15) #对识别结果不构成影响
        #samples = samples*(2**4)
    elif ext == ".hdfv":
        samples, metadata = read_signal_from_hdf5(fname_hdfraw)
        args.rate = metadata['fs']
        args.freq  = metadata['fc']
        args.bandwidth = metadata['bw']

    proc_wbsig(samples, args.rate, args.freq, args.bandwidth)

    # 2 测试分类结果
    dataset_name = R'fchanscan-S1.hdf5'
    ShowCurPredict(dataset_name)


def Testpreceison_byfolders(sFolderName=R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample"):
    bvsp_files = list_bvsp_files(sFolderName, cls_names)
    for i in range(len(bvsp_files)):
        fname_sample = bvsp_files[i]
        print("解析采样文件:{0}\n".format(fname_sample))
        Test_savedhdfs(fname_sample)

def Test_mainproc():
    #DebugUsrp_byShow() #调试usrp数据显示用
    #Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample\nb_radiolink_t8f8\7.bvsp")
    #Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample\nb_radiolink_t16d\CJ0_1M_1048.bvsp")
    #Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample\nb_elrs_128x\CJ0_1M_1.bvsp")

    #Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample\nb_frsky_x9d2\CJ0_1M_1.bvsp")
    #Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample\nb_futaba_sfhss\CJ0_1M_1202.bvsp")
    # Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample\nb_crossfire_gfsk\CJ1_10M_1535.bvsp")
    Test_savedhdfs(fname_hdfraw = R"E:\lz_signaldB\datafiles\02-LabeledData\2025-05-23\nb_frsky_tw_fsk\CJ0_1M_1314.bvsp")
    #Testpreceison_byfolders(sFolderName=R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample")
    #AddRecords()


def AddRecords():
    bvsp_files = list_bvsp_files(R"E:\lz_signaldB\datafiles\02-LabeledData\TestSample", cls_names)
    chan_id = 0
    N_rec = len(cls_ids)

    fname = "./config/sig-vectordB.cfg"
    ViewArcVectorDB(fname) #查看向量库
    if os.path.exists(fname):
        os.remove(fname)

    #for fname_bvsp in bvsp_files:
    for i in range(N_rec):
        fname_bvsp = bvsp_files[i]   
        print("[解析文件{0}]:{1}\n".format(i, fname_bvsp))
        (rxDatas, args.rate, args.freq, args.bandwidth) = Read_sigfile(fname_bvsp, [0, -1]) #读入信号
        samples = rxDatas[chan_id,:,0]+1j*rxDatas[chan_id,:,1] #转换为复数    
        proc_wbsig(samples, args.rate, args.freq, args.bandwidth)
        dataset_name = R'fchanscan-S1.hdf5'   
        vectors, afs_value, abw_value = get_match_vector(Model, dataset_name)
        if len(vectors)>0: 
            AddArcVector(vectors[0], cls_ids[i], cls_names[i], fname_bvsp, afs_value[0], abw_value[0])
            
    ViewArcVectorDB(fname) #查看向量库


def init_params():
    #分类
    threshold_classify[0] = 0.9952 # nb_RFD900X
    threshold_classify[1] = 0.99 # nb_crossfire_gfsk
    threshold_classify[2] = 0.96 # nb_crossfire_lora
    threshold_classify[3] = 0.999 # nb_HITEC_FLAH8_2430
    threshold_classify[4] = 0.99 # nb_433M
    threshold_classify[5] = 0.99 # nb_radiolink_t8f8
    threshold_classify[6] = 0.999 # nb_radiolink_t16d

    threshold_classify[7] = 0.999 # nb_elrs_128x
    threshold_classify[8] = 0.999 # nb_frsky_td_fsk
    threshold_classify[9] = 0.999 # nb_frsky_tw_fsk
    threshold_classify[10] = 0.999 # nb_frsky_x9d2
    threshold_classify[11] = 0.993 # nb_futaba_sfhss
    threshold_classify[12] = 0.999 # nb_crossfire_ethix
    #匹配
    threshold_match[0] = 0.98 # nb_RFD900X
    threshold_match[1] = 0.92 # nb_crossfire_gfsk
    threshold_match[2] = 0.9907 # nb_crossfire_lora
    threshold_match[3] = 0.999 # nb_HITEC_FLAH8_2430
    threshold_match[4] = 0.83 # nb_433M

    threshold_match[7] = 0.999 # nb_elrs_128x
    threshold_match[11] = 0.99901 # nb_futaba_sfhss #存在一定问题
    threshold_match[12] = 0.999 # nb_crossfire_ethix

# bvsp文件格式测试
def Debug_bvspfile():
    filename_wb = R"E:\project\tool_dataset\sam_evo\433\ch9364Capture433-5.dat"
    (rxDatas, args.rate, args.freq, args.bandwidth) = Read_sigfile(filename_wb, [0, -1]) #读入信号
    write_sigfile("outsig.bvsp", rxDatas, args.rate, args.freq, args.bandwidth)

def main():
    if src_sampdata==0:
        init_params()
    Normal_mainproc() # 正常过程
    #Test_mainproc()  # 单文件测试过程，方便定位误判的问题

def list_bvsp_files(basepath, clsnames):
    """
    列举指定目录下所有.bvsp扩展名的文件
    
    参数:
    directory (str): 要搜索的目录路径
    
    返回:
    list: 包含.bvsp文件路径的列表
    """
    bvsp_files = []
    
    for clsname in clsnames:#所有类别文件夹
        directory = os.path.join(basepath, clsname)
        # 检查目录是否存在
        if not os.path.exists(directory):
            print(f"错误: 目录 '{directory}' 不存在")
            continue
        
        # # 遍历目录中的所有项目,但会遍历子路径
        # for root, _, files in os.walk(directory):
        #     for file in files:
        #         if file.lower().endswith('.bvsp'):
        #             bvsp_files.append(os.path.join(root, file))

        with os.scandir(directory) as entries: # 不包含子路径
            for entry in entries:
                # 直接通过 entry.is_file() 判断，无需拼接路径
                if entry.is_file() and entry.name.lower().endswith('.bvsp'):
                    bvsp_files.append(entry.path)
    
    return bvsp_files


if __name__ == "__main__":
    main()  
    # (2)matlab中图形化查看hdf文件 (时序图和频谱图)
    # DispDatasetRecByChart("E:\project\prj_predict_usrp\saved_hdfs\20250508-182211-640.hdf5",1,"原始")
    # matlab中 wav文件查看, showSingleWave("E:\project\prj_predict_usrp\saved_hdfs\20250509-153831-471.wav")
    # matlab中 hdfv文件查看, showSingleWave("E:\project\prj_predict_usrp\saved_hdfs\20250512-103959-869.hdfv")