# =======================================================================================================================
#   Function    ：AddSigToDbByTxt.py
#   Description : 添加信号到信号库中，config/sig-vectordB.cfg
#                 可在命令行中输入: python AddSigToDbByTxt.py
#                                 路径文件为 ./../data/vector_ds.txt
#
#   Parameter   : -p 后面为标注文件路径
#   Author      : Liuzhiguo
#   Date        : 2024-1-20
# =======================================================================================================================

from usrlib.usrlib import *
import torch 
from nets.arcface import Arcface
import getopt
import sys

if __name__ == "__main__":
    modeltype = 1 #模型类别 0:分类 1:角向量
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    dataset_file = ""

    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    #文件方式读入，加入
    path_TestSample = R"E:\lz_signaldB\datafiles\LabeledData\TestSample"
    dsdef_file = os.path.join(path_TestSample, 'vector_ds.txt')

    # 命令行方式获取标注文件路径 dsdef_file
    opts,args = getopt.getopt(sys.argv[1:],'-p:',['labelfilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-p','--labelfilepath'):
            dsdef_file = opt_value
            print("The labelfilepath is ", dsdef_file)

    file_subpaths, posset, N_rec = get_dsdata(dsdef_file)

    #1. 路径及模型参数
    model_path = "logs/Mtype1-ep096-loss0.000-val_loss0.000-0521.pth"
    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()

    fname = "./config/sig-vectordB.cfg"
    ViewArcVectorDB(fname) #查看向量库
    if os.path.exists(fname):
        os.remove(fname)

    for i in range(N_rec):
        spos,epos = posset[i].replace('[','').replace(']','').split(',')
        clippos = [int(spos), int(epos)]
        file_subpath = file_subpaths[i]
        data_file = os.path.join(path_TestSample, file_subpath)
        sigdata = Read_sigfile(data_file, clippos)
        print('要解析信号的结构：{0}'.format(sigdata.shape))
        sigdata = torch.from_numpy(sigdata)
        sigdata = sigdata.cuda()
        #4.生成向量,加库
        vec1 = Model(sigdata)
        vec1 = vec1.squeeze()
        clsname = file_subpath.split('\\')[0]
        index = cls_names.index(clsname)
        clsid = cls_ids[index]
        AddArcVector(vec1.detach().cpu().numpy(), int(clsid), clsname, file_subpath)

    ViewArcVectorDB() 