clear;
close all;

%可放在命令行中执行
IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量

%func_scansignal('E:\ftproot\signalDB\nb_Anxiangdongli\1.bvsp',1);
%delete('E:\project\tool_dataset\outdataset\test\sam\fchanscan-S1.hdf5')
%func_scansignal('E:\ftproot\signalDB\nb_Flysky_PL18\5.bvsp',18);
nSigChecked = func_scansignal('ch9364Capture.dat',0);%捕获信号
%% web read
%url = 'http://127.0.0.1:5000/test?fid=2';% fid:
%webresponse = webread(url);
%web(url)
if nSigChecked>0
    web('http://127.0.0.1:5000/classify?fid=2')
end