import torch
from typing import Callable, List, Optional, Union
import torch.nn.functional as F

class VectorSimilarity():
    def __init__(self,similarity_fn: Union[str, Callable] = 'euclidean_distance',
                 topk: int = -1) -> None:
        self.similarity = similarity_fn
        self.topk = topk
    
    @property
    def similarity_fn(self):
        """Returns a function that calculates the similarity."""
        # If self.similarity_way is callable, return it directly
        if isinstance(self.similarity, Callable):
            return self.similarity

        if self.similarity == 'cosine_similarity':
            # a is a tensor with shape (N, C)
            # b is a tensor with shape (M, C)
            # "cosine_similarity" will get the matrix of similarity
            # with shape (N, M).
            # The higher the score is, the more similar is
            return lambda a, b: torch.cosine_similarity(
                a.unsqueeze(1), b.unsqueeze(0), dim=-1)
        elif self.similarity == 'euclidean_distance':
            # a is a tensor with shape (N, C)
            # b is a tensor with shape (M, C)
            # "euclidean_distance" will get the matrix of similarity
            # with shape (N, M).
            # The higher the score is, the more similar is
            # 扩展 A 和 B 的维度以进行广播
            # a (N, C)-->(N, 1, C)
            # b (M, C)-->(1, M, C)
            # 计算差值的平方
            # 沿最后一个维度求和
            # 取平方根得到欧氏距离
            return lambda a, b:  torch.sqrt(torch.sum((F.normalize(a).unsqueeze(1)-F.normalize(b).unsqueeze(0))**2, dim=-1))
            # return lambda a, b: torch.cosine_similarity(
            #     a.unsqueeze(1), b.unsqueeze(0), dim=-1)
        else:
            raise RuntimeError(f'Invalid function "{self.similarity_fn}".')
    
    def matching(self, inputs: torch.Tensor, prototype_vecs: torch.Tensor):
        """Compare the prototype and calculate the similarity.

        Args:
            inputs (torch.Tensor): The input tensor with shape (N, C).
        Returns:
            dict: a dictionary of score and prediction label based on fn.
        """
        sim = self.similarity_fn(inputs, prototype_vecs)
        descending = False
        if self.similarity == 'cosine_similarity':
            descending = True
        elif self.similarity == 'euclidean_distance':
            descending = False
        sorted_sim, indices = torch.sort(sim, descending=descending, dim=-1)
        predictions = dict(
            score=sim, pred_label=indices, pred_score=sorted_sim)
        return predictions    