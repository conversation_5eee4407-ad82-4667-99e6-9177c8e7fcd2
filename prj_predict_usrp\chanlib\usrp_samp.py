# =======================================================================================================================
#   Function    ：usrp_samp.py
#   Description : 
#                 读取usrp采样数据，并调用解析代码
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-07
# =======================================================================================================================
import argparse
import numpy as np
import uhd
from uhd.usrp import dram_utils
from uhd.types import StreamCMD, StreamMode
import argparse
import time
import os
import shutil
from usrlib.MyHelper import save_signal_to_hdf5
from datetime import datetime

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

def parse_args():
    """Parse the command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("-a", "--args", default="", type=str)
    parser.add_argument("-o", "--output-file", type=str, default="sigcap.dat")
    parser.add_argument("-f", "--freq", type=float, default=910e6)
    parser.add_argument("-r", "--rate", default=30.72e6, type=float)
    parser.add_argument("-b", "--bandwidth", default=20e6, type=float)
    # parser.add_argument("-r", "--rate", default=61.44e6, type=float)
    # parser.add_argument("-b", "--bandwidth", default=56e6, type=float)
    parser.add_argument("-d", "--duration", default=10e-3, type=float)
    parser.add_argument("-c", "--channel", default=0, nargs="+", type=int)
    parser.add_argument("-g", "--gain", type=int, default=30)
    parser.add_argument("-n", "--numpy", default=False, action="store_true",
                        help="Save output file in NumPy format (default: No)")
    parser.add_argument("--dram", action='store_true',default=False,
                        help="If given, will attempt to stream via DRAM")
    return parser.parse_args()

def init_args(fc=2.4e9,fs=30.72e6, bw=20e6, duration=10e-3, gain=30):
    """Parse the command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("-a", "--args", default="", type=str)
    parser.add_argument("-o", "--output-file", type=str, default="sigcap.dat")
    parser.add_argument("-f", "--freq", type=float, default=fc)
    parser.add_argument("-r", "--rate", type=float, default=fs)
    parser.add_argument("-b", "--bandwidth", type=float, default=bw)
    parser.add_argument("-d", "--duration", type=float, default=duration)
    parser.add_argument("-c", "--channel", default=0, nargs="+", type=int)
    parser.add_argument("-g", "--gain", type=int, default=gain)
    parser.add_argument("-n", "--numpy", default=False, action="store_true",
                        help="Save output file in NumPy format (default: No)")
    parser.add_argument("--dram", action='store_true',default=False,
                        help="If given, will attempt to stream via DRAM")
    return parser.parse_args()
    
def multi_usrp_rx(usrp, args):
    """
    multi_usrp based RX example
    """
    num_samps = int(np.ceil(args.duration*args.rate))
    if not isinstance(args.channel, list):
        args.channel = [args.channel]
    samps = usrp.recv_num_samps(num_samps, args.freq, args.rate, args.channel, args.gain)

    #samps = usrp.recv_num_samps(num_samps, args.freq, args.rate, args.channel, args.gain)
    # with open(args.output_file, 'wb') as out_file:
    #     if args.numpy:
    #         np.save(out_file, samps, allow_pickle=False, fix_imports=False)
    #     else:
    #         samps.tofile(out_file)
    return samps


def manage_folder(foldername):
    """检查文件夹是否存在，存在则清空，不存在则创建"""
    try:
        # 检查文件夹是否存在
        if os.path.exists(foldername):
            # 确认是否为目录
            if os.path.isdir(foldername):
                # 清空文件夹中的所有内容
                for item in os.listdir(foldername):
                    item_path = os.path.join(foldername, item)
                    if os.path.isfile(item_path):
                        os.remove(item_path)
                    elif os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                print(f"文件夹 '{foldername}' 已清空。")
            else:
                print(f"错误：'{foldername}' 存在但不是文件夹！")
                return False
        else:
            # 创建新文件夹
            os.makedirs(foldername)
            print(f"文件夹 '{foldername}' 已创建。")
        return True
    except Exception as e:
        print(f"操作失败：{str(e)}")
        return False
    
def samp_data2folder(folder_path, fc_set=2446.0e6, progress_callback=None, existing_usrp=None):
    """
    采样数据到特定文件夹下
    
    Args:
        folder_path: 保存数据的文件夹路径
        fc_set: 中心频率，默认为2446.0e6
        progress_callback: 进度回调函数，接受三个参数：当前文件索引、总文件数、状态消息
        existing_usrp: 已初始化的USRP设备对象，如果为None则会创建新的设备对象
    """
    bret = manage_folder(folder_path)
    if bret == False:
        if progress_callback:
            progress_callback(0, 10, "文件夹准备失败")
        return
    
    args = init_args(fc=fc_set, fs=15.36e6, bw=12e6, duration=20e-3) #nb_futaba_sfhss
    
    # 使用已初始化的USRP设备或创建新的设备
    if existing_usrp:
        usrp = existing_usrp
        if progress_callback:
            progress_callback(1, 10, "使用已初始化的USRP设备")
    else:
        # 调用usrp
        try:
            if progress_callback:
                progress_callback(1, 10, "正在初始化USRP设备...")
            usrp = uhd.usrp.MultiUSRP(args.args)
            if progress_callback:
                progress_callback(2, 10, "USRP设备初始化成功")
        except Exception as e:
            error_msg = f"USRP 初始化错误: {e}"
            print(error_msg)
            if progress_callback:
                progress_callback(0, 10, error_msg)
            return
    
    total_files = 10  # 采集10个数据包
    
    for i in range(0, total_files):
        # 更新进度信息
        if progress_callback:
            status_message = f"正在采集数据包 {i+1}/{total_files}..."
            progress_callback(i+1, total_files, status_message)
        
        now = datetime.now()
        current_time = "{:02d}-".format(i)+now.strftime("%Y%m%d-%H%M%S") #次数+时间
        new_file_name = f"{current_time}.hdfv"
        filename_wav = os.path.join(folder_path, new_file_name)

        try:
            rxDatas = multi_usrp_rx(usrp, args) # 采集数据
            chan_id = 0
            samples= rxDatas[chan_id, :]
            samples = samples*(2**15-1)
            save_signal_to_hdf5(filename_wav, samples, args.freq, args.rate, args.bandwidth) # 保存文件
        except Exception as e:
            error_msg = f"数据采集错误: {e}"
            print(error_msg)
            if progress_callback:
                progress_callback(i+1, total_files, error_msg)
            # 继续尝试采集下一个数据包
    
    # 完成所有文件的采集
    if progress_callback:
        progress_callback(total_files, total_files, "数据采集完成")
    
    return total_files  # 返回采集的文件数量


def multi_usrp_stream(usrp, args):
    """
    multi_usrp based RX example
    """
    # Set the USRP rate, freq, and gain
    usrp.set_rx_rate(args.rate, args.channel)
    usrp.set_rx_freq(uhd.types.TuneRequest(args.freq), args.channel)
    usrp.set_rx_gain(args.gain, args.channel)

    #usrp.set_error_handler_threshold(8)

    # Create the buffer to recv samples
    num_samps = int(np.ceil(args.duration*args.rate)) #max(args.nsamps, width)
    samples = np.empty((1, num_samps), dtype=np.complex64)

    # 把 USRP 设备配置成以 16 位有符号整数复数格式（sc16）在设备和计算机之间传输数据，
    # 而在 CPU 端以 32 位浮点数复数格式（fc32）处理数据。这样做既能保证数据传输的高效性，又能让 CPU 方便地进行数字信号处理。
    st_args = uhd.usrp.StreamArgs("fc32", "sc16")
    #st_args = uhd.usrp.StreamArgs("sc16", "sc16") #报错
    st_args.channels = [args.channel]

    # 在接收数据时，USRP 设备不仅会提供实际的信号样本，还会附带一些关于这些样本的状态信息，
    # 这些信息就会被存储在 RXMetadata 对象中。
    metadata = uhd.types.RXMetadata()
    streamer = usrp.get_rx_stream(st_args)
    buffer_samps = streamer.get_max_num_samps()
    recv_buffer = np.zeros((1, buffer_samps), dtype=np.complex64)
    # 配置接收参数
    #recv_buffer = uhd.types.create_recv_buffer(streamer.get_max_num_samps(), 1) #module 'uhd.types' has no attribute 'create_recv_buffer' 
    #函数通常用于创建一个适合存储从 USRP 设备接收到的数据的缓冲区。其参数一般表示缓冲区的维度信息，具体参数含义可能因 UHD 库的实现细节有所不同，
    # 但通常第一个参数表示样本数量，第二个参数表示通道数。

    # 开始接收数据
    stream_cmd = uhd.types.StreamCMD(uhd.types.StreamMode.start_cont)
    stream_cmd.stream_now = True
    streamer.issue_stream_cmd(stream_cmd)

    # Receive the samples
    recv_samps = 0
    nCount = 0
    while recv_samps < num_samps:
        samps = streamer.recv(recv_buffer, metadata)

        if metadata.error_code != uhd.types.RXMetadataErrorCode.none:#OERROR_CODE_OVERFLOW
            print(metadata.strerror())
        # elif metadata.error_code != uhd.types.OverflowError:
        #     print(metadata.strerror())

        if samps:
            real_samps = min(num_samps - recv_samps, samps)
            samples[:, recv_samps:recv_samps + real_samps] = recv_buffer[:, 0:real_samps]
            recv_samps += real_samps
            nCount += 1
    
    # 等待一段时间
    #time.sleep(1)

    # 创建停止流的命令
    stop_cmd = uhd.types.StreamCMD(uhd.types.StreamMode.stop_cont)
    streamer.issue_stream_cmd(stop_cmd)

    #print(nCount)
    return samples

