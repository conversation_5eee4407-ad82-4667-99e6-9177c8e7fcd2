import os

import numpy as np
import torch
import torch.utils.data as data
import torchvision.datasets as datasets
from PIL import Image
import h5py
import soundfile
from .utils import cvtColor, preprocess_input, resize_image


class FacenetDataset(data.Dataset):
    def __init__(self, input_shape, lines, random):
        self.input_shape    = input_shape
        self.lines          = lines
        self.random         = random

    def __len__(self):
        return len(self.lines)

    def rand(self, a=0, b=1):
        return np.random.rand()*(b-a) + a

    def __getitem__(self, index):
        annotation_path = self.lines[index].split(';')[1].split()[0]
        y               = int(self.lines[index].split(';')[0])

        image = cvtColor(Image.open(annotation_path))
        #------------------------------------------#
        #   翻转图像
        #------------------------------------------#
        if self.rand()<.5 and self.random: 
            image = image.transpose(Image.FLIP_LEFT_RIGHT)
        image = resize_image(image, [self.input_shape[1], self.input_shape[0]], letterbox_image = True)

        image = np.transpose(preprocess_input(np.array(image, dtype='float32')), (2, 0, 1))
        return image, y

def LoadHdfsDataset(dataset_file):
    '''
    加载单个hdfs数据集
    '''
    if os.path.exists(dataset_file)==False:
        print("{0}:文件不存在".format(dataset_file))
        return
    
    #LenNorm = 220000 #3ms for 61.44M
    LenNorm = 1024*58+512# 应该<=48000 #narrow band,10ms for 4M

    f = h5py.File(dataset_file, 'r')
    rx_signals  = f['rx_signal'][:]
    class_ids   = f['class_id'][:]
    class_names = f['class_name'][:]
    fs_values = f['fs'][:]
    bw_values = f['bw'][:]
    f.close()

    # 裁剪数据集的size
    N_row =  rx_signals.shape[0]
    N_col =  rx_signals.shape[1]
    if N_col<LenNorm:
        rx_signals = np.pad(rx_signals,((0,0),(0,LenNorm-N_col),(0,0)), 'constant')
    else:
        rx_signals = np.resize(rx_signals,(N_row, LenNorm, 2))
    
    # print('rx_signal:', rx_signals.shape, rx_signals.dtype)
    # print('class_id:', class_ids.shape, class_ids.dtype)
    return  rx_signals, class_ids, class_names, fs_values, bw_values


class SigNbDataset(data.Dataset):
    def __init__(self, lines):
        self.lines       = lines

    def __len__(self):
        return len(self.lines)

    def __getitem__(self, index):
        annotation_path = self.lines[index].split(';')[1].split()[0]
        label               = int(self.lines[index].split(';')[0])
        
        #rx_signal, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(annotation_path)
        rx_signal, fs_value = soundfile.read(annotation_path, dtype='float32')#(60000, 2)
        LenNorm = 1024*58+512
        rx_signal = rx_signal[0:LenNorm,:]
        if rx_signal.shape[0]!=LenNorm or rx_signal.shape[1]!=2 or len(rx_signal.shape)!=2: #(1, 59392, 2)
             print(fs_value)

        # if label != class_id:
        #     print(fs_value)    
        return rx_signal, fs_value, label

def SigNbDataset_collate(batch):
    rx_signals  = []
    fs_values  = []
    targets = []
    for rx_signal, fs_value, label in batch:
        rx_signals.append(rx_signal)
        fs_values.append(fs_value)
        targets.append(label)

    rx_signals  = torch.tensor(np.array(rx_signals)).type(torch.FloatTensor)#torch.from_numpy(np.array(rx_signals))#.type(torch.FloatTensor)
    fs_values  = torch.tensor(np.array(fs_values)).type(torch.FloatTensor)
    targets = torch.tensor(np.array(targets)).long()#torch.from_numpy(np.array(targets)).long()

    #targets = torch.cat(targets, 0)
    return rx_signals, fs_values, targets
    
def dataset_collate(batch):
    images  = []
    targets = []
    for image, y in batch:
        images.append(image)
        targets.append(y)
    images  = torch.from_numpy(np.array(images)).type(torch.FloatTensor)
    targets = torch.from_numpy(np.array(targets)).long()
    return images, targets

#labeled signal wild
class LSWDataset(data.Dataset):
    def __init__(self, lines_lsw):
        self.lines_lsw = lines_lsw 

    def __getitem__(self, index):
        line    = self.lines_lsw[index].replace("\n", "")#移除换行符
        (id1, path_1, id2, path_2) = line.split(';')
        issame = (id1==id2)
        #rx_signal1, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_1)
        #rx_signal2, class_id, class_name, fs_value, bw_value = LoadHdfsDataset(path_2)
        rx_signal1, fs_value1 = soundfile.read(path_1, dtype='float32')
        rx_signal2, fs_value2 = soundfile.read(path_2, dtype='float32')
        LenNorm = 1024*58+512
        rx_signal1 = rx_signal1[0:LenNorm,:]
        rx_signal2 = rx_signal2[0:LenNorm,:]

        return rx_signal1, fs_value1, rx_signal2, fs_value2, issame

    def __len__(self):
        return len(self.lines_lsw)

class LFWDataset(datasets.ImageFolder):
    def __init__(self, dir, pairs_path, image_size, transform=None):
        #super(LFWDataset, self).__init__(dir,transform)
        self.image_size = image_size
        self.pairs_path = pairs_path
        self.validation_images = self.get_lfw_paths(dir)

    def read_lfw_pairs(self,pairs_filename):
        pairs = []
        with open(pairs_filename, 'r') as f:
            for line in f.readlines()[1:]:
                pair = line.strip().split()
                pairs.append(pair)
        return pairs#np.array(pairs)

    def get_lfw_paths(self,lfw_dir,file_ext="jpg"):

        pairs = self.read_lfw_pairs(self.pairs_path)

        nrof_skipped_pairs = 0
        path_list = []
        issame_list = []

        for i in range(len(pairs)):
        #for pair in pairs:
            pair = pairs[i]
            if len(pair) == 3:
                path0 = os.path.join(lfw_dir, pair[0], pair[0] + '_' + '%04d' % int(pair[1])+'.'+file_ext)
                path1 = os.path.join(lfw_dir, pair[0], pair[0] + '_' + '%04d' % int(pair[2])+'.'+file_ext)
                issame = True
            elif len(pair) == 4:
                path0 = os.path.join(lfw_dir, pair[0], pair[0] + '_' + '%04d' % int(pair[1])+'.'+file_ext)
                path1 = os.path.join(lfw_dir, pair[2], pair[2] + '_' + '%04d' % int(pair[3])+'.'+file_ext)
                issame = False
            if os.path.exists(path0) and os.path.exists(path1):    # Only add the pair if both paths exist
                path_list.append((path0,path1,issame))
                issame_list.append(issame)
            else:
                nrof_skipped_pairs += 1
        if nrof_skipped_pairs>0:
            print('Skipped %d image pairs' % nrof_skipped_pairs)

        return path_list

    def __getitem__(self, index):
        (path_1, path_2, issame)    = self.validation_images[index]
        image1, image2              = Image.open(path_1), Image.open(path_2)

        image1 = resize_image(image1, [self.image_size[1], self.image_size[0]], letterbox_image = True)
        image2 = resize_image(image2, [self.image_size[1], self.image_size[0]], letterbox_image = True)
        
        image1, image2 = np.transpose(preprocess_input(np.array(image1, np.float32)),[2, 0, 1]), np.transpose(preprocess_input(np.array(image2, np.float32)),[2, 0, 1])

        return image1, image2, issame

    def __len__(self):
        return len(self.validation_images)
