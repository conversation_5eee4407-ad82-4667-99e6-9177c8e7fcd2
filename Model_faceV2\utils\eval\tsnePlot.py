'''
 #todo: 这个文件用于将分类模型和匹配模型进行降维映射至低维空间展示为什么二者出现较大的性能差异
 function：
 -> 因为最后匹配采用的是线性相关，所以先尝试PCA降维的方式线性投影至低维

'''

import numpy as np
import torch
import matplotlib

from nets.arcface import Arcface
from usrlib.usrlib import Init_model

#matplotlib.use('TkAgg')  # 在导入pyplot之前设置后端
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.preprocessing import StandardScaler
import seaborn as sns
from sklearn.decomposition import PCA

def plot_features_tsne(reference_features, test_features, reference_labels, test_labels, class_names, perplexity=30):
    """
    将特征降维并可视化
    
    Args:
        reference_features: 参考特征 [N_ref, dim]
        test_features: 测试特征 [N_test, dim]
        reference_labels: 参考标签 [N_ref]
        test_labels: 测试标签 [N_test]
        class_names: 类别名称列表
        perplexity: t-SNE的perplexity参数
    """
    # 合并特征和标签
    all_features = np.vstack([reference_features, test_features])
    all_labels = np.concatenate([reference_labels, test_labels])
    
    # 标准化
    scaler = StandardScaler()
    all_features_scaled = scaler.fit_transform(all_features)
    
    # t-SNE降维
    tsne = TSNE(n_components=2, perplexity=perplexity, random_state=42)
    features_2d = tsne.fit_transform(all_features_scaled)
    
    # 分离参考和测试数据
    n_ref = len(reference_features)
    ref_2d = features_2d[:n_ref]
    test_2d = features_2d[n_ref:]
    
    # 设置颜色映射
    unique_labels = np.unique(all_labels)
    colors = sns.color_palette("husl", n_colors=len(unique_labels))
    color_map = dict(zip(unique_labels, colors))
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 绘制测试数据点
    for label in unique_labels:
        mask = test_labels == label
        plt.scatter(test_2d[mask, 0], test_2d[mask, 1], 
                   c=[color_map[label]], 
                   label=f'Test {class_names[label]}',
                   alpha=0.3,
                   marker='o')
    
    # 绘制参考数据点
    for label in unique_labels:
        mask = reference_labels == label
        plt.scatter(ref_2d[mask, 0], ref_2d[mask, 1], 
                   c=[color_map[label]], 
                   label=f'Ref {class_names[label]}',
                   alpha=1.0,
                   marker='*',
                   s=200)  # 增大参考点的大小
    
    plt.title('t-SNE Visualization of Features')
    plt.xlabel('t-SNE 1')
    plt.ylabel('t-SNE 2')
    
    # 添加图例
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys(), 
              bbox_to_anchor=(1.05, 1), 
              loc='upper left')
    plt.tight_layout()
    plt.show()

def plot_features_pca(reference_features, test_features, reference_labels, test_labels, class_names, n_components=2):
    """
    使用PCA将特征降维并可视化
    
    Args:
        reference_features: 参考特征 [N_ref, dim]
        test_features: 测试特征 [N_test, dim]
        reference_labels: 参考标签 [N_ref]
        test_labels: 测试标签 [N_test]
        class_names: 类别名称列表
        n_components: PCA降维后的维度
    """
    # 合并特征和标签
    reference_labels = np.array(reference_labels).astype(int)
    test_labels = np.array(test_labels).astype(int)
    all_features = np.vstack([reference_features, test_features])  # 前面N_ref个为
    all_labels = np.concatenate([reference_labels, test_labels])
    
    # 标准化
    scaler = StandardScaler()
    all_features_scaled = scaler.fit_transform(all_features)
    # PCA降维
    pca = PCA(n_components=n_components)
    features_2d = pca.fit_transform(all_features_scaled)
    # 计算解释方差比
    explained_variance_ratio = pca.explained_variance_ratio_
    cumulative_variance_ratio = np.cumsum(explained_variance_ratio)
    class_means, class_covs, class_vars = compute_class_stats(all_features, all_labels, reference_labels)
    thresholds = compute_thresholds(class_vars, base=0.8, alpha=1, beta=0.2)

    for cid in reference_labels:
        print(f"类别{cid}：PCA均值方差={class_vars[cid]:.4f}，阈值={thresholds[cid]:.4f}")
    # 分离参考和测试数据
    n_ref = len(reference_features)
    ref_2d = features_2d[:n_ref]
    test_2d = features_2d[n_ref:]
    
    # 设置颜色和marker映射
    unique_labels = np.unique(all_labels)
    colors = sns.color_palette("husl", n_colors=len(unique_labels))
    color_map = dict(zip(unique_labels, colors))
    # 25种不同的marker
    markers = [
        'o',    # 圆形
        's',    # 方形
        '^',    # 三角形
        'D',    # 菱形
        'v',    # 倒三角形
        '<',    # 左三角形
        '>',    # 右三角形
        'p',    # 五角星
        '*',    # 星形
        'h',    # 六边形
        'H',    # 六边形2
        '+',    # 加号
        'x',    # 叉号
        'd',    # 小菱形
        '|',    # 竖线
        '_',    # 横线
        '1',    # 三叉形1
        '2',    # 三叉形2
        '3',    # 三叉形3
        '4',    # 三叉形4
        '8',    # 八边形
        'P',    # 加号
        'X',    # 叉号
        'D',    # 菱形
        'd'     # 小菱形
    ]
    
    # 确保marker数量足够
    if len(unique_labels) > len(markers):
        print(f"警告：类别数量({len(unique_labels)})超过marker数量({len(markers)})，将循环使用marker")
        markers = markers * (len(unique_labels) // len(markers) + 1)
    
    marker_map = dict(zip(unique_labels, markers[:len(unique_labels)]))
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 绘制测试数据点
    for label in unique_labels:
        mask = test_labels == label
        plt.scatter(test_2d[mask, 0], test_2d[mask, 1], 
                   c=[color_map[label]], 
                   label=f'Test {class_names[int(label)]}',
                   alpha=1,
                   marker=marker_map[label])  # 使用对应的marker
    
    # 绘制参考数据点
    for label in unique_labels:
        mask = reference_labels == label
        plt.scatter(ref_2d[mask, 0], ref_2d[mask, 1], 
                   c=[color_map[label]], 
                   label=f'Ref {class_names[int(label)]}',
                   alpha=0.3,
                   marker=marker_map[label],  # 使用相同的marker
                   s=200)  # 增大参考点的大小
    
    # 添加标题和轴标签
    plt.title(f'PCA Visualization of Features\n'
             f'Explained variance ratio: {explained_variance_ratio[0]:.2f}, {explained_variance_ratio[1]:.2f}\n'
             f'Cumulative variance ratio: {cumulative_variance_ratio[1]:.2f}')
    plt.xlabel(f'PC1 ({explained_variance_ratio[0]:.2%} variance)')
    plt.ylabel(f'PC2 ({explained_variance_ratio[1]:.2%} variance)')
    
    # 添加图例
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys(), 
              bbox_to_anchor=(1.05, 1), 
              loc='upper left',
               ncol = 2)
    plt.tight_layout()
    plt.show()
    return pca, scaler  #note:返回这个PCA模型 （先尝试分类模型和匹配模型使用相同的降维模型）


def compute_class_stats(features_pca, labels, class_ids):
    """
    计算每个类别的均值和协方差矩阵
    Args:
        features_pca: [N, k]，PCA降维后的特征
        labels: [N,]，类别标签（已映射为0~C-1）
        class_ids: 类别ID列表（如[0,1,2,...]）
    Returns:
        class_means: dict, {class_id: 均值向量}
        class_covs: dict, {class_id: 协方差矩阵}
        class_vars: dict, {class_id: 方差（主成分方向的均值）}
    """
    class_means = {}
    class_covs = {}
    class_vars = {}
    for cid in class_ids:
        idx = (labels == cid)
        feats = features_pca[idx]
        mean = np.mean(feats, axis=0)
        cov = np.cov(feats, rowvar=False)
        if len(cov.shape)==0:
            var = cov
        else:
            var = np.mean(np.diag(cov))  # 主成分方向的均值方差
        class_means[cid] = mean
        class_covs[cid] = cov
        class_vars[cid] = var
    return class_means, class_covs, class_vars


def compute_thresholds(class_vars, base=0.9, alpha=1, beta=20):
    """
    根据公式为每一类别设置阈值、
    根据PCA方差自适应调整匹配阈值
    Args:
        class_vars: dict, {class_id: 方差}
        base: 全局基础阈值
        alpha, beta: 权重系数
    Returns:
        thresholds: dict, {class_id: 阈值}
    """
    thresholds = {}
    for cid, var in class_vars.items():
        thresholds[cid] = alpha * base + beta * var
    return thresholds

def plot_features_pcaForClassify(predicted_features, true_labels, class_names, n_components=3):
    """
    使用PCA将特征降维并可视化
    
    Args:
        predicted_features: 预测特征 [N_pred, dim]
        true_labels: 真实标签 [N_pred]
        class_names: 类别名称列表
        n_components: PCA降维后的维度
    """
    # 确保输入为numpy数组
    predicted_features = np.array(predicted_features.cpu())
    true_labels = np.array(true_labels).astype(int)
    
    print(f"开始PCA分析...")
    print(f"原始特征维度: {predicted_features.shape}")
    print(f"类别数量: {len(np.unique(true_labels))}")
    # 标准化
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(predicted_features)
    
    # PCA降维
    pca = PCA(n_components=n_components)
    features_3d = pca.fit_transform(features_scaled)
    
    # 计算解释方差比
    explained_variance_ratio = pca.explained_variance_ratio_
    cumulative_variance_ratio = np.cumsum(explained_variance_ratio)
    
    print(f"PCA降维完成:")
    print(f"第一主成分解释方差比: {explained_variance_ratio[0]:.2%}")
    print(f"第二主成分解释方差比: {explained_variance_ratio[1]:.2%}")
    print(f"第三主成分解释方差比: {explained_variance_ratio[2]:.2%}")
    print(f"累积解释方差比: {cumulative_variance_ratio[2]:.2%}")
    # 获取实际出现的类别
    unique_labels = np.unique(true_labels)

    # 计算类别统计信息
    class_means, class_covs, class_vars = compute_class_stats(features_3d, true_labels, unique_labels)

    # 设置颜色和marker映射
    colors = sns.color_palette("husl", n_colors=len(unique_labels))
    color_map = dict(zip(unique_labels, colors))
    
    # 25种不同的marker
    markers = [
        'o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h',
        'H', '+', 'x', 'd', '|', '_', '1', '2', '3', '4',
        '8', 'P', 'X', 'D', 'd'
    ]
    
    # 确保marker数量足够
    if len(unique_labels) > len(markers):
        print(f"警告：类别数量({len(unique_labels)})超过marker数量({len(markers)})，将循环使用marker")
        markers = markers * (len(unique_labels) // len(markers) + 1)
    
    marker_map = dict(zip(unique_labels, markers[:len(unique_labels)]))
    
    # 创建3D图形
    fig = plt.figure(figsize=(16, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制每个类别的数据点
    for label in unique_labels:
        mask = true_labels == label
        class_features = features_3d[mask]
        
        # 获取类别名称
        if isinstance(class_names, list) and label < len(class_names):
            class_name = class_names[label]
        else:
            class_name = f"Class_{label}"
        
        # 绘制数据点
        ax.scatter(class_features[:, 0], class_features[:, 1], class_features[:, 2],
                   c=[color_map[label]], 
                   label=f'{class_name} (n={len(class_features)})',
                   alpha=0.7,
                   marker=marker_map[label],
                   s=60)
        
        # 绘制类别中心点
        center = class_means[label]
        ax.scatter(center[0], center[1], center[2],
                   c='black', 
                   marker=marker_map[label],
                   s=200, 
                   alpha=1.0,
                   edgecolors='white',
                   linewidths=2)
        
        # 添加类别中心标注
        ax.text(center[0], center[1], center[2], f'{class_name}_center',
                fontsize=8, alpha=0.8)
    ax.view_init(elev=20, azim=0)  # elev: 仰角，azim: 方位角
    # 添加标题和轴标签
    ax.set_title(f'3D PCA Visualization of Features (Classify Model)\n'
                f'PC1: {explained_variance_ratio[0]:.2%}, PC2: {explained_variance_ratio[1]:.2%}, PC3: {explained_variance_ratio[2]:.2%}\n'
                f'Cumulative variance: {cumulative_variance_ratio[2]:.2%}')
    ax.set_xlabel(f'PC1 ({explained_variance_ratio[0]:.2%} variance)')
    ax.set_ylabel(f'PC2 ({explained_variance_ratio[1]:.2%} variance)')
    ax.set_zlabel(f'PC3 ({explained_variance_ratio[2]:.2%} variance)')
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 添加图例
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.savefig('./confusion_analysis_results/pca_3d-2.png', dpi=300, bbox_inches='tight')
    plt.show()
    # 打印类别统计信息
    print(f"\n类别统计信息:")
    print("-" * 60)
    for label in unique_labels:
        if isinstance(class_names, list) and label < len(class_names):
            class_name = class_names[label]
        else:
            class_name = f"Class_{label}"
        
        variance = class_vars[label]
        sample_count = np.sum(true_labels == label)
        print(f"{class_name:<25} 样本数: {sample_count:4d}, PCA方差: {variance:.4f}")
    
    return pca, scaler  # 返回这个PCA模型

