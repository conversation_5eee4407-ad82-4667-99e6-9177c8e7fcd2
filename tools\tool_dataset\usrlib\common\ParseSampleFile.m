function [] = ParseSampleFile(sDatFile,sGenDsfile)
%  Function    ：ParseSampleFile
%  Description : 解析单个dat/bvsp文件的频谱
%  Parameter   : sDatFile       -- dat文件名称
%  Return      : 解析频谱信息
%
%  Author      : Liuzhiguo
%  Date        : 2024-09-24
if exist(sDatFile,"file")>0
    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sDatFile);%加载文件

    [sigparams] = energydetection(wb_signal,wb_fs,wb_fc);%探测信息

    spath=fullfile(sDatFile);
    sSub = split(spath,'\');
    ncount = length(sSub);
    %写入标注文件
    fid = fopen(sGenDsfile,"wt+");%"at+" 
    for i =1 : length(sigparams)
        sLine = sprintf('%s\\%s;[%d, %d];%0.3e\n',cell2mat(sSub(ncount-1)), ...
            cell2mat(sSub(ncount)), sigparams(i).beginPoint, ...
            sigparams(i).endPoint,sigparams(i).freq_point*1e6);

        disp('文件类别标识:');
        disp(sLine); 
        fwrite(fid, sLine);
    end
    fclose(fid);
else
    error("文件：%s 不存在！", sDatFile);
    %return;
end
end

