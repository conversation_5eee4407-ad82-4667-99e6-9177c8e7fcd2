# =======================================================================================================================
#   Function    ：modelDesign.py
#   Description : 深度网络模型代码
#                 核心模块：DroneSig_classifier
# 
#   Parameter   :     x - 输入波形信号[batchsize，NPoints，I/Q] 
#                     ==》 sft变换后[batch, fft_size, timeframe, I/Q] 
#                     ==》 分类网络
#                     ==》 1280分类节点 （fc网络层） --- 分类类别数（1000）
#
#   Author      : Liuzhiguo
#   Date        : 2024-08-13
# =======================================================================================================================
import torch
import torch.nn as nn
from axial_attention import AxialImageTransformer, AxialAttention, AxialPositionalEmbedding
import librosa
import numpy as np
import matplotlib.pyplot as plt
import torch.nn.functional as F
from torch.nn import Module, Parameter
import math
from net.Module_attentions import CBAM,  SE, DAM

class ResBlock(nn.Module):
    '''
    残差网络模块 2个 （norm + gelu+ conv(3,3) 模块） + 残差 
    '''
    def __init__(self, channel_list, H, W, **kwargs):
        super(ResBlock, self).__init__(**kwargs)
        self.channel_list = channel_list
        self._conv_1 = nn.Conv2d(self.channel_list[0], self.channel_list[1], kernel_size=[3,3], padding='same')
        self._layer_norm_1 = nn.LayerNorm([self.channel_list[1], H, W])
        self._conv_2 = nn.Conv2d(self.channel_list[1], self.channel_list[2], kernel_size=[3,3], padding='same')
        self._layer_norm_2 = nn.LayerNorm([self.channel_list[2], H, W])
        # self._relu = nn.ReLU()
        self._relu = nn.GELU()
        

    def forward(self, inputs):
        x_ini = inputs
        x = self._layer_norm_1(x_ini)
        x = self._relu(x)
        x = self._conv_1(x)
        x = self._layer_norm_2(x)
        x = self._relu(x)
        x = self._conv_2(x)
        x_ini = x_ini + x
        return x_ini
    
# # DroneSig-cls backbone
# backbone:
#   # [from, repeats, module, args]
#   - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
#   - [-1, 1, Conv, [128, 3, 2]] # 1-P2/4
#   - [-1, 3, C2f, [128, True]]
#   - [-1, 1, Conv, [256, 3, 2]] # 3-P3/8
#   - [-1, 6, C2f, [256, True]]
#   - [-1, 1, Conv, [512, 3, 2]] # 5-P4/16
#   - [-1, 6, C2f, [512, True]]
#   - [-1, 1, Conv, [1024, 3, 2]] # 7-P5/32
#   - [-1, 3, C2f, [1024, True]]

# # DroneSig-cls head
# head:
#   - [-1, 1, Classify, [nc]] # Classify

def autopad(k, p=None, d=1):  # kernel, padding, dilation
    """Pad to 'same' shape outputs."""
    if d > 1:
        k = d * (k - 1) + 1 if isinstance(k, int) else [d * (x - 1) + 1 for x in k]  # actual kernel-size
    if p is None:
        p = k // 2 if isinstance(k, int) else [x // 2 for x in k]  # auto-pad
    return p

class Conv(nn.Module):
    """Standard convolution with args(ch_in, ch_out, kernel, stride, padding, groups, dilation, activation)."""

    default_act = nn.SiLU()  # default activation

    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, d=1, act=True):
        """Initialize Conv layer with given arguments including activation."""
        super().__init__()
        self.conv = nn.Conv2d(c1, c2, k, s, autopad(k, p, d), groups=g, dilation=d, bias=False)
        self.bn = nn.BatchNorm2d(c2)
        self.act = self.default_act if act is True else act if isinstance(act, nn.Module) else nn.Identity()

    def forward(self, x):
        """Apply convolution, batch normalization and activation to input tensor."""
        return self.act(self.bn(self.conv(x)))

    def forward_fuse(self, x):
        """Perform transposed convolution of 2D data."""
        return self.act(self.conv(x))
    
class ConvPreBN(nn.Module):
    """Standard convolution with args(ch_in, ch_out, kernel, stride, padding, groups, dilation, activation)."""

    default_act = nn.SiLU()  # default activation

    def __init__(self, c1, c2, k=1, s=1, p=None, g=1, d=1, act=True):
        """Initialize Conv layer with given arguments including activation."""
        super().__init__()
        self.conv = nn.Conv2d(c1, c2, k, s, autopad(k, p, d), groups=g, dilation=d, bias=False)
        self.bn = nn.BatchNorm2d(c1)
        self.act = self.default_act if act is True else act if isinstance(act, nn.Module) else nn.Identity()

    def forward(self, x):
        """Apply convolution, batch normalization and activation to input tensor."""
        return self.act(self.conv(self.bn(x)))

    def forward_fuse(self, x):
        """Perform transposed convolution of 2D data."""
        return self.act(self.conv(x))    

class Bottleneck(nn.Module):
    """Standard bottleneck."""

    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        """Initializes a bottleneck module with given input/output channels, shortcut option, group, kernels, and
        expansion.
        """
        super().__init__()
        c_ = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, c_, k[0], 1)
        self.cv2 = Conv(c_, c2, k[1], 1, g=g)
        self.add = shortcut and c1 == c2

    def forward(self, x):
        """'forward()' applies the YOLO FPN to input data."""
        return x + self.cv2(self.cv1(x)) if self.add else self.cv2(self.cv1(x))

#
#  特征聚合：通过将不同Bottleneck模块的输出和原始特征图拼接在一起，C2F模块能够更好地聚合多尺度信息。
#  模型压缩：C2F模块中的卷积操作可以有效地压缩特征图，减少计算量，同时保持或增强模型的表达能力。
#     
class C2f(nn.Module):
    """Faster Implementation of CSP Bottleneck with 2 convolutions."""

    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        """Initialize CSP bottleneck layer with two convolutions with arguments ch_in, ch_out, number, shortcut, groups,
        expansion.
        """
        super().__init__()
        self.c = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, 2 * self.c, 1, 1)
        self.cv2 = Conv((2 + n) * self.c, c2, 1)  # optional act=FReLU(c2)
        self.m = nn.ModuleList(Bottleneck(self.c, self.c, shortcut, g, k=((3, 3), (3, 3)), e=1.0) for _ in range(n))

    def forward(self, x):
        """Forward pass through C2f layer."""
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        return self.cv2(torch.cat(y, 1))

    def forward_split(self, x):
        """Forward pass using split() instead of chunk()."""
        y = list(self.cv1(x).split((self.c, self.c), 1))
        y.extend(m(y[-1]) for m in self.m)
        return self.cv2(torch.cat(y, 1))


#
# DCNN特征与最后一个完全连接层之间的点积等于特征和权值归一化后的余弦距离。
#
class Arcface_Head(nn.Module):
    def __init__(self, embedding_size=128, num_classes=10575, batch_size=64., m=0.5):
        super(Arcface_Head, self).__init__()
        self.s = batch_size
        self.m = m
        self.weight = Parameter(torch.FloatTensor(num_classes, embedding_size))
        nn.init.xavier_uniform_(self.weight)

        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, input, label): 
        if label is not None:
            if input.shape[1]==label.shape[1]:
                cosine = input #直接复用分向量
            else:
                cosine  = F.linear(input, F.normalize(self.weight))
        else:
            cosine  = F.linear(input, F.normalize(self.weight))
        

        if label is None:
            # when eval, logit is the cosine between W and pre_logits;
            # cos(theta_yj) = (x/||x||) * (W/||W||)   
            output = cosine #
        else: 
            # when training, add a margin to the pre_logits where target is
            # True, then logit is the cosine between W and new pre_logits
            sine    = torch.sqrt((1.0 - torch.pow(cosine, 2)).clamp(0, 1)) # sin(theta_yj)
            phi     = cosine * self.cos_m - sine * self.sin_m # cos(theta_yj+m)
            phi     = torch.where(cosine.float() > self.th, phi.float(), cosine.float() - self.mm)

            one_hot = torch.zeros(cosine.size()).type_as(phi).long()
            one_hot.scatter_(1, label.view(-1, 1).long(), 1)
            output  = (one_hot * phi) + ((1.0 - one_hot) * cosine) 

        output  *= self.s
        return output
        
    
class Classify(nn.Module):
    """classification head, i.e. x(b,c1,20,20) to x(b,c2)."""

    def __init__(self, c1, c2, k=1, s=1, p=None, g=1):
        """Initializes classification head with specified input and output channels, kernel size, stride,
        padding, and groups.
        """
        super().__init__()
        c_ = 1280  # efficientnet_b0 size
        self.conv = Conv(c1, c_, k, s, p, g)
        self.pool = nn.AdaptiveAvgPool2d(1)  # to x(b,c_,1,1)
        self.drop = nn.Dropout(p=0, inplace=True)
        self.linear = nn.Linear(c_, c2)  # to x(b,c2)

    def forward(self, x):
        """Performs a forward pass of the model on input image data."""
        if isinstance(x, list):
            x = torch.cat(x, 1)
        x = self.linear(self.drop(self.pool(self.conv(x)).flatten(1)))
                
        #return x if self.training  else x.softmax(1)
        return x

def dispSpectrum(spec,siglength,n_fft,hop_length,fs):
        nFrames = int(siglength/hop_length-1) # spec.shape[n_fft,nFrames]
        spec_abs = np.abs(spec.squeeze().cpu())
        #spec_abs = np.abs(spec[:,:].real.squeeze().cpu())
        #spec_abs = spec[:,:].real.squeeze().cpu()
        #plt.pcolormesh(np.array(range(nFrames))*hop_length/fs, np.array(range(n_fft))*fs/n_fft, 10*np.log10(spec_abs))
        plt.pcolormesh(np.array(range(nFrames))*hop_length*1000/fs, np.array(range(n_fft))*(fs/1e6)/n_fft, 20*np.log10(spec_abs))
        plt.colorbar()
        plt.title('STFT Magnitude')
        plt.ylabel('Frequency [MHz]')
        plt.xlabel('Time [ms]')
        plt.tight_layout()
        plt.show()

class DroneSig_classifier(nn.Module):
    '''
    无人机信号网络模型
    '''
    def __init__(self, nc, ModelType = 0,
                 **kwargs):
        '''
            nc         : 分类个数
            ModelType  : 模型类别 0:分类 1:角向量
        '''
        super(DroneSig_classifier, self).__init__(**kwargs)



        self.subcarriers = 1024 #4096 # 61.44e6/4096=15e3, 对于窄带 4M/1024=3.9K
        self.timesymbols = 151  # 根据实际会有变化
        self.streams = 1        # 流个数
        self.nclass  = nc       # 分类个数
        #信号分成3段，分别赋予不同权重
        self.weights = torch.tensor([0.5, 0.4, 0.1], requires_grad=True)
        self.weights = self.weights.cuda()
        self.HeaderType = ModelType

        self.in_ch = 8 # 划分块数
        self.out_ch = 16#64#128 #channel数

        #注意力机制网络，目前未加入

        #信号网络
        self.blocks = nn.Sequential()

        #self.prebn = nn.BatchNorm2d(1)
    
        #conv1 = Conv(c1=self.in_ch+2, c2=self.out_ch, k=[3,3]) #c1 划分块数+fs+bw
        conv1 = Conv(c1=self.in_ch, c2=self.out_ch, k=[3,3]) #c1 划分块数+fs+bw
        #conv1 = ConvPreBN(c1=self.in_ch+2, c2=self.out_ch, k=[3,3]) #c1 划分块数+fs+bw
        block_index = 0
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv1)

        #AttenBlock = SE(self.out_ch, 4)
        #AttenBlock = AxialImageTransformer(dim = self.out_ch, depth = 3,  reversible = True)
        AttenBlock = CBAM(self.out_ch)
        #AttenBlock = DAM(self.out_ch) #需要大内存
        block_index = 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=AttenBlock)

        for i in range(1):
            block = C2f(c1=16,c2=16,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        conv2 = Conv(c1=16, c2=32, k=[3,3], s=[2,2]) #1/2
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv2)

        for i in range(1):
            block = C2f(c1=32,c2=32,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block)           

        conv3 = Conv(c1=32, c2=64, k=[3,3], s=[2,2]) #1/4
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv3)           

        for i in range(1):
            block = C2f(c1=64,c2=64,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block)   

        conv4 = Conv(c1=64, c2=128, k=[3,3], s=2) #1/8
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv4) 

        for i in range(3):
            block = C2f(c1=128,c2=128,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        conv5 = Conv(c1=128, c2=256, k=[3,3], s=2) #1/16x
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv5) 

        for i in range(3):
            block = C2f(c1=256,c2=256,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        conv6 = Conv(c1=256, c2=512, k=[3,3], s=2) #1/32
        block_index = block_index + 1
        self.blocks.add_module(name='block_{}'.format(block_index), module=conv6) 

        for i in range(1):
            block = C2f(c1=512,c2=512,shortcut=True)
            block_index = block_index + 1
            self.blocks.add_module(name='block_{}'.format(block_index), module=block) 

        self.last_channel = 640 #1280
        self.conv_last = Conv(512, self.last_channel, k=1, s=1)
        self.pool_last = nn.AdaptiveAvgPool2d(1)  # to x(b,c_,1,1)
        self.features   = nn.BatchNorm1d(self.last_channel, eps=1e-05)

        #self.cls_layer = Classify(c1=256, c2=self.nclass)
        #分类头 (分类用)
        self.ClsHead = nn.Sequential(
            nn.Dropout(p=0),#0.2
            nn.Linear(self.last_channel, self.nclass),
        )
        #Arc向量头 (计算信号指纹用)
        self.ArcHead = Arcface_Head(embedding_size=self.last_channel, num_classes=self.nclass, batch_size=16)

    def normalize_complex_tensor(self, tensor):
        # 获取复数张量的模
        nRow = tensor.shape[0]
        for i in range(nRow): #复数归一化
            magnitudes = torch.abs(tensor[i,:])
            # 计算模的最大值
            max_magnitude = torch.max(magnitudes)
            # 避免除以零
            if max_magnitude>0:
                tensor[i,:] =  tensor[i,:] / max_magnitude
        return tensor

        
    def forward(self, y, fs_value, bw_value, labels=None, bpredict=False):
        # y : [batchsize, siglen, I/Q]
        # 1. 计算stft
        N_fft = self.subcarriers
        N_window = N_fft  
        N_overlap = torch.tensor(N_fft/2).floor().long() # hop 小段，提高分辨率，所以设置为1/4    
        z = torch.complex(y[:,:,0], y[:,:,1]) # 合成复数
        #z = self.normalize_complex_tensor(z)
        #torch.nn.BatchNorm1d()
        hann_window = torch.hann_window(N_fft, periodic=True, device=z.device)

        z = torch.stft(input=z, n_fft=N_fft, window=hann_window,hop_length=N_overlap, win_length=N_window,normalized=False,center=False) #转换到频域  
        #z = torch.fft.fftshift(z, dim=1) #[batch, fft_size, timeframe, I/Q]
        z = torch.fft.fftshift(z, dim=(-2, -1)) #[batch, fft_size, timeframe, I/Q]
        #z = self.normalize_complex_tensor(z)
        # fs = 61.44e6
        # dispSpectrum(z[0,:,:]+1e-6, 40000, N_fft, N_overlap.numpy(), 61.44e6)
        # 变换后信号，转换为I/Q信号，传递给分类网络

        #改进点数
        #取中心频率
        # center_start = (N_fft // 2) - 300
        # center_end = center_start + 600
        # z = z[:, center_start:center_end, :] #截取中心 600个点，61.44M时为 9M

        z = torch.abs(z).unsqueeze(-1)
        z = z.permute(0, 3, 1, 2)     #[batch, abs(I/Q), fft_size, timeframe]

        # 2 在时间维度，数据分为3块，分别设置不同权重
        chunks = torch.chunk(z, 3, dim=-1)
        # 为每块赋予权重
        weighted_chunks = [chunk * weight for chunk, weight in zip(chunks, self.weights)]
        # 重新组合
        z = torch.cat(weighted_chunks, dim=-1)
        nbatch, _ , nCarriers, nTimesym = z.shape
        n_subcount = torch.tensor(N_fft/self.in_ch).floor().long()
        z = torch.reshape(z,(nbatch, self.in_ch, n_subcount, nTimesym))
        
        #concat
        #fs_value_div = 1/fs_value
        fs_value_extend = torch.tile(fs_value,(1,n_subcount*nTimesym))
        fs_value_extend = torch.reshape(fs_value_extend,(nbatch, 1, n_subcount, nTimesym)) # 参数复制到 n_subcount, nTimesym
        bw_value_extend = torch.tile(bw_value, (1,n_subcount*nTimesym))
        bw_value_extend = torch.reshape(bw_value_extend,(nbatch, 1, n_subcount, nTimesym)) # 参数复制到 n_subcount, nTimesym
        #z = torch.cat((z, fs_value_extend, bw_value_extend), dim=1)
        

        # 特征提取网络
        z = self.blocks(z)    # [batch 512 fft_size/64 timeframe/64]
        z = self.conv_last(z) # [batch 640 fft_size/64 timeframe/64]
        z = self.pool_last(z) # to x(b,c_,1,1) 为 [batch 640]
        z = z.view(z.size()[0], -1) # [batch 640]

        # 产生分类头
        #z1 = self.ClsHead(z) # [batch self.nclass]  
        #需要独立训练
        # if bpredict==True: #预测
        #     z = F.normalize(z, dim=1)    # 格式化
        #     zOut = z #[batch 640] ,预测方式，返回原来的变量
        #     #zOut = self.ArcHead(z, None) #Can't broadcast (24,) -> (640,)
        # else:        
        #     z = F.normalize(z, dim=1)    # 格式化
        #     zOut = self.ClsHead(z) # [batch self.nclass] #复用分类参数            
        #     if self.HeaderType == 1:# 分类模型
        #         zOut = self.ArcHead(zOut, labels) # [batch self.nclass] 计算各类间夹角  
            

        if self.HeaderType == 0:# 分类模型
            z = F.normalize(z,dim=1)    # 格式化
            zOut = self.ClsHead(z) # [batch self.nclass]  
        else:                   # 角度向量模型
            #z = self.features(z) #作用不大
            # # 产生arcface向量抽取头
            if bpredict==True: #预测
                zOut = z #[batch 640] ,预测方式，返回原来的变量
                #zOut = self.ArcHead(z, None) #Can't broadcast (24,) -> (640,)
            else:        
                z = F.normalize(z, dim=1)    # 格式化
                zOut = self.ArcHead(z, labels) # [batch self.nclass] 计算各类间夹角
                
        return zOut 
