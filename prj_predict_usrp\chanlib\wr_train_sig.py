# =======================================================================================================================
#   Function    ：wr_train_sig.py
#   Description : 写入为hdfs信号文件
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import h5py
import numpy as np
import os


def WrTrainSig(fname_wr, rxSig_clip, class_id, class_name, fc, fs, bw, startbatch=0, lbl_startpos=0, lbl_endpos=0, duration_ms=0, snr_dB=0):
    # 生成写入训练数据
    # N_MaxPoints = 61.44e6*20e-3
    N_1msPoints = int(fs * 1e-3)
    #nMaxSize = (2, np.inf, np.inf)  # [I/Q N-points Batch]
    index = 1
    
    if os.path.exists(fname_wr)==False:
        f = h5py.File(fname_wr, "w-") #build File object
        f.create_dataset('rx_signal', shape=(1, N_1msPoints*10, 2), maxshape=(None, N_1msPoints*10, 2), dtype='float32', chunks=(1, N_1msPoints, 2))
        f.create_dataset('class_id', shape=(1, 1), maxshape=(None,1), dtype='int32', chunks=(1, 1))
        f.create_dataset('startpos', shape=(1, 1), maxshape=(None,1), dtype='int32', chunks=(1, 1))
        f.create_dataset('endpos', shape=(1, 1), maxshape=(None,1), dtype='int32', chunks=(1, 1))        
        f.create_dataset('fc', shape=(1, 1), maxshape=(None,1), dtype='float32', chunks=(1, 1))
        f.create_dataset('fs', shape=(1, 1), maxshape=(None,1), dtype='float32', chunks=(1, 1))
        f.create_dataset('bw', shape=(1, 1), maxshape=(None,1), dtype='float32', chunks=(1, 1))
        f.create_dataset('duration_ms', shape=(1, 1), maxshape=(None,1), dtype='float32', chunks=(1, 1))
        f.create_dataset('snr_dB', shape=(1, 1), maxshape=(None,1), dtype='float32', chunks=(1, 1))
        f.create_dataset('class_name', shape=(1, 1), maxshape=(None,1), dtype=h5py.string_dtype(), chunks=(1, 1))
        f.close()
        index = 0

    try:
        with h5py.File(fname_wr, 'a') as f:
            # if startbatch == 1 and fname_wr in f:
            #     print(f"已有训练数据文件：{fname_wr}")                
            # else:

            curvector  = f["rx_signal"]
            curclsid  = f["class_id"]
            curstartpos = f["startpos"]
            curendpos = f["endpos"]
            curclsname = f["class_name"]
            curfc = f["fc"]
            curfs = f["fs"]
            curbw = f["bw"]
            curSNR = f["snr_dB"]
            curduration = f["duration_ms"]

            if index!=0:#不为第1条数据
                index = curclsid.shape[0]
                curvector.resize(curvector.shape[0]+1, axis=0)
                curclsid.resize(curclsid.shape[0]+1, axis=0)
                curstartpos.resize(curstartpos.shape[0]+1, axis=0)
                curendpos.resize(curendpos.shape[0]+1, axis=0)
                curclsname.resize(curclsname.shape[0]+1, axis=0)
                curfc.resize(curfc.shape[0]+1, axis=0)
                curfs.resize(curfs.shape[0]+1, axis=0)
                curbw.resize(curbw.shape[0]+1, axis=0)
                curSNR.resize(curSNR.shape[0]+1, axis=0)
                curduration.resize(curduration.shape[0]+1, axis=0)

            # 写入数据
            N_points = len(rxSig_clip)
            #nCurSize = (1, N_points, 2)
            wr_sig = np.stack([np.real(rxSig_clip), np.imag(rxSig_clip)], axis=-1)
            #wr_sig = np.reshape(np.stack([np.real(rxSig_clip), np.imag(rxSig_clip)], axis=-1), nCurSize)
            curvector[index, 0:N_points] = wr_sig.astype(np.float32)
            curclsid[index] = np.int32(class_id)
            curstartpos[index] = np.int32(lbl_startpos)
            curendpos[index] = np.int32(lbl_endpos)            
            curfc[index] = np.float32(fc)
            curfs[index] = np.float32(fs)
            curbw[index] = np.float32(bw)
            curduration[index] = np.float32(duration_ms)
            curSNR[index] = np.float32(snr_dB)
            curclsname[index] = str(class_name)

        print(f"写入训练数据文件：{fname_wr}")
    except Exception as e:
        print(f"写入文件时出错: {e}")

    