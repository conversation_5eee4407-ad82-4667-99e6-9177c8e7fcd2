function [bOK] = DetectSigSpread(data,wb_fs,wb_fc, fc_nb)
%  Function    ：DetectSigSpread
%  Description : 检查文件夹下的bvsp信号质量
%  Parameter   : foldername       -- 文件夹路径名称
%
%  Return      :
%
%  Author      : Liuzhiguo
%  Date        : 2025-06-16

%stft变换
fft_len = 2048;%2048;       %fft长度
fft_overlap = fft_len/2;
window = blackman(fft_len,"periodic");

psd_value_out = stft(data,wb_fs,"Window",window,"OverlapLength",fft_overlap,"FFTLength",fft_len,"FrequencyRange","centered");%
col = length(psd_value_out(1,:));
psd_value_out = abs(psd_value_out)/fft_len;%[f, t]

%寻找中心频率点索引
f  = (-fft_len/2:1:fft_len/2-1)*(wb_fs/fft_len)+wb_fc;
index = 1;
for n=1:length(f)
    if f(n) > fc_nb
        index = n-1;
        break;
    end
end

%寻找是否有功率阈值特别低的点
psd_value_sel = psd_value_out(index,:);
%threshold_psd = 0.0001*max(psd_value_sel);
% threshold_psd = 2.0*mean(psd_value_out,"all");%lora信号不满足
% threshold_psd = 1/55*mean(psd_value_out,"all");%lora信号
threshold_psd = 100*min(min(psd_value_out));
%detectChangePoints(psd_value_sel);
for i=1:col
    if psd_value_sel(i) < threshold_psd
        break;
    end
end
bOK = true;
if i<col/4 
    bOK = false;
elseif i>col*3/4
    bOK = (i==col);
end
if bOK~=true
    fprintf("检测频域信号起始位置，结果=%d\n",bOK);
end
end

function detectChangePoints(y,thresholdFactor, minInterval)
% 检测信号中的突变点
% 输入参数:
%   y - 信号点数组
%   thresholdFactor - 阈值因子(默认3.0)
%   minInterval - 突变点最小间隔(采样点数，默认20)

% 设置默认参数
if nargin < 2
    thresholdFactor = 3.0;
end
if nargin < 3
    minInterval = 3;
end
% 归一化信号
y = y / max(abs(y));

% 预处理 - 平滑滤波
windowSize = 5;
smoothedSignal = movmedian(y, windowSize);

% 计算一阶差分
firstDiff = diff(smoothedSignal);

% 计算差分绝对值
diffAbs = abs(firstDiff);

% 计算阈值 - 基于均值和标准差
diffMean = mean(diffAbs);
diffStd = std(diffAbs);
threshold = diffMean + thresholdFactor * diffStd;

% 检测超过阈值的点
candidatePoints = find(diffAbs > threshold);
disp(threshold)
disp(diffAbs(candidatePoints))
% 后处理 - 去除时间间隔过近的点
changePoints = [];
if ~isempty(candidatePoints)
    changePoints = candidatePoints(1);
    for i = 2:length(candidatePoints)
        if candidatePoints(i) - changePoints(end) > minInterval
            changePoints = [changePoints, candidatePoints(i)];
        end
    end
end

% 输出突变点时间
changeTimes = changePoints*1024/61.44e6;
fprintf('检测到 %d 个突变点，时间点(秒):\n', length(changePoints));
for i = 1:length(changeTimes)
    fprintf('  突变点 %d: %.3f ms\n', i, changeTimes(i)*1e3);
end

end