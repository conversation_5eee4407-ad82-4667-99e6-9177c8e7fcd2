# =======================================================================================================================
#   Function    ：predict_proc.py
#   Description : 模型预测推理代码
#                 传入hdf5文件路径，并对modelTrain.py调用
# 
#   Parameter   : ../data/predict/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from nets.arcface import Arcface
from usrlib.usrlib import *
from utils.dataloader import *
from usrlib.VectorToVectorSim import *
import getopt

def load_classify_model(model_path = "logs/Mtype0-ep100-loss0.008-val_loss0.007.pth"):
    modeltype = 0 #模型类别 0:分类 1:角向量
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    #dataset_file = "" # 数据文件路径
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)     
    #1. 路径及模型参数
    t1 = time.time()
    #2.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    return Model, cls_count, cls_ids, cls_names

def predict_classify_proc(Model, cls_count, cls_names, dataset_file, thresholds):
    batch_size = 64 #256  
    predict_out =  np.zeros(cls_count, dtype=int)
    #1. 读取数据文件
    t0 = time.time()
    #print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    if os.path.exists(dataset_file):
        rx_signal,classid_gt,class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_file)
    else:
         return predict_out#未能捕捉到信号不需要推理

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  


    t2 = time.time()
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    #2. 推理过程 
    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput.softmax(dim=1))
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("Classify model eval：{:.2f}s".format(t3 - t2))

    val_predict = torch.argmax(output_all,dim=1)
    nCount = val_predict.shape[0]

    print('list the top predict results:')
    #predict_threshold = 0.8
    for i in np.arange(nCount):
        id_cls = val_predict[i]
        print("predict val:{0} {1} props: {2:.5f}".format(val_predict[i].item(), cls_names[val_predict[i]], output_all[i, id_cls]))
        if output_all[i, id_cls] > thresholds[id_cls]: #每类独立的阈值
            predict_out[id_cls] += 1

    return predict_out

def predict_classify_proc_all(Model, cls_count, cls_names, dataset_file, thresholds):
    batch_size = 64 #256  
    predict_out =  np.zeros(cls_count, dtype=int)
    #1. 读取数据文件
    t0 = time.time()
    #print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    if os.path.exists(dataset_file):
        rx_signal,classid_gt,class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_file)
    else:
         return predict_out#未能捕捉到信号不需要推理

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  


    t2 = time.time()
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    #2. 推理过程 
    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput.softmax(dim=1))
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("Classify model eval：{:.2f}s".format(t3 - t2))

    val_predict = torch.argmax(output_all,dim=1)
    nCount = val_predict.shape[0]
    print('list the top predict results:')
    #predict_threshold = 0.8
    for i in np.arange(nCount):
        id_cls = val_predict[i]

        print("predict val:{0} {1} props: {2:.5f}".format(val_predict[i].item(), cls_names[val_predict[i]], output_all[i, id_cls]))

    return output_all


def load_match_model(model_path = "logs/Mtype1-ep100-loss0.000-val_loss0.000.pth"):
    modeltype = 1 #模型类别 0:分类 1:角向量
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    #dataset_file = "" # 数据文件路径
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)     
    #1. 路径及模型参数
    

    t1 = time.time()
    #2.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    return Model, cls_count, cls_ids, cls_names

def predict_match_proc(Model, cls_count, cls_names, dataset_file, thresholds):
    batch_size = 64 #256  
    predict_out =  np.zeros(cls_count, dtype=int)
    #1. 读取数据文件
    t0 = time.time()
    #print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    if os.path.exists(dataset_file):
        rx_signal,classid_gt,class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_file)
    else:
         return predict_out#未能捕捉到信号不需要推理

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  


    t2 = time.time()
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    #2. 推理过程 
    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput)
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("Matching model eval：{:.2f}s".format(t3 - t2))

    # 5. 取数据库中特征向量
    db_result = GetArcVectorDB()
    # 兼容新旧格式的数据库
    if len(db_result) == 9:  # 最新版本数据库（包含record_id）
        vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, _, _ = db_result
    elif len(db_result) == 8:  # 中间版本数据库（包含样本字段但无record_id）
        vectors, clsids, clsnames, filepaths, curfs, curbw, _, _ = db_result
    else:  # 旧版本数据库
        vectors, clsids, clsnames, filepaths, curfs, curbw = db_result
    VectorSim = VectorSimilarity()
    if len(output_all.shape) == 1:
        avec = output_all.unsqueeze(0)
    else:
        avec = output_all

    vectors_tensor = torch.from_numpy(vectors)
    vectors_tensor = vectors_tensor.cuda()
    predicts = VectorSim.matching(avec, vectors_tensor) # 匹配特征向量

    nCount = predicts['pred_label'].shape[0]

    # 调试信息：检查相似度矩阵中的nan值
    score_matrix = predicts['score']
    nan_count = torch.isnan(score_matrix).sum().item()
    inf_count = torch.isinf(score_matrix).sum().item()
    print(f"相似度矩阵形状: {score_matrix.shape}")
    print(f"NaN值数量: {nan_count}, Inf值数量: {inf_count}")
    if nan_count > 0 or inf_count > 0:
        print("警告: 相似度计算中存在无效值，将被替换为默认值")

    print('list the top predict results:')
    #predict_threshold = 0.8
    for i in np.arange(nCount):
        id_cls_index = predicts['pred_label'][i, 0]
        clsnames_db = clsnames[id_cls_index, 0]
        # 处理字节字符串
        if isinstance(clsnames_db, bytes):
            clsnames_db = clsnames_db.decode('utf-8')
        elif isinstance(clsnames_db, str) and clsnames_db.startswith("b'"):
            clsnames_db = clsnames_db[2:-1]
        else:
            clsnames_db = str(clsnames_db)

        probs = predicts['pred_score'][i, 0]
        
        # 处理nan/inf值
        if torch.isnan(probs) or torch.isinf(probs):
            # 将nan/inf替换为一个小的默认值
            probs = torch.tensor(0.01, device=probs.device)
            print(f"警告: 样本 {i} 的相似度分数为无效值，已替换为默认值")

        try:
            id_cls = cls_names.index(clsnames_db) #从数据库中索引出 id_cls
            print("predict val:{0} {1} props: {2:.5f}".format(id_cls, cls_names[id_cls], probs))
            if probs > thresholds[id_cls]: #每类独立的阈值
                predict_out[id_cls] += 1
        except ValueError:
            print(f"警告: 类名 '{clsnames_db}' 不在预定义类别列表中")

    return predict_out

def predict_match_proc_all(Model, cls_count, cls_names, dataset_file, thresholds):
    batch_size = 64 #256
    predict_out =  np.zeros(cls_count, dtype=int)
    #1. 读取数据文件
    t0 = time.time()
    #print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    if os.path.exists(dataset_file):
        rx_signal,classid_gt,class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_file)
    else:
         # 返回与predict_classify_proc_all兼容的格式：torch.Tensor
         return torch.zeros(1, cls_count, dtype=torch.float32)#未能捕捉到信号不需要推理

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  


    t2 = time.time()
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    #2. 推理过程 
    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput)
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("Matching model eval：{:.2f}s".format(t3 - t2))

    # 5. 取数据库中特征向量
    db_result = GetArcVectorDB()
    # 兼容新旧格式的数据库
    if len(db_result) == 9:  # 最新版本数据库（包含record_id）
        vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, _, _ = db_result
    elif len(db_result) == 8:  # 中间版本数据库（包含样本字段但无record_id）
        vectors, clsids, clsnames, filepaths, curfs, curbw, _, _ = db_result
    else:  # 旧版本数据库
        vectors, clsids, clsnames, filepaths, curfs, curbw = db_result
    VectorSim = VectorSimilarity()
    if len(output_all.shape) == 1:
        avec = output_all.unsqueeze(0)
    else:
        avec = output_all

    vectors_tensor = torch.from_numpy(vectors)
    vectors_tensor = vectors_tensor.cuda()
    predicts = VectorSim.matching(avec, vectors_tensor) # 匹配特征向量

    nCount = predicts['pred_label'].shape[0]

    print('list the top predict results:')
    #predict_threshold = 0.8
    print("nCount:", nCount)

    # 调试信息：检查相似度矩阵中的nan值
    score_matrix = predicts['score']
    nan_count = torch.isnan(score_matrix).sum().item()
    inf_count = torch.isinf(score_matrix).sum().item()
    print(f"相似度矩阵形状: {score_matrix.shape}")
    print(f"NaN值数量: {nan_count}, Inf值数量: {inf_count}")
    if nan_count > 0 or inf_count > 0:
        print("警告: 相似度计算中存在无效值，将被替换为默认值")

    # 获取数据库向量数量
    db_vector_count = predicts['score'].shape[1]

    # 创建与predict_classify_proc_all兼容的输出格式
    # 形状为[nCount, cls_count]的概率分布张量
    output_all_compatible = torch.zeros(nCount, cls_count, dtype=torch.float32)

    # 创建数据库索引到类别索引的映射
    db_to_cls_mapping = {}
    for db_idx in range(db_vector_count):
        clsnames_db = clsnames[db_idx, 0]
        # 处理字节字符串，提取单引号内的类名
        if isinstance(clsnames_db, bytes):
            clsnames_db = clsnames_db.decode('utf-8')
        elif isinstance(clsnames_db, str) and clsnames_db.startswith("b'"):
            clsnames_db = clsnames_db[2:-1]  # 去掉 b' 和 '
        else:
            clsnames_db = str(clsnames_db)

        try:
            cls_idx = cls_names.index(clsnames_db)
            db_to_cls_mapping[db_idx] = cls_idx
        except ValueError:
            print(f"警告: 数据库中的类名 '{clsnames_db}' 不在预定义类别列表中")
            continue

    # 将相似度分数映射到对应的类别
    for sample_idx in range(nCount):
        for db_idx, cls_idx in db_to_cls_mapping.items():
            similarity_score = predicts['score'][sample_idx, db_idx]

            # 处理nan值
            if torch.isnan(similarity_score) or torch.isinf(similarity_score):
                # 将nan/inf替换为一个小的默认值
                similarity_score = torch.tensor(0.01, device=similarity_score.device)

            # 对于同一类别的多个数据库向量，取最大相似度
            output_all_compatible[sample_idx, cls_idx] = torch.max(output_all_compatible[sample_idx, cls_idx], similarity_score)

    # 显示top预测结果
    for i in np.arange(min(nCount, 5)):  # 只显示前5个样本
        id_cls_index = predicts['pred_label'][i, 0]
        clsnames_db = clsnames[id_cls_index, 0]
        # 处理字节字符串
        if isinstance(clsnames_db, bytes):
            clsnames_db = clsnames_db.decode('utf-8')
        elif isinstance(clsnames_db, str) and clsnames_db.startswith("b'"):
            clsnames_db = clsnames_db[2:-1]
        else:
            clsnames_db = str(clsnames_db)

        probs = predicts['pred_score'][i, 0]
        try:
            cls_idx = cls_names.index(clsnames_db)
            print("predict val:{0} {1} props: {2:.5f}".format(cls_idx, clsnames_db, probs))
        except ValueError:
            print(f"警告: 类名 '{clsnames_db}' 不在预定义类别列表中")

    return output_all_compatible


def get_match_vector(Model,  dataset_file):
    batch_size = 64 #256  
    #1. 读取数据文件
    t0 = time.time()
    #print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    if os.path.exists(dataset_file):
        rx_signal,classid_gt,class_name, fs_value, bw_value, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_file)
    else:
         return [], [], []#未能捕捉到信号不需要推理

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  


    t2 = time.time()
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    #2. 推理过程 
    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput)
            break

    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    
    return output_all.detach().cpu().numpy(), afs_value.detach().cpu().numpy(), abw_value.detach().cpu().numpy()