function [new_fc, new_bw] = findAccuracteBW(signal_in, nb_fc, nb_bw,  wb_fc, wb_fs,fft_len)
%  Function    ：findAccuracteBW
%  Description : 根据信号，粗布估计的中心频率带宽等参数，
%                搜索更精确的带宽及中心频率参数
%  Parameter   : 
%                   signal_in  -- 输入信号
%                   nb_fc      -- 初步估计得到的中心频率
%                   nb_bw      -- 初步估计得到的带宽
%                   wb_fc      -- 信号宽带设置的中心频率
%                   wb_fs      -- 信号采样率
%                   fft_len    -- 估计时用的fft长度
%                   
%  return      :
%                   new_fc -- 新的中心点频率
%                   new_fc -- 新的带宽点
%
%  Author      : Liuzhiguo
%  Date        : 2025-02-08
%
%showWBSig(signal,wb_fc, wb_fs);

% 1 对于输入信号进行滤波
df          = wb_fs/fft_len;
fc_range     = [nb_fc-1*df, nb_fc+1*df];
nb_bw_max = nb_bw+2*df;

L_filter = 48;
Wn = nb_bw_max/(wb_fs/2); %带宽估算
b = fir1(L_filter, Wn); % 0.05
signal_filtered= filter(b,1,signal_in);

% 2 进行全信号域快速傅里叶变换
wb_samps = length(signal_filtered);
y = abs(fft(signal_filtered,wb_samps))/wb_samps;      %直接fft
%y = abs(fft(xcorr(signal_filtered,"biased"), wb_samps))/wb_samps; %谱相关

sig_freqs=wb_fc - wb_fs/2 +(0 : wb_samps - 1)*wb_fs/wb_samps;%信号频率
Psd_signal = fftshift(y);

% 3 寻找信号的开始帧和结束帧，在此范围内寻找最大频率点
[new_fc]=findmaxfreq(sig_freqs, Psd_signal, fc_range);

if 0
    figure(2);
    subplot(2,1,1);
    plot(Psd_signal);
    xlabel('数据点'); ylabel('频谱值');
    subplot(2,1,2);
    plot(sig_freqs/1e6, Psd_signal);
    title(['频谱图 [fc= ' num2str(wb_fc/1e6) 'Mhz' '  fs= ' num2str(wb_fs/1e6) 'Mhz]']);
    xlabel('频率(MHz)'); ylabel('频谱值');
end

% 4 寻找带宽的开始帧和结束帧，在此范围内寻找起始和结束点
%分段查找开始及结束范围
gap = 2*df;
[lower_band] = findmaxfreq(sig_freqs, Psd_signal, [floor(new_fc-nb_bw/2-gap), floor(new_fc-nb_bw/2+gap)]);
[upper_band ] = findmaxfreq(sig_freqs, Psd_signal, [floor(new_fc+nb_bw/2-gap), floor(new_fc+nb_bw/2+gap)]);
new_bw = floor(upper_band - lower_band);
if new_bw < nb_bw/4
    new_bw = nb_bw;
end

end

function [fc]=findmaxfreq(sig_freqs, Psd_signal, fc_range)
%  Function    ：findmaxfreq
%  Description : 根据信号频率范围，搜索最大频率值点
%  Parameter   : 
%                   sig_freqs  -- 整体信号频率
%                   Psd_signal -- 整体信号功率值 
%                   fc_range   -- 中心频率范围
%  return      :
%                   fc -- 中心点频率
%
%  Author      : Liuzhiguo
%  Date        : 2025-02-08
%
start_frame = find(sig_freqs > fc_range(1), 1, 'first');
end_frame   = find(sig_freqs < fc_range(2), 1, 'last');
%[~, idx]    = max(Psd_signal(start_frame:end_frame));
[~, idx]    = max(abs(diff(Psd_signal(start_frame:end_frame))));%问题，有可能sig_freqs频率范围过大
fc          = sig_freqs(start_frame+idx-1);
end