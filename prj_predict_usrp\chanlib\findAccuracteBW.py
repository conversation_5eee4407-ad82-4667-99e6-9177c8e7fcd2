# =======================================================================================================================
#   Function    ：findAccuracteBW.py
#   Description : 根据信号，初步估计的中心频率带宽等参数，搜索更精确的带宽及中心频率参数
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import numpy as np
from scipy.signal import firwin, lfilter, fftconvolve
import matplotlib.pyplot as plt


def findmaxfreq(sig_freqs, Psd_signal, fc_range):
    """
    根据信号频率范围，搜索最大频率值点
    :param sig_freqs: 整体信号频率
    :param Psd_signal: 整体信号功率值
    :param fc_range: 中心频率范围
    :return: 中心点频率
    """
    start_frame = np.argmax(sig_freqs > fc_range[0])
    end_frame = len(sig_freqs) - np.argmax(sig_freqs[::-1] < fc_range[1]) 
    #end_frame = np.argmax(sig_freqs[::-1] < fc_range[1])
    idx = np.argmax(np.abs(np.diff(Psd_signal[start_frame:end_frame])))
    fc = sig_freqs[start_frame + idx]
    return fc


def findAccuracteBW(signal_in, nb_fc, nb_bw, wb_fc, wb_fs, fft_len):
    """
    根据信号，初步估计的中心频率带宽等参数，
    搜索更精确的带宽及中心频率参数
    :param signal_in: 输入信号
    :param nb_fc: 初步估计得到的中心频率
    :param nb_bw: 初步估计得到的带宽
    :param wb_fc: 信号宽带设置的中心频率
    :param wb_fs: 信号采样率
    :param fft_len: 估计时用的fft长度
    :return: 新的中心点频率, 新的带宽点
    """
    # 1 对于输入信号进行滤波
    df = wb_fs / fft_len
    fc_range = [nb_fc - 1 * df, nb_fc + 1 * df]
    nb_bw_max = nb_bw + 2 * df

    L_filter = 48
    Wn = nb_bw_max / (wb_fs / 2)
    b = firwin(L_filter+1, Wn)
    signal_filtered = lfilter(b, 1, signal_in)

    # 2 进行全信号域快速傅里叶变换
    wb_samps = len(signal_filtered)
    y = np.abs(np.fft.fft(signal_filtered, wb_samps)) / wb_samps
    sig_freqs = wb_fc - wb_fs / 2 + np.arange(wb_samps) * wb_fs / wb_samps
    Psd_signal = np.fft.fftshift(y)

    # 3 寻找信号的开始帧和结束帧，在此范围内寻找最大频率点
    new_fc = findmaxfreq(sig_freqs, Psd_signal, fc_range)

    if False:
        plt.figure(2)
        plt.subplot(2, 1, 1)
        plt.plot(Psd_signal)
        plt.xlabel('数据点')
        plt.ylabel('频谱值')
        plt.subplot(2, 1, 2)
        plt.plot(sig_freqs / 1e6, Psd_signal)
        plt.title(f'频谱图 [fc= {wb_fc / 1e6}Mhz  fs= {wb_fs / 1e6}Mhz]')
        plt.xlabel('频率(MHz)')
        plt.ylabel('频谱值')
        plt.show()

    # 4 寻找带宽的开始帧和结束帧，在此范围内寻找起始和结束点
    gap = df
    lower_band = findmaxfreq(sig_freqs, Psd_signal, [np.floor(new_fc - nb_bw / 2 - gap),
                                                     np.floor(new_fc - nb_bw / 2 + gap)])
    upper_band = findmaxfreq(sig_freqs, Psd_signal, [np.floor(new_fc + nb_bw / 2 - gap),
                                                     np.floor(new_fc + nb_bw / 2 + gap)])
    new_bw = np.floor(upper_band - lower_band)
    if new_bw <= 20e3:#求得值太小，不符合要求
        new_bw = nb_bw
    return new_fc, new_bw

    