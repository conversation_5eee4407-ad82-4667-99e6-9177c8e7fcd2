#!/usr/bin/env python3
"""
进度对话框模块

提供用于创建和更新进度对话框的功能
"""

import tkinter as tk
from tkinter import ttk

class ProgressDialog:
    """
    进度对话框类
    
    提供创建和更新进度对话框的功能
    """
    
    def __init__(self, parent, title, message, language='zh', width=400, height=150):
        """
        初始化进度对话框
        
        Args:
            parent: 父窗口
            title: 对话框标题
            message: 初始消息
            language: 语言设置，'zh'为中文，其他为英文
            width: 对话框宽度
            height: 对话框高度
        """
        self.parent = parent
        self.language = language
        self.cancel_flag = False
        self.on_cancel_callback = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 设置对话框大小和位置
        x = parent.winfo_rootx() + (parent.winfo_width() - width) // 2
        y = parent.winfo_rooty() + (parent.winfo_height() - height) // 2
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
        self.dialog.resizable(False, False)
        
        # 添加进度消息标签
        self.msg_label = tk.Label(self.dialog, text=message, pady=10)
        self.msg_label.pack()
        
        # 添加进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.dialog, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, padx=20, pady=10)
        
        # 添加文件计数标签
        self.count_label = tk.Label(self.dialog, text="", pady=5)
        self.count_label.pack()
        
        # 添加取消按钮
        cancel_text = "取消" if language == 'zh' else "Cancel"
        self.cancel_button = tk.Button(self.dialog, text=cancel_text, command=self._on_cancel)
        self.cancel_button.pack(pady=10)
        
        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self._on_cancel)
        
        # 初始化进度
        self.update_progress(0, message)
    
    def update_progress(self, value, message=None, file_count=None):
        """
        更新进度信息
        
        Args:
            value: 进度值（0-100）
            message: 进度消息（可选）
            file_count: 文件计数信息（可选）
        """
        if self.dialog.winfo_exists():
            self.progress_var.set(value)
            
            if message is not None:
                self.msg_label.config(text=message)
            
            if file_count is not None:
                count_text = f"已处理文件数: {file_count}" if self.language == 'zh' else f"Files processed: {file_count}"
                self.count_label.config(text=count_text)
            
            self.dialog.update()
    
    def set_on_cancel(self, callback):
        """
        设置取消回调函数
        
        Args:
            callback: 取消时调用的函数
        """
        self.on_cancel_callback = callback
    
    def _on_cancel(self):
        """
        取消按钮点击事件
        """
        self.cancel_flag = True
        
        if self.on_cancel_callback:
            self.on_cancel_callback()
        
        self.close()
    
    def is_cancelled(self):
        """
        检查是否已取消
        
        Returns:
            bool: 是否已取消
        """
        return self.cancel_flag
    
    def close(self):
        """
        关闭对话框
        """
        if self.dialog.winfo_exists():
            self.dialog.destroy()
    
    def start_auto_pulse(self, interval=100):
        """
        启动自动脉冲模式（适用于无法获取确切进度的情况）
        
        Args:
            interval: 更新间隔（毫秒）
        """
        def pulse_step():
            if self.dialog.winfo_exists() and not self.cancel_flag:
                self.progress_bar.step(1)
                self.dialog.after(interval, pulse_step)
        
        # 将进度条设置为不确定模式
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start(interval)
        
        # 启动脉冲更新
        self.dialog.after(interval, pulse_step)
    
    def start_auto_update(self, interval=100, max_value=90):
        """
        启动自动更新模式（模拟进度）
        
        Args:
            interval: 更新间隔（毫秒）
            max_value: 最大进度值（通常小于100，留出一些空间给最终处理）
        """
        def update_step():
            if self.dialog.winfo_exists() and not self.cancel_flag:
                current_value = self.progress_var.get()
                if current_value < max_value:
                    # 使用非线性增长，开始快，接近max_value时变慢
                    increment = 0.5 * (1 - current_value / max_value)
                    self.progress_var.set(current_value + increment)
                
                self.dialog.after(interval, update_step)
        
        # 启动更新循环
        self.dialog.after(interval, update_step)