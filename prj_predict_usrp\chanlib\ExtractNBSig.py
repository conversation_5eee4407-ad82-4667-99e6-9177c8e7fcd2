# =======================================================================================================================
#   Function    ：ExtractNBSig.py
#   Description : 
#                 滤波及窄带化函数
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-07
# =======================================================================================================================
import numpy as np
import math

def ExtractNBSig(sig, wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs, nb_len_set=None):
    # 计算宽带信号长度
    wb_n_sig = len(sig)
    # 对信号进行FFT变换
    wb_sig_fd = np.fft.fft(sig)
    # 对频域信号进行循环移位
    wb_sig_fd = np.roll(wb_sig_fd, np.floor(len(wb_sig_fd) / 2).astype(int))

    if nb_len_set is not None:
        nb_n_sig = nb_len_set
    else:
        # 采样率换算，计算窄带信号长度
        nb_n_sig = int(round(wb_n_sig * (nb_fs / wb_fs)))
        # 重新计算采样率
        nb_fs = 2 * nb_bw

    try:
        # 调用窄带化函数
        ret, nb_sig_fd = ExtractNarrowbandSignal(wb_sig_fd, wb_fc, wb_bw, wb_fs, nb_n_sig, nb_fc, nb_bw, nb_fs, 1)
    except Exception as ME:
        raise ME

    if ret != 1:
        return ret, nb_sig_fd

    # 将信号转换到时域
    nb_sig_td = np.fft.ifft(nb_sig_fd) * nb_n_sig
    # 功率归一化因子
    pow_norm_factor = 1 / wb_n_sig
    # 将信号功率归一化
    nb_sig_td = nb_sig_td * pow_norm_factor
    sync_sig_td = nb_sig_td

    return ret, sync_sig_td

def IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs):
    """
    函数    ：IsValidNarrowBW
    描述    : 依据窄带信号分布范围是否在宽带信号分布范围以内，判断是否为有效的窄带信号
    参数    : 
             wb_fc       -- 宽带信号中心频率
             wb_bw       -- 宽带信号带宽
             wb_fs       -- 宽带信号采样率 
             nb_fc       -- 窄带信号中心频率
             nb_bw       -- 窄带信号带宽 
             nb_fs       -- 窄带信号采样率 
    返回值  : 1:有效 -1:无效
    作者    : Liuzhiguo
    日期    : 2025-04-28
    """
    ret = 1
    if wb_bw < wb_fs:
        # 1. 计算宽带信号上下边界
        wb_bw_lb = wb_fc - wb_bw / 2
        wb_bw_ub = wb_fc + wb_bw / 2
        wb_fs_lb = wb_fc - wb_fs / 2
        wb_fs_ub = wb_fc + wb_fs / 2
    else:
        ret = -1
        return ret

    # 2. 计算窄带信号上下边界
    scale_factor = 1.2
    nb_bw_lb = nb_fc - math.floor((nb_bw * scale_factor) / 2)
    nb_bw_ub = nb_fc + math.floor((nb_bw * scale_factor) / 2)
    nb_fs_lb = nb_fc - math.floor(nb_fs / 2)
    nb_fs_ub = nb_fc + math.floor(nb_fs / 2)

    # 检查下边界 bw
    if nb_bw_lb < wb_bw_lb:
        ret = -1
        print(f"Error: nb_bw_lb={nb_bw_lb:.2f} < wb_bw_lb={wb_bw_lb:.2f}")
        return ret
    # 检查上边界 bw
    if nb_bw_ub > wb_bw_ub:
        ret = -1
        print(f"Error: nb_bw_ub={nb_bw_ub:.2f} > wb_bw_ub={wb_bw_ub:.2f}")
        return ret
    # 检查下边界 fs
    if nb_fs_lb < wb_fs_lb:
        ret = -1
        print(f"Error: nb_fs_lb={nb_fs_lb:.2f} < wb_fs_lb={wb_fs_lb:.2f}")
        return ret
    # 检查上边界 fs
    if nb_fs_ub > wb_fs_ub:
        ret = -1
        print(f"Error: nb_fs_ub={nb_fs_ub:.2f} > wb_fs_ub={wb_fs_ub:.2f}")
        return ret

    return ret

def getfftData(wb_signal, wb_fc, wb_fs):
    """
    计算信号的FFT变换并确定频率序列
    
    参数:
        wb_signal: 输入信号
        wb_fc: 中心频率
        wb_fs: 采样频率
    
    返回:
        fftdata: FFT变换后的幅度值（已归一化）
        nb_freqs: 对应的频率序列
    """
    nSig_len = len(wb_signal)
    # 计算FFT并归一化
    fftdata = np.abs(np.fft.fftshift(np.fft.fft(wb_signal))) / nSig_len
    wb_samps = nSig_len
    # 生成频率序列
    nb_freqs = wb_fc - wb_fs/2 + np.arange(wb_samps) * wb_fs/wb_samps
    
    return fftdata, nb_freqs

def getFreqIndex(nb_freqs, fc_nb):
    """
    搜索频率范围索引
    
    参数:
        nb_freqs: 频率序列
        fc_nb: 目标频率
    
    返回:
        nIndex: 第一个大于目标频率的索引（Python中索引从0开始）
    """
    nIndex = 0
    # 遍历频率序列，寻找第一个大于fc_nb的索引
    for n in range(len(nb_freqs)):
        if nb_freqs[n] > fc_nb:
            nIndex = n
            break
    
    return nIndex

def ExtractNarrowbandSignal(wb_sig_fd, wb_fc, wb_bw, wb_fs, nb_len_set, nb_fc, nb_bw, nb_fs, rm_dc=1):
    """
    从宽带信号中提取每个通道
    :param wb_sig_fd: 宽带信号频域点
    :param wb_fc: 宽带信号中心频率
    :param wb_bw: 宽带信号带宽
    :param wb_fs: 宽带信号采样率
    :param nb_len_set: 窄带信号点数
    :param nb_fc: 窄带信号中心频率
    :param nb_bw: 窄带信号带宽
    :param nb_fs: 窄带信号采样率
    :param rm_dc: 去直流
    :return: ret, nb_sig_fd
    """
    ret = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs)
    nb_sig_fd = np.zeros(nb_len_set, dtype=complex)
    if ret != 1:
        return ret, nb_sig_fd

    # 1. 信号带宽及长度折算
    wb_len = len(wb_sig_fd)  # 宽带信号的频域点数，wb_len/2为中心频率点

    nb_dc = round(wb_len * (nb_fc - wb_fc) / wb_fs + wb_len / 2)  # 窄带中心频率点位置，同时减去wb_fc也实现了下变频功能
    nb_bw_len = math.floor(wb_len * nb_fs / wb_fs)  # 窄带信号长度(折算到窄带长度),用 nb_fs 折算点数
    nb_bw_len = min(nb_bw_len, nb_len_set)  # 选择最少的频域点数

    nb_len_pos = math.floor(nb_bw_len / 2)
    nb_len_neg = nb_bw_len - nb_len_pos  # 实质上为: 窄带长度/2

    # 2. 信号低通滤波，仅仅取：窄带频率中心点位置 + 窄带频率长度/2，窄带频率中心点位置 - 窄带频率长度/2
    # Low-pass filtering
    # 设置 上半部分 [0-fs/2]+nb_dc
    if rm_dc:
        len_wb_toend = len(wb_sig_fd[nb_dc:])  # 计算宽带频率点的最大长度
        len_tosel = min(nb_len_pos - 1, len_wb_toend)  # 取最小长度

        nb_sig_fd[1:1 + len_tosel] = wb_sig_fd[nb_dc:nb_dc + len_tosel]  # 如果去直流，就把中心频率点去掉，取窄带频率中心点位置+1 + 窄带频率长度/2
    else:
        len_wb_toend = len(wb_sig_fd[nb_dc - 1:])  # 计算宽带频率点的最大长度
        len_tosel = min(nb_len_pos, len_wb_toend)  # 取最小长度

        nb_sig_fd[:len_tosel] = wb_sig_fd[nb_dc - 1:nb_dc - 1 + len_tosel]  # 窄带频率中心点位置 + 窄带频率长度/2

    # 下半部分 [-fs/2, 0]+nb_dc
    if nb_dc > nb_len_neg:  # 长度足够
        len_wb_toend = len(wb_sig_fd[nb_dc - nb_len_neg - 1:])
        len_tosel = min(nb_len_neg, len_wb_toend)

        nb_sig_fd[nb_len_set - len_tosel:nb_len_set] = wb_sig_fd[nb_dc - len_tosel - 1:nb_dc - 1]  # 窄带频率中心点位置 - 窄带频率长度/2
    else:
        nb_len_neg_reg = nb_dc - 1  # 修正长度后的
        nb_sig_fd[nb_len_set - nb_len_neg_reg:nb_len_set] = wb_sig_fd[:nb_dc - 1]  # 窄带频率中心点位置 - 窄带频率长度/2

    ret = 1
    return ret, nb_sig_fd