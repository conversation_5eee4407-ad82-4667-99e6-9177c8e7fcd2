function [ret] = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs)
%  Function    ：IsValidNarrowBW
%  Description : 依据窄带信号分布范围是否在宽带信号分布范围以内，判断是否为有效的窄带信号
%  Parameter   : 
%           wb_fc       -- 宽带信号中心频率
%           wb_bw       -- 宽带信号带宽
%           wb_fs       -- 宽带信号采样率 
%           nb_fc       -- 窄带信号中心频率
%           nb_bw       -- 窄带信号带宽 
%           nb_fs       -- 窄带信号采样率 
%
%  Return      : 1:有效 -1:无效
%
%  Author      : Liuzhiguo
%  Date        : 2025-04-28
%
ret = 1;
if wb_bw<wb_fs
    %问题：如果宽带中心频点与窄带中心频点
    wb_bw_lb = wb_fc - wb_bw / 2;
    wb_bw_ub = wb_fc + wb_bw / 2;
    % 1. 计算宽带信号上下band
    wb_fs_lb = wb_fc - wb_fs / 2;%modified by lzg for bandwidth
    wb_fs_ub = wb_fc + wb_fs / 2;
else
    ret = -1;
    return;
end

% 2. 计算窄带信号上下band
scale_factor = 1.2;
nb_bw_lb = nb_fc - floor((nb_bw*scale_factor)/ 2);
nb_bw_ub = nb_fc + floor((nb_bw*scale_factor)/ 2);

nb_fs_lb = nb_fc - floor(nb_fs/ 2);
nb_fs_ub = nb_fc + floor(nb_fs/ 2);

%检查下边界 bw
if nb_bw_lb < wb_bw_lb
    ret = -1;
    warning("Error: nb_bw_lb=%.2f < wb_bw_lb=%.2f", nb_bw_lb, wb_bw_lb);
    return;
end
%检查上边界 bw
if nb_bw_ub > wb_bw_ub
    ret = -1;
    warning("Error: nb_bw_ub=%.2f > wb_bw_ub=%.2f", nb_bw_ub, wb_bw_ub);
    return;
end
%检查下边界 fs
if nb_fs_lb < wb_fs_lb
    ret = -1;
    warning("Error: nb_fs_lb=%.2f < wb_fs_lb=%.2f", nb_fs_lb, wb_fs_lb);
    return;
end
%检查上边界 fs
if nb_fs_ub > wb_fs_ub
    ret = -1;
    warning("Error: nb_fs_ub=%.2f > wb_fs_ub=%.2f", nb_fs_ub, wb_fs_ub);
    return;
end

end