# =======================================================================================================================
#   Function    ：Trans2OnxModel.py
#   Description : 转换pth模型到onnx格式
#                 并转换onnx到trt格式
# 
#   Parameter   : ../data/predict/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2025-02-18
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from nets.arcface import Arcface
from usrlib.usrlib import *
from usrlib.dataloader import *
import sys
print(sys.argv)
import getopt
import tensorrt as trt
from onnx.shape_inference import infer_shapes
from onnx import load_model, save_model
from usrlib.usrlib import compute_stft
from utils.onnx_helper import convert_onnx_to_engine

if __name__ == "__main__":
    #print(trt.__version__)
    modeltype = 0 #模型类别 0:分类 1:角向量
    input_shape     = 512*46 
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    dataset_file = "" # 数据文件路径
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    #1. 路径及模型参数
    #model_name = "Mtype1-ep100-loss0.000-val_loss2.476"
    model_name = "Mtype0-ep094-loss0.084-val_loss0.156" # fp32
    model_name = "Mtype0-ep094-loss0.084-val_loss0.161" # fp16
    model_name = "Mtype0-ep095-loss0.084-val_loss0.145" 
    model_path = "logs/{0}.pth".format(model_name)
    onnx_fname = 'model_data/{0}.onnx'.format(model_name)
    trt_fname = 'model_data/{0}.onnx'.format(model_name) #tensor rt filename

    batch_size = 64 #256  

    #2. 读取数据文件
    t0 = time.time()
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))

    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=0)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    
    # 定义动态轴
    # 对于输入，第 0 轴（批量大小）是动态的
    # 对于输出，第 0 轴（批量大小）也是动态的
    dynamic_axes = {
        'input': {0: 'batch_size'},  # 输入张量的第 0 轴是动态的，命名为 'batch_size'
        'output': {0: 'batch_size'}  # 输出张量的第 0 轴是动态的，命名为 'batch_size'
    }
    
    dummy_input     = torch.randn(1, input_shape, 2).cuda()
    dummy_input     = compute_stft(dummy_input)
    torch_out = torch.onnx.export(Model, 
                                  dummy_input, 
                                  onnx_fname, 
                                  input_names=['input'],  # 输入张量的名称
                                  output_names=['output'],  # 输出张量的名称
                                  dynamic_axes=dynamic_axes  # 指定动态轴
                                  )
    t3 = time.time()
    print("model trans to onnx：{:.2f}s".format(t3 - t2))

    #convert_onnx_to_engine(onnx_fname, trt_fname)
    
    #简化onnx模型 fp16
    #onnxsim.exe .\model_data\Mtype1-output0217.onnx  .\model_data\model_sim.onnx
    #./trtexec --onnx=data/resnet50/ResNet50.onnx --useDLACore=0 --fp16 --allowGPUFallback
    #trtexec --onnx=.\model_data\Mtype0-ep094-loss0.084-val_loss0.156.onnx  --fp16 --allowGPUFallback --saveEngine=.\model_data\Mtype0-ep094-loss0.084-val_loss0.156.trt
    #trtexec --onnx=.\model_data\Mtype0-ep095-loss0.084-val_loss0.145_modified.onnx  --fp16 --allowGPUFallback --saveEngine=.\model_data\Mtype0-ep095-loss0.084-val_loss0.145_modified.trt
    #trtexec --onnx=.\model_data\Mtype0-ep095-loss0.084-val_loss0.145.onnx  --fp16 --allowGPUFallback --saveEngine=.\model_data\Mtype0-ep095-loss0.084-val_loss0.145.trt

    #torch_out = torch.onnx.export(Model, dummy_input, output_onnx, export_params=True, verbose=False, keep_initializers_as_inputs=True,operator_export_type=torch.onnx.OperatorExportTypes.ONNX_ATEN_FALLBACK) # input_names=input_names, output_names=output_names
    #torch.onnx.errors.UnsupportedOperatorError: Exporting the operator 'aten::complex' to ONNX opset version 17
    # model=torch.onnx.load(output_onnx)
    # model=infer_shapes.infer_shapes(model)

    #---------------------------------------------------
    #import torch.quantization
    #使用torch.quantization.quantize()函数对模型进行量化
    #quantizedmodel = torch.quantization.quantizedynamic(model, {torch.nn.Linear: torch.nn.quantized.Linear})
    #prunedmodel = torch.quantization.prunemodel(model, pruning_method='l1', amount=0.5)

    
