function [demodBits, nextSampIndex] = NrzDemod(sig, nextSampIndex, numBit, sampsPerBit)
demodBits = zeros(numBit, 1);

for ibit = 1:numBit
    decisionMetric = sig(nextSampIndex);
    
    if decisionMetric > 0
        demodBits(ibit) = 1;
    else
        demodBits(ibit) = 0;
    end
    
    nextSampIndex = nextSampIndex + sampsPerBit;
    
    if nextSampIndex > length(sig)
        break;
    end
end
