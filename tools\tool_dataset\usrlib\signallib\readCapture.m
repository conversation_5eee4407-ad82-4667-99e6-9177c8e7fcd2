function [nb_signal , wb_signal, wb_fc, wb_fs]= readCapture(file,ischanneliz,nb_bandwidth,nb_samprate,nb_center_freq,isEngine)
%    function: readCapture
%    Params： file       ,    -- 信号文件路径
%             ischanneliz,    -- 是否提取窄带信号
%             nb_bandwidth,   -- 窄带信号带宽
%             nb_samprate,    -- 窄带信号采样率
%             nb_center_freq, -- 窄带信号中心频率列表
%              isEngine       -- 数据是否来自engine
%
% 1. 加载信号，并解析数据包中信息
if isEngine
    packet = load_sigcap(file);                   % 加载信号（注意：此时加载的信号为宽带信号）
    %参数信息
    packet.wb_fc = packet.header.center_freq;    % 信号中心频率
    packet.wb_bw = packet.header.bandwidth;       % 信号带宽
    packet.wb_fs = packet.header.samp_rate;     % 信号采样率
    packet.duration = length(packet.data)/packet.wb_fs;  %信号持续时间 单位：s
    %只取部分0-13ms
    clip_time = 600/1000;
    if clip_time < packet.duration
        len_point = floor(clip_time*packet.wb_fs);
        packet.signal = packet.data(1:len_point, 1);
        packet.duration = clip_time;
    else
        packet.signal = packet.data(:,1);             % 信号数据
    end

else
    rx = load(file);
    signal_i = rx(1:2:end);
    signal_q = rx(2:2:end);
    Cname = strsplit(file,'_');
    packet.device = Cname{1};
    packet.time = Cname{2};
    packet.wb_fc = str2double(regexp(Cname{3},'\d*\.?\d*','match'))*1e6;
    packet.wb_bw = str2double(regexp(Cname{4},'\d*\.?\d*','match'));
    packet.wb_fs = str2double(regexp(Cname{5},'\d*\.?\d*','match'));
    packet.len = str2double(regexp(Cname{6},'\d*\.?\d*','match'));
    packet.signal = complex(signal_i,signal_q).';
    packet.duration = packet.len/packet.wb_fs;
end

showWBSig(packet.signal, packet.wb_fc, packet.wb_fs);

% 3. 由用户提供的窄带信息（窄带中心频率、采样率、带宽等信息），进行窄带信号提取
sync_sig_td = [];
if(ischanneliz)
    %3.1 信号参数初始化
    % Initializations
    sig = packet.signal;                            %信号（I/Q）
    wb_fc = double(packet.wb_fc);                  %宽带信号中心频率
    wb_fs = double(packet.wb_fs);                 %宽带信号中心频率
    wb_bw = double(packet.wb_bw);                   %宽带信号带宽

    signal_list =  nb_center_freq;                  %窄带信号中心频率 [f0,f1...]
    det_duration = packet.duration;                 %窄带信号持续时间
    nb_chanlist = signal_list;                      %窄带信号列表
    nb_sigsamps = round(det_duration*nb_samprate);  %窄带信号采样点数 （按照 时间*采样率 折算后得到）
    nb_fs = nb_samprate;                            %窄带信号采样率
    nb_bw = nb_bandwidth;                           %窄带信号带宽
    nb_cfo = 0;                                     %载波频偏
    n_chans_scanned = 0;                            %窄带信号带宽
    nb_n_sig = nb_sigsamps;                         %窄带信号采样点数
    wb_n_sig = round(nb_n_sig * (wb_fs / nb_fs));   %折算到宽带信号采样点数
    sync_n_sig = nb_n_sig;                          %窄带信号采样点数
    ch_list = nb_chanlist;                          %窄带信号列表
    n_chans = length(ch_list);                      %窄带信号个数
    pow_norm_factor = 1/ ((wb_n_sig));              % 1/折算到宽带信号采样点数

    % Convert time-domain signal to frequency domain
    wb_sig_fd = fft(sig, wb_n_sig);
    %wb_sig_fd = circshift(wb_sig_fd, floor(length(wb_sig_fd)/2));   %宽频域带信号
    wb_sig_fd = circshift(wb_sig_fd, length(wb_sig_fd)/2);   %宽频域带信号
    nb_signal = [];
    %3.2 根据信号个数，对信号进行窄带化处理（提取窄带信号，并转换到时域）
    for i_chan =1:n_chans
        nb_fc = ch_list(i_chan) + nb_cfo;                    %窄带信号中心频率
        % 3.2.1 提取窄带信号，并完成下变频
        % [ret, nb_sig_fd] = ExtractNarrowbandSignal(...
        %     wb_sig_fd, wb_fc, wb_bw, wb_fs, nb_n_sig, nb_fc, nb_fs); %为了判别方便修改 by liuzhiguo
        [ret, nb_sig_fd] = ExtractNarrowbandSignal(...
            wb_sig_fd, wb_fc, wb_bw, wb_fs, nb_n_sig, nb_fc, nb_bw, nb_fs, 1); %提取窄带信号
        if ret == -1
            continue;
        elseif ret == 1
            continue;
        else
            n_chans_scanned = n_chans_scanned + 1;
        end

        % 3.2.2 信号转换到时域
        nb_sig_td = ifft(nb_sig_fd) * nb_n_sig;     % 将信号转换到时域
        nb_sig_td = nb_sig_td * pow_norm_factor;    % 将信号功率归一化
        sync_sig_td = nb_sig_td(1:sync_n_sig, 1);   % 截取信号

        showNBSig(i_chan, sync_sig_td, nb_fc, nb_fs) %显示窄带信号

        % （3）窄带信号输出
        nb_signal = [nb_signal sync_sig_td];
    end

else
    nb_signal = [];
end
wb_signal = packet.signal;%原始信号
end