%    function    : cmdGenDatasetPar.m
%    description : 生成数据集文件（并行化方式）

clear;
close all;

IncludedPaths; %统一引入路径
InitMyParams;  %统一定义变量

%% 基本数据集
maxclip_ms = 6;%好像与3ms差距不大

% 并行化生成base路径
if exist(myconfig.folder_outputDS,"dir")>0 && exist(myconfig.fpath_labeldef,"file")>0
    fprintf("[启动数据集创建] 输出路径为:%s\n", myconfig.folder_outputDS);
else
    error("文件：%s 不存在！", myconfig.fpath_labeldef);
end

Table_lbldef = readtable(myconfig.fpath_labeldef,'Format','%s %s %s %f %d','Delimiter',';');
Table_lbldef.Properties.VariableNames = ["foldername","meta_subtype","clsname","duration_ms","forwardoffset"];

% 启动并行池（如果还没有启动）
if ~isempty(gcp('nocreate'))
    delete(gcp('nocreate'));
end

% 获取CPU核心数并限制工作进程数量
maxWorkers = feature('numCores');
workerCount = max(1, floor(maxWorkers * 0.7));  % 使用70%的核心
fprintf('启动并行池，使用 %d 个工作进程（总核心数: %d）\n', workerCount, maxWorkers);

parpool('local', workerCount);

%优化为了parfor快速执行
foldernames = Table_lbldef(:,1).foldername;
meta_subtypes = Table_lbldef(:,2).meta_subtype;
clsnames = Table_lbldef(:,3).clsname;
duration_mss = Table_lbldef(:,4).duration_ms;
forwardoffsets = Table_lbldef(:,5).forwardoffset;
% 生成base文件夹
nRows = height(Table_lbldef);
% 启动计时器
tic;

parfor iRow = 1 : nRows
    srcFolder = fullfile(myconfig.folder_labeleddata, foldernames(iRow));
    meta_subtype = strjoin(meta_subtypes(iRow));
    clsname = strjoin(clsnames(iRow));
    duration_ms = duration_mss(iRow);
    forwardoffset = forwardoffsets(iRow);

    fname_class = myconfig.fpath_classdef;
    destFolder_base = fullfile(myconfig.folder_outputDS,"base",clsname);
    destFolder_base_noise=fullfile(myconfig.folder_outputDS,"base_noised",clsname);
    destFolder_base_augment= fullfile(myconfig.folder_outputDS,"base_augment",clsname);
    destFolder_noised = fullfile(myconfig.folder_outputDS,"noised",clsname);
    destFolder_aug = fullfile(myconfig.folder_outputDS,"augment",clsname);
    
    fname_recs1 = fullfile(srcFolder, "base.txt");
    fname_recs2 = fullfile(srcFolder, "noised.txt");
    fname_recs3 = fullfile(srcFolder, "augment.txt");
   

    %1 写入base文件
    ensureFolder(destFolder_base);
    ensureFolder(destFolder_base_noise);
    ensureFolder(destFolder_base_augment);

    WriteHdfsbase_nbr(srcFolder, destFolder_base,maxclip_ms,fname_class,fname_recs1);
    WriteHdfsbase_nbr(srcFolder, destFolder_base_noise,maxclip_ms,fname_class,fname_recs2);
    WriteHdfsbase_nbr(srcFolder, destFolder_base_augment,maxclip_ms,fname_class,fname_recs3);

    %2 写入噪声文件
    sinFile_list = fullfile(destFolder_base_noise,'wfile_list.txt');
    ensureFolder(destFolder_noised);
    gennoisedDS_bylistr(sinFile_list,destFolder_noised);

    %3 写入增强文件
    ensureFolder(destFolder_aug);
    WriteAugment_nbr(srcFolder,destFolder_aug,maxclip_ms,fname_class,fname_recs3);
end

% 生成base文件夹
% 停止计时器并获取经过的时间
elapsedTime = toc;
disp(['程序执行时间: ', num2str(round(elapsedTime/60,2),'%.2f'), '分']);

% 关闭并行池
delete(gcp);


function []=ensureFolder(destFolder)
%确认文件夹存在且为空
if exist(destFolder,"dir")>0
    % fprintf("文件夹:%s 已存在\n", destFolder);
    % x = input('如删除，请按1，不理会，按Enter\n');
    % if x==1
    %     rmfolderfiles(destFolder); %删除文件夹中数据
    %     % break;
    % end
    rmfolderfiles(destFolder); %删除文件夹中数据
else
    mkdir(destFolder);
end
end

function []=rmfolderfiles(folderPath)
% 获取文件夹下所有文件和文件夹信息
fileList = dir(fullfile(folderPath, '*'));

% 遍历文件列表
for i = 1:length(fileList)
    % 排除当前目录和上级目录
    if ~strcmp(fileList(i).name, '.') && ~strcmp(fileList(i).name, '..')
        % 构建文件的完整路径
        filePath = fullfile(folderPath, fileList(i).name);

        % 判断是否为文件
        if fileList(i).isdir
            % 如果是文件夹，可以选择递归删除或者跳过
            % 这里选择跳过，如果需要递归删除，可以添加相应的递归函数
            continue;
        else
            % 删除文件
            delete(filePath);
        end
    end
end
end