function [label] = calunionregion(psd_value,wb_signal,t,f,delta_f,fft_overlap,wb_fc,bshowdgraph)
%从二值图像 BW 中删除少于 P 个像素的所有连通分量（对象）
P=200*1;
psd_value = bwareaopen(psd_value, P) ; % 根据[t,f]二值图,计算联通区域
[L, NUM] = bwlabel(psd_value, 8);      % 根据联通区域，标识各个区域的标签


label.left = 1e6;
label.right = 0;
label.up = 1e6;
label.down = 0;
label.valid = 1;
label.centor_freq = 0;
label.start_idx = 0;
label.stop_idx = 0;
label.bw = 0;

%1 对于行列图，取不为0的行列，标记其具体位置信息，根据联通区域NUM，对应到标签label
for col=1:length(L(1,:))  %统计的是列数
    for row = 1:length(L(:,1))%行数
        index = L(row,col);
        if(index>0) %非背景标签
            if(length(label)< index)
                label(index).left = 1e6;
                label(index).right = 0;
                label(index).up = 1e6;
                label(index).down = 0;
                label(index).valid = 1;
            end

            %列数可以判定信号的横向
            if(label(index).left>col) %待划分区域的左边大于列
                label(index).left = col;
            end
            if(label(index).right<col)%待划分区域的左边大于列数
                label(index).right = col;
            end
            %行数可以判定信号的纵向范围
            if(label(index).up>row)
                label(index).up = row;
            end
            if(label(index).down<row)
                label(index).down = row;
            end
        end
    end

end

len = length(label);
limit_val = 0.8;%区域中1的比例门槛，如500个点，大于80%的点为1，则判定为联通区域
%先确定所有带标签连通区域的上下边界，将整个纵向范围分为两部分，上半部分查找上边界。
for n=1:len
    up_flg = 0;
    %从上向下找，找能量满足0.8的行作为顶端
    for m=label(n).up:round((label(n).down-label(n).up)/2)+label(n).up %列-顶部
        % /n是因为每个连通区域的标签值为n,求均值后进行归一化
        mean_l = mean(L(m,label(n).left:label(n).right))/n; %每行
        if(mean_l>limit_val)
            label(n).up = m;
            up_flg=1;
            break;
        end
    end
    if(up_flg == 0)
        label(n).valid=0;
    end
    down_flg = 0;
    %从下向上找 找出能量满足0.8的行，作为底界，从下找一半。
    for m=label(n).down:-1:label(n).down-round((label(n).down-label(n).up)/2)%列-底部
        mean_l = mean(L(m,label(n).left:label(n).right))/n;%每行
        if(mean_l>limit_val)
            label(n).down = m;
            down_flg = 1;
            break;
        end
    end
    if(down_flg == 0)
        label(n).valid=0;
    end

end
% len = length(label);
for n=1:len
    left_flg = 0;
    for m=label(n).left:round((label(n).right-label(n).left)/2)+label(n).left %每行-左侧
        mean_l = mean(L(label(n).up:label(n).down,m))/n; %列
        if(mean_l>limit_val)
            label(n).left = m;
            left_flg = 1;
            break;
        end
    end
    if(left_flg == 0)
        label(n).valid=0;
    end
    right_flg = 0;
    for m=label(n).right:-1:label(n).right-round((label(n).right-label(n).left)/2) %每行-右侧
        mean_l = mean(L(label(n).up:label(n).down,m))/n;%列
        if(mean_l>limit_val)
            label(n).right = m;
            right_flg = 1;
            break;
        end
    end
    if(right_flg == 0)
        label(n).valid=0;
    end
end

%重新给扩大部分标记标号(1，2) +1...，显示边框，便于观察
for n=1:length(label)
    if(label(n).valid)
        %上边框
        L(label(n).up:label(n).up,label(n).left:label(n).right)    = length(label)+1;
        %下边框
        L(label(n).down:label(n).down,label(n).left:label(n).right)  = length(label)+1;
        %左边框
        L(label(n).up:label(n).down,label(n).left)     = length(label)+1;
        %右边框
        L(label(n).up:label(n).down,label(n).right)    = length(label)+1;
    end
    % imshow(L)
end


%计算中心频率及带宽
for n=1:length(label)
    % if(label(n).valid)
        %在时频图切片信号中心做标记
        y = round((label(n).down-label(n).up)/2)+label(n).up;
        x = round((label(n).right-label(n).left)/2)+label(n).left;
        L(y-1:y+1,x-1:x+1) = length(label);%标记中心位置

        label(n).bw = (label(n).down-label(n).up)*delta_f;   % 带宽估计=频率范围*频率分辨率
        label(n).centor_freq = (y-length(f)/2)*delta_f+wb_fc;% 中心频率估计
        label(n).start_idx = label(n).left*fft_overlap - fft_overlap;
        if(label(n).start_idx - fft_overlap <0)
            label(n).start_idx = 0;
        end
        label(n).stop_idx  = label(n).right*fft_overlap + fft_overlap;
        if(label(n).stop_idx > length(wb_signal))
            label(n).stop_idx = length(wb_signal);
        end
% 
    % end
end

if bshowdgraph
    figure(118)
    subplot(212)
    surf(t,f,L,'edgecolor','none'); axis tight;title('联通图处理后的功率谱密度 3D');
    view(0,90);
end

end