import os
#dataset_file = "E:\\project\\tool_dataset\\outdataset\\train\\base\\nb-dataset-S1.hdf5"   #已训练过的数据集
#dataset_file = "E:\\project\\tool_dataset\\outdataset\\test\\sam\\nb-test433-S1.hdf5"      #采集数据集
dataset_file = "E:\\project\\tool_dataset\\outdataset\\test\\sam\\nb-flyfrsky-S1.hdf5"      #采集数据集
#dataset_file = "E:\\project\\tool_dataset\\outdataset\\test\\gendataset\\nb-gendataset-S1.hdf5" #matlab生成数据集
os.system('python predictClass.py -p {0} -m {1}'.format(dataset_file, 0)) #python 训练文件调用 模型类别 0:分类 1:角向量

#信号分类
#python predictClass.py -p E:\project\tool_dataset\outdataset\test\sam\nb-test433-odoor-S1.hdf5 -m DroneSigCls.pth
#python predictClass.py -p E:\project\tool_dataset\outdataset\test\sam\fchanscan-S1.hdf5 -m DroneSigCls.pth
#python predictClass.py -p E:\project\tool_dataset\outdataset\train\base\nb-dataset-val-S1.hdf5 -m DroneSigCls.pth
#python predictClass.py -p E:\project\tool_dataset\outdataset\train\augment\nb-dataset-val-dbw-S1.hdf5 -m DroneSigCls.pth
#python predictClass.py -p E:\project\tool_dataset\outdataset\train\augment\nb-dataset-val-dfc-S1.hdf5 -m DroneSigCls.pth
#python predictClass.py -p E:\project\tool_dataset\outdataset\test\sam\nb-test433-1126-S1.hdf5 -m DroneSigCls.pth

#预测指纹相似度
#python PredictSimVector.py -p E:\project\tool_dataset\outdataset\train\base\nb-dataset-val-S1.hdf5 -m DroneSigArc.pth
#python PredictSimVector.py -p E:\\project\\tool_dataset\\outdataset\\test\\sam\\nb-test433-odoor-S1.hdf5 -m DroneSigArc.pth
#python PredictSimVector.py -p E:\project\tool_dataset\outdataset\test\sam\fchanscan-S1.hdf5 -m DroneSigArc.pth

#生成无人机指纹数据库
# 

#训练模型
#python ModelTrain-TV.py -m 0
#python ModelTrain-TV.py -m 1

#查看数据库结构
#python AddVectorDbByHdfs.py
#python summary.py