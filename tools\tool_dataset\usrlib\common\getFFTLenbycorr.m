function [fftlen] = getFFTLenbycorr(signal_in, bShowfigure)
%  Function    ：showselfcorr
%  Description : 计算相关值，并显示图表
%                主要目的为：在cp未知时，确定FFT长度
%                原理：相关峰曲线判别法，采用整体序列自相关，会出现两个相关峰，
%                     第1个相关峰为自相关，第2个相关峰为cp相关，利用两峰间的差值确定FFT长度
%                如：时域相关峰值依次为：1，1025，1097，2193 （(1024+72)*2+1）
%
%  Parameter   : signal_in    -- 复信号
%  output      : corrseq_len  -- 计算的相关长度值
%
%  Author      : Liuzhiguo
%  Date        : 2024-07-01
%
corrseq_len = 2048*2;%2048为最大可能FFT长度
offset = 0;
[corr_self] = getselfcorrvals(signal_in(1+offset:end), corrseq_len);

Nsearch = 20;
[fv,findex] = maxk(corr_self,Nsearch);
for i=1:Nsearch
    fftlen = findex(i)-findex(1);
    if fftlen >= 128
        break;
    end
end
fprintf('fftlen is %d',fftlen);

if bShowfigure==true
    figure;%生成新的图窗
    plot(corr_self);
    xlabel('相关点坐标');
    ylabel('相关值');
    title('自相关峰值');
end
end

