function [sig_baseband, fs, fc, bw] = GenBBSig_nb433()
%生成NB433基带数据

%帧结构：
%
% | 5B Preamble| + | 2B Sync| + payload (20B) + | 1B 0xCC |
% payload = | 2B header | + | 1B Len |+ | Data | + | 2B CRC|
% crc = crc16(| 2B header | + | 1B Len |+ | Data |)
% len = length(Data)
% white xor (payload)
% Data = | 5B Preamble| + | 2B Sync| + white(payload) + | 1B 0xCC |
% 大端模式:先高位后低，如 2D 表示为:0 0 1 0 1 1 0 1
%

%% 1 设置参数
bitrate =64e3; %码速率
spb=8*4;         %每个码 8个采样点， SamplesPerSymbol
fs = bitrate * spb; %采样率
fc = 433e6;     % 载波频率
bw = 256e3;

%numPreambleHalfBytes = 8*2;
numPreambleBytes =8;
numSyncBytes = 2;
numHeaderBytes = 2;
numLenthBytes = 1;
numPayloadBytes =31;

numPreambleBits = 8 * numPreambleBytes;
numSyncBits = 8 * numSyncBytes;
numHeaderBits = 8 * numHeaderBytes;
numLenthBits = 8 * numLenthBytes;
numPayloadBits = 8 * numPayloadBytes;

% preambleBits = [de2bi(hex2dec(['5555']),numPreambleBits/4, 'left-msb')...
%     de2bi(hex2dec(['5555']),numPreambleBits/4, 'left-msb')...
%     de2bi(hex2dec(['5555']),numPreambleBits/4, 'left-msb')...
%     de2bi(hex2dec(['5555']),numPreambleBits/4, 'left-msb')...
%     de2bi(hex2dec(['5555']),numPreambleBits/4, 'left-msb')];

preambleBits = zeros(1,numPreambleBits);
for i = 1: numPreambleBytes
    preambleBits(1+(i-1)*8:i*8) = de2bi(hex2dec('55'),8, 'left-msb');
end
preambleBits = preambleBits';

syncBits = de2bi(hex2dec('2DD4'),numSyncBits, 'left-msb')';
headerBits = de2bi(hex2dec('0019'),numHeaderBits, 'left-msb')';

packagetype = 1;%包类型  0：default，1：len=33，2：len=2 

if packagetype==0
    % payloadBits =[headerBits;LengthBits;[randi([0, 1], numPayloadBits, 1)]] ;
    LengthBits = de2bi(hex2dec('11'),numLenthBits, 'left-msb')';
    payloadBits =[headerBits;LengthBits;
        de2bi(hex2dec('FE'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('09'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('10'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('FF'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('BE'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('00'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('00'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('00'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('00'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('00'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('06'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('08'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('C0'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('04'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('03'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('47'),numLenthBits, 'left-msb')';...
        de2bi(hex2dec('CC'),numLenthBits, 'left-msb')'];
elseif packagetype==1
    %254 3 26 1 1  165 113 19 0 249 217 254 12 27 1 1 2 206 245
    % 111 170 162 31 1 0 84 245 17 0 216 214 133 0
    LengthBits = de2bi(33, numLenthBits, 'left-msb')';
    payloadBits =[LengthBits;
        de2bi(254,numLenthBits, 'left-msb')';...
        de2bi(3,numLenthBits, 'left-msb')';...
        de2bi(26,numLenthBits, 'left-msb')';...
        de2bi(1,numLenthBits, 'left-msb')';...
        de2bi(1,numLenthBits, 'left-msb')';...
        de2bi(165,numLenthBits, 'left-msb')';...
        de2bi(113,numLenthBits, 'left-msb')';...
        de2bi(19,numLenthBits, 'left-msb')';...
        de2bi(0,numLenthBits, 'left-msb')';...
        de2bi(249,numLenthBits, 'left-msb')';...
        de2bi(217,numLenthBits, 'left-msb')';...
        de2bi(254,numLenthBits, 'left-msb')';...
        de2bi(12,numLenthBits, 'left-msb')';...
        de2bi(27,numLenthBits, 'left-msb')';...
        de2bi(1,numLenthBits, 'left-msb')';...
        de2bi(1,numLenthBits, 'left-msb')';...
        de2bi(2,numLenthBits, 'left-msb')';...
        de2bi(206,numLenthBits, 'left-msb')';...
        de2bi(245,numLenthBits, 'left-msb')';...
        de2bi(111,numLenthBits, 'left-msb')';...
        de2bi(170,numLenthBits, 'left-msb')';...
        de2bi(162,numLenthBits, 'left-msb')';...
        de2bi(31,numLenthBits, 'left-msb')';...
        de2bi(1,numLenthBits, 'left-msb')';...
        de2bi(0,numLenthBits, 'left-msb')';...
        de2bi(84,numLenthBits, 'left-msb')';...
        de2bi(245,numLenthBits, 'left-msb')';...
        de2bi(17,numLenthBits, 'left-msb')';...
        de2bi(0,numLenthBits, 'left-msb')';...
        de2bi(216,numLenthBits, 'left-msb')';...
        de2bi(214,numLenthBits, 'left-msb')';...
        de2bi(133,numLenthBits, 'left-msb')';...
        de2bi(0,numLenthBits, 'left-msb')'...
        ];
elseif packagetype==2
    LengthBits = de2bi(2, numLenthBits, 'left-msb')';
    payloadBits =[LengthBits;
        de2bi(56,numLenthBits, 'left-msb')';...
        de2bi(1,numLenthBits, 'left-msb')';...
        ];
end

%% CRC编码
[crcBits]=crc_nb433(payloadBits);
crchex=dec2hex(bi2de(reshape(crcBits',8,length(crcBits)/8)', 'left-msb'));

%% 白化
[white_bits] = scramble_nb433(crcBits);
whitehex=dec2hex(bi2de(reshape(white_bits',8,length(white_bits)/8)', 'left-msb'));

%% 合成数据
packetBits = [preambleBits; syncBits; white_bits];
len_tx = length(packetBits)/8;
data_tx=bi2de(reshape(packetBits',8,len_tx)', 'left-msb');
fprintf("发送数据: %d bytes\n", len_tx);
fprintf('%02X ',data_tx)
fprintf('\n');
%% GFSK调制
sig_baseband = gfskModulator(packetBits,spb);

end

%GFSK调制
function y = gfskModulator(x,sps)
persistent mod meanM
if isempty(mod)
    M = 2;
    mod = comm.CPMModulator(...
        'ModulationOrder', M, ...
        'FrequencyPulse', 'Gaussian', ...
        'BandwidthTimeProduct', 0.5, ...
        'ModulationIndex', 1, ...
        'SamplesPerSymbol', sps);
    % mod = comm.GMSKModulator( ...
    % BitInput=false, ...
    % PulseLength=1, ...
    % SamplesPerSymbol=sps);

    % mod = comm.CPMModulator(...
    %     'ModulationOrder', M, ...
    %     'FrequencyPulse', 'Gaussian', ...
    %     'BandwidthTimeProduct', 0.5, ...
    %     'ModulationIndex', 1, ...
    %     'SamplesPerSymbol', sps);

    meanM = mean(0:M-1);
end
y = mod(2*(x-meanM));
end



