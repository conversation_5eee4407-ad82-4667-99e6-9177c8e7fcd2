function [rd_sig,class_id,class_name,fc,fs,bw] = RdTrainSig(fname_rd)
%  Function    ：RdTrainSig
%  Description : 读取训练数据
%  Parameter   : fname_rd       -- 写入文件名称
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%                fc             -- 中心频率
%                fs             -- 采样率
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13
if exist(fname_rd,"file")>0
    [~,~,ext] = fileparts(fname_rd);
    if ext == ".wav"
        [rd_sig, fs] = audioread(fname_rd);
        rd_sig = rd_sig';%(60000,2)-->(2,60000)
        info = audioinfo(fname_rd);
        class_name = info.Title;
        class_id = str2double(info.Artist);
        comments = split(info.Comment,",");%类似"nb_bw=10,fc=20"
        nb_bws = split(comments(1),"=");
        bw = str2double(nb_bws(2));
        fcs = split(comments(2),"=");
        fc = str2double(fcs(2));
    else %hdf5
        rd_sig     = h5read(fname_rd,"/rx_signal");
        class_id   = h5read(fname_rd,"/class_id");
        class_name = h5read(fname_rd,"/class_name");
        fc = h5read(fname_rd,"/fc");
        fs = h5read(fname_rd,"/fs");
        bw = h5read(fname_rd,"/bw");
    end
else
    error('%s 不存在！\n',fname_rd);
end

end

