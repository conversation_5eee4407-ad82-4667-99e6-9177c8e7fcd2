function []=gennoisedDS_bylistr(sinFile_list,outpath)
%  Function    ：gennoisedDS_bylistr
%  Description : 根据列表生成噪声数据集
%  Parameter   : sinFile_list       -- 列表文件名称 如Train_ds_gen_list.txt
%                outpath            -- 散列文件路径 如".\outdataset\train-files\noised\"
%  Author      : Liuzhiguo
%  Date        : 2024-12-27
% addpath(fullfile(pwd, 'usrlib'));  % 添加 usrlib 目录及其所有子目录
parentDir = fullfile(pwd, '..');
% 将上一级目录添加到搜索路径
addpath(parentDir);
% % 调用init函数
InitMyParams;
noised_arrays = myconfig.noised_arrays;
 % noised_arrays=3:3:12;
[~,listfile_name,listfile_ext] = fileparts(sinFile_list);
lines = readlines(sinFile_list);
for i=1:length(lines)%列表文件
    line_rec = lines(i);
    if line_rec==""
        continue;
    end
    strPaths = split(line_rec,";");
    fname_indataset = strPaths(2);%hdf文件名称
    lsfilename = strcat(listfile_name,listfile_ext);
    GenNoisedDS_MF(fname_indataset,outpath,lsfilename,noised_arrays);%生成单个噪声数据文件
end
end

function [] = GenNoisedDS_MF(fname_indataset,outpath,lsfilename,noised_arrays)

[filepathin,name,ext] = fileparts(fname_indataset);
% dir_1 = getlastfolder(filepathin, 1);
% dir_2 = getlastfolder(outpath, 0);

%filepathout = strrep(filepathin,'base','noised');
filepathout = outpath;

if exist(filepathout,"dir")<=0
    mkdir(filepathout)
end
%4 添加噪声干扰
rowpointer = 0;
[rd_sig,class_id,class_name, arrayfc, arrayfs, arraybw] = RdTrainSig(fname_indataset);

for idx = 1:length(noised_arrays)
    snr = noised_arrays(idx);%3:3:12
    fprintf("生成[%s]噪声数据：snr=%.2f\n",class_name, snr);

    %fname_outdataset = strrep(fname_indataset,'base','noised');
    sPack = sprintf("SNR%d",snr);
    fname_outdataset = fullfile(filepathout,strcat(name,'-',sPack,ext));

    for iRow = 1 : length(class_id)
        cls_id = class_id(1,iRow);
        cls_name = class_name(1,iRow);
        fc = arrayfc(1,iRow);
        fs = arrayfs(1,iRow);
        bw = arraybw(1,iRow);

        signal = rd_sig(1,:,iRow)+1j*rd_sig(2,:, iRow);
        wb_rxSig_clip = awgn(signal, snr, 'measured');
        rowpointer = rowpointer+1;
        %WrTrainSig(fname_outdataset, wb_rxSig_clip, cls_id,cls_name, fc, fs, bw, 1);  % 生成数据集，hdf文件格式
        scomment = sprintf("nb_bw=%d,fc=%d",bw,fc);%写入audio文件
        audiowrite(fname_outdataset, [real(wb_rxSig_clip)' imag(wb_rxSig_clip)'],floor(fs),'BitsPerSample',32,'Title',cls_name,'Artist',string(cls_id),'Comment',scomment);

        strline = strcat(int2str(cls_id),";",fname_outdataset);
        sOutfilename_list = fullfile(outpath,lsfilename);
        if exist(sOutfilename_list,"file")<=0
            writelines(strline, sOutfilename_list,'WriteMode','overwrite');
        else
            writelines(strline, sOutfilename_list,'WriteMode','append');
        end
    end

    %current_dir = pwd;%转换到绝对路径
    % absolute_path = fullfile(current_dir, fname_dataset);
end
%DispDatasetRecByChart(fname_dataset_noised,1,"噪声");
end

function [thedir] = getlastfolder(filepathin, offset)
basedirs = split(filepathin,'\');
index=length(basedirs);
index = index - offset;%上面第几个
if basedirs(index)~=""
    thedir = basedirs(index);
else
    thedir = basedirs(index-1);
end
end