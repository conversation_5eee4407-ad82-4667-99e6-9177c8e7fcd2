%  Function    ：WriteHdfsnoise
%  Description : 写入噪声训练数据
%  Parameter   : fname_rd       -- 写入文件名称
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13

% 1. 初始化命令
clc
clear
close all

addpath("lib");             %库函数路径
addpath("usrlib\common");   %用户自定义路径
addpath("usrlib\wb_evo2");  %用户自定义路径

% 2. 文件读入
% 2.1 文件目录读取
inpath = ".\outdataset\base\";
inname = "nb-dataset";
cls_size = 'S1';

fname_indslist = dir(strcat(inpath,inname,'-*','.hdf5'));

for i=1:length(fname_indslist)
    fname_indataset = strcat(fname_indslist(i).folder,'\',fname_indslist(i).name);
    disp(strcat("--------输入文件:",fname_indataset,"--------"));
    GenMultiPathDS(fname_indataset);
end

function [] = GenMultiPathDS(fname_indataset)
%5 添加多径干扰
%v = 50;         %  移动速度(m/s)
rowpointer = 0;
for v = [0:25]   % max 90km/h
    fprintf("生成多径数据：v=%.2f\r\n",v);
    %fc = 2.4e9;                % 载波频率
    c = 3e8;                    % 光速
    % fd = double(v)*double(fc)/c;
    delay = [0 0.1 0.2 0.3 0.4 0.5]*1e-6;  % 时延 (s)
    gain = [0 -4 -8 -12 -16 -20];  % 路径增益


    [rd_sig,class_id,class_name, arrayfc, arrayfs] = RdTrainSig(fname_indataset);
    fname_outdataset = strrep(fname_indataset,'base','multipath');
    for iRow = 1 : length(class_id)
        cls_id = class_id(1,iRow);
        cls_name = class_name(1,iRow);
        fc = arrayfc(1,iRow);
        fs = arrayfs(1,iRow);

        fd = double(v)*double(fc)/c;
        ch_multipath = comm.RayleighChannel('PathDelays', delay, 'AveragePathGains', gain,...
            'NormalizePathGains',true,'MaximumDopplerShift',fd,'SampleRate',double(fs));

        % ch_multipath = comm.RicianChannel( ...
        %     SampleRate=double(fs), ...
        %     PathDelays=delay, ...
        %     AveragePathGains=gain, ...
        %     KFactor=3, ...
        %     MaximumDopplerShift=fd, ...
        %     ChannelFiltering=false);

        signal = rd_sig(1,:,iRow)+1j*rd_sig(2,:, iRow);
        wb_rxSig_clip = ch_multipath(signal.');%添加多径信息
        wb_rxSig_clip = wb_rxSig_clip.';
        rowpointer = rowpointer+1;
        WrTrainSig(fname_outdataset, wb_rxSig_clip, cls_id,cls_name, fc, fs,rowpointer);  % 生成数据集，hdf文件格式
    end
end
%DispDatasetRecByChart(fname_dataset_mpath,1,"多径");

end

