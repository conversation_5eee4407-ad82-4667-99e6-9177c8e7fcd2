function [outbytes_dewhite] = demodnb433_bybin(bindata)
%判断同步起始位置
sPos = 0;
len_tx = floor(length(bindata)/8);
outbytes_org=bi2de(reshape(bindata(1:len_tx*8)',8,len_tx)', 'left-msb');
% fprintf('\n收到信号:%d bytes\n', length(outbytes_org));
% fprintf('%02X ',outbytes_org);

for i=1:len_tx
    if outbytes_org(i)==hex2dec('55')
        sPos=sPos+1;
    else
        break;
    end
end

sPos_Pack = (sPos+2)*8+1;%preamble+syncword(2)
%ePos_Pack = (len_tx-1)*8;%尾部为crc
ePos_Pack = (len_tx)*8;
if ePos_Pack-sPos_Pack+1<2*8+1 %短帧
    ePos_Pack = (len_tx)*8;%
end
bodyBits = bindata(sPos_Pack:ePos_Pack);

% 解白化
[white_bits] = scramble_nb433(bodyBits');%输入为列向量
%outbytes_dewhite = bi2de(reshape(white_bits',8,length(white_bits)/8)', 'left-msb');
outbytes_dewhite = bit2int(white_bits,8);
% whitehex=dec2hex(outbytes_dewhite);
% disp(whitehex);
fprintf('\n解白化后信号: %d bytes\n', length(outbytes_dewhite));
fprintf('%02X ',outbytes_dewhite');%转换为行向量
%% CRC校验
if length(white_bits)>3*8
    payloadBits = white_bits(1:end-2*8);
    [crcBits]=crc_nb433(payloadBits);
    % crchex=dec2hex(bi2de(reshape(crcBits',8,length(crcBits)/8)', 'left-msb'));
    % disp(crchex);

    outbytes_crc = bi2de(reshape(crcBits',8,length(crcBits)/8)', 'left-msb');
    fprintf('\n解CRC后信号:%d bytes\n', length(outbytes_crc));
    fprintf('%02X ',outbytes_crc);
 end

end