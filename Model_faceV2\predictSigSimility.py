# =======================================================================================================================
#   Function    ：predictSigSimility.py
#   Description : 模型预测推理：信号向量相似度计算
#                 传入列表文件路径，并对modelTrain.py调用
#                 调用方式: 
#   Parameter   : "E:\\lz_signaldB\\datafiles\\PreprocessData\\2025-04-29\\base\\Test_ds_gen.txt"
#   Author      : Liuzhiguo
#   Date        : 2024-08-27
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from usrlib.VectorToVectorSim import *
from usrlib.usrlib import *
import sys
#print(sys.argv)
import getopt
from nets.arcface import Arcface
from utils.dataloader import SigNbDataset, LSWDataset, SigNbDataset_collate
from torch.utils.data import DataLoader

if __name__ == "__main__":
    
    modeltype = 1 #模型类别 0:分类 1:角向量
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, server_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    dataset_file = ""

    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    # 命令行方式获取测试文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['modelname=','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modelname'):
            fname_model = opt_value
            print("The modelname is ", fname_model)
        elif opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something


    #1. 路径及模型参数
    model_path = "logs/Mtype1-ep096-loss0.000-val_loss0.000-0521.pth" #64 acc= 100%

    batch_size = 64 #256  

    # 2. 读取数据文件
    t0 = time.time()

    if dataset_file=="":
        dataset_file = annotation_path_test
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))

    #wav文件格式数据集
    lines_test   = read_dataset_lines(dataset_file, ['base'])
    np.random.seed(10101)
    np.random.shuffle(lines_test)
    np.random.seed(None)
    test_dataset = SigNbDataset(lines_test)
    test_loader  = DataLoader(test_dataset, batch_size=batch_size,  collate_fn=SigNbDataset_collate)

    
    if sys.platform.startswith('win'):
        print("当前系统是Windows系统")
    if server_path!=windows_path_local: # 判断windows路径和本地windows路径是否相同
        windows_to_local_path(server_path, windows_path_local, lines_test)
    elif sys.platform.startswith('linux'):
        print("当前系统是Linux系统") #路径转换
        windows_to_linux_path(server_path, linux_path, lines_test)
    else:
        print("当前系统不是Windows也不是Linux系统")  


    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    # 3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))



    # 4.抽取特征向量
    output_all = []
    classid_gt = []
    with torch.no_grad():
        for idx, (data, labels)  in enumerate(test_loader):
            y = data
            y = y.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput)
            classid_gt.append(labels)
            break #只取batchsize个数据
    
    output_all = torch.cat(output_all, dim=0)
    classid_gt = torch.cat(classid_gt, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    # 5. 取数据库中特征向量
    (vectors, clsids, clsnames, filepaths, curfs, curbw) = GetArcVectorDB()

    VectorSim = VectorSimilarity()
    if len(output_all.shape) == 1:
        avec = output_all.unsqueeze(0)
    else:
        avec = output_all

    vectors_tensor = torch.from_numpy(vectors)
    vectors_tensor = vectors_tensor.cuda()
    predicts = VectorSim.matching(avec, vectors_tensor) # 匹配特征向量
    #print(predicts)

    print("DB Labels List:\n{0}".format(clsids.reshape(1,-1)))
    #信号实际值
    print("True Labels:\n{0}".format(classid_gt.reshape(1,-1)))

    # 6. 匹配结果显示
    for i in range(3):
        Indexs = predicts['pred_label'][:,i]
        probs = predicts['pred_score'][:,i]
        probs = probs.cpu().numpy()
        Indexs = Indexs.cpu().numpy()
        print("Predict Top {0} Labels:\n{1} \n Probs:\n{2}".format( i, clsids[Indexs].reshape(1,-1), probs)) #预测标签
        if i == 0:
            val_predict = clsids[Indexs]

    # 7. 计算匹配准确率
    val_predict = val_predict.squeeze()
    val_score = np.where(val_predict == classid_gt, 1.0, 0.0)
    val_acc = np.mean(val_score)
    print('predict_result: acc= {:.2f}%'.format(val_acc*100))

    t4 = time.time()
    print("count score：{:.2f}s".format(t4 - t3))

