function [bindata] = DemodLoraWave(filename,spos,epos,nb_fc,kBaud,nSampPerSym,nStartPoint,nb_bw)
% 1. 初始化命令
%clc
% close all
% 
% addpath("lib")     %库函数路径
% addpath("usrlib\common\")  %用户自定义路径
% addpath("usrlib\nb_433\");

nb_fs = kBaud*nSampPerSym;
if exist('nb_bw','var')==0
    nb_bw = floor(nb_fs/2);
end
%nb_bw = kBaud;

%fname_sign    = filename(end-5);
% kBaud = 64e3; %1.2e3*80;
% nb_bw   = kBaud*8;      % 窄带信号的带宽
%需要与 GenBBSig_nb433 中参数对应


% ischannelized=1;   % 是否信道化
% isengine = 1;      % 文件来源是否是引擎
[wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filename);

if(exist('spos','var')>0 && exist('epos','var')>0)
    if spos==-1
        spos=1;
        epos=length(wb_signal);
    end
else
    spos=1;
    epos=length(wb_signal);
end
wb_signal = wb_signal(spos:epos);
showWBSig(wb_signal, wb_fc, wb_fs, filename);%显示宽带信号 - 时频域

if exist('nb_fc','var')==0
    nb_fc = wb_fc;
end

[nb_sig_td] = ExtractNBSig(wb_signal, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs);
%showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号 - 时频域
showNBSig(0, nb_sig_td, nb_fc, nb_fs);

%[bindata] = dmodgfsk_byarcos(nb_sig_td, nSampPerSym, nStartPoint);

rf_freq = 0;%nb_fc;    % carrier frequency, used to correct clock drift
sf = 6;             % spreading factor
bw = nb_bw;         % bandwidth
fs = nb_fs;           % sampling rate
phy = LoRaPHY(rf_freq, sf, bw, fs);
phy.has_header = 0;
phy.crc = 0;
phy.fast_mode = true;
%phy.
[bindata, ~, ~] = phy.demodulate(nb_sig_td(nStartPoint:end));
end