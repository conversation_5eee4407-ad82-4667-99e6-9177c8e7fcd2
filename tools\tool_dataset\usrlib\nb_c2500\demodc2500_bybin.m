function [outbytes_dewhite] = demodc2500_bybin(bindata)
%判断同步起始位置
sPos = 1;
len_tx = floor(length(bindata)/8);
outbytes_org=bi2de(reshape(bindata(1:len_tx*8)',8,len_tx)', 'left-msb');
% fprintf('\n收到信号:%d bytes\n', length(outbytes_org));
% fprintf('%02X ',outbytes_org);

for i=1:len_tx
    if outbytes_org(i)==hex2dec('55')
        break;
    else
        sPos=sPos+1;
    end
end

% 3.3 viterbi译码用参数
tPoly = poly2trellis(4, [13 17]); % Define trellis. 生成多项式，viterbi译码用

rxSig_demoded = bindata(sPos:end)';
rxSig_demoded = 1 - rxSig_demoded;%反向
%3.4.5.1 取数据部分, 32bit preamble，16bit  0xD391，16bit  0xD391
preamble     = rxSig_demoded(1:32);
header_syncA = rxSig_demoded(33:48);
header_syncB = rxSig_demoded(49:64);
body_data    = rxSig_demoded(65:end-1);%保证偶数

%3.4.5.3 解交织过程 %body_descramble
bin_deinterlv = deinterleaver(body_data(1:end));

%3.4.5.4 信道译码过程
tb = 6;
decoded_out = vitdec(bin_deinterlv,tPoly,tb,'trunc','hard');

%3.4.5.2 解扰（去白化）
data_native = decode_PN9(decoded_out); %解扰过程
outbytes_dewhite = bit2int(data_native,8);
fprintf('\n解白化后信号: %d bytes\n', length(outbytes_dewhite));
fprintf('%02X ',outbytes_dewhite');%转换为行向量

%3.4.5.5 按照帧格式取包长度信息
byte_len = floor(length(data_native)/8);
package_len = bit2int(data_native(1:8),8,true); %addr field + data field (optional words)
body_addr = bit2int(data_native(9:16),8,true);
rxbuf = bit2int(data_native(1:byte_len*8),8,true);

%3.4.5.6 CRC校验
body_len = package_len + 1; % length field + addr field + data field
%(1) 固定包长数据
if body_len < byte_len

    chksum_rx = rxbuf(body_len+1)*256+rxbuf(body_len+2);
    checksum = calcrcwordbyseq(rxbuf(1:body_len));
    fprintf('\nthe checksum value=%d (%X) the rx checksum=%d (%X)\r\n', checksum, checksum, chksum_rx, chksum_rx);
    if (chksum_rx == checksum)
        fprintf('\ncrc check ok, body len=%d\n', body_len);
    end
end
%(2) 非固定包长数据
bcheckok = false;
for body_len=10:byte_len-2
    chksum_rx = rxbuf(body_len+1)*256+rxbuf(body_len+2);
    checksum = calcrcwordbyseq(rxbuf(1:body_len));
    if (chksum_rx == checksum)
        fprintf('\ncrc check ok, body len=%d\n', body_len);
        bcheckok = true;
        break;
    end
end
if bcheckok==false
    fprintf('\ncrc check is not ok, body len=%d\n', body_len);
end

end

