
% file = '/home/<USER>/work/inspire/inspire.dat';
% modifyEngineSignal(file,-5);

function modifyEngineSignal(file,snr)


v = uint8(read_byte_binary(file));
header_length =  double(typecast(v(5:6), 'uint16'));
data_vb = v(header_length+1:end);
raw_data_u16 = typecast(data_vb, 'uint16');

% Remove 4 MSBits from raw data and convert to float
data_s16 = typecast(raw_data_u16, 'int16');
data_s16 = bitshift(data_s16, 4, 'int16');
data_s16 = bitshift(data_s16, -4, 'int16');
data_floats = double(data_s16);
data = data_floats(1:2:end) + 1j*data_floats(2:2:end);

signal = round(awgn(data,snr,'measured'));
resave_data=zeros(2*length(signal),1);
resave_data(1:2:end)=real(signal);
resave_data(2:2:end)=imag(signal);
v0 = typecast(int16(resave_data), 'uint8');
vv=[v(1:header_length) ;v0];
fid = fopen ('resave.dat', 'wb');
fwrite(fid, vv, 'uint8');
fclose(fid);
end

