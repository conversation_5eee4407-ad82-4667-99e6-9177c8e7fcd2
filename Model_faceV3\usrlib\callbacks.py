import datetime
import os

import torch
import matplotlib
matplotlib.use('Agg')
import scipy.signal
from matplotlib import pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm

class LossHistory():
    '''
    Loss日志类
    '''
    def __init__(self, log_dir, model, input_shape):
        time_str        = datetime.datetime.strftime(datetime.datetime.now(),'%Y_%m_%d_%H_%M_%S')
        self.log_dir    = os.path.join(log_dir, "loss_" + str(time_str))
        self.losses     = []
        self.val_loss   = []
        
        os.makedirs(self.log_dir)
        self.writer     = SummaryWriter(self.log_dir)
        try:
            dummy_input     = torch.randn(2, 3, input_shape[0], input_shape[1])
            self.writer.add_graph(model, dummy_input)
        except:
            pass

    def append_loss(self, epoch, loss, val_loss):
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        self.losses.append(loss)
        self.val_loss.append(val_loss)

        with open(os.path.join(self.log_dir, "epoch_loss.txt"), 'a') as f:
            f.write(str(loss))
            f.write("\n")
        with open(os.path.join(self.log_dir, "epoch_val_loss.txt"), 'a') as f:
            f.write(str(val_loss))
            f.write("\n")

        self.writer.add_scalar('loss', loss, epoch)
        self.writer.add_scalar('val_loss', val_loss, epoch)
        self.loss_plot()

    def loss_plot(self):
        iters = range(len(self.losses))

        plt.figure()
        plt.plot(iters, self.losses, 'red', linewidth = 2, label='train loss')
        plt.plot(iters, self.val_loss, 'coral', linewidth = 2, label='val loss')
        try:
            if len(self.losses) < 25:
                num = 5
            else:
                num = 15
            
            plt.plot(iters, scipy.signal.savgol_filter(self.losses, num, 3), 'green', linestyle = '--', linewidth = 2, label='smooth train loss')
            plt.plot(iters, scipy.signal.savgol_filter(self.val_loss, num, 3), '#8B4513', linestyle = '--', linewidth = 2, label='smooth val loss')
        except:
            pass

        plt.grid(True)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend(loc="upper right")

        plt.savefig(os.path.join(self.log_dir, "epoch_loss.png"))

        plt.cla()
        plt.close("all")

def generator(batch, datahandler):
    '''
        数据生成函数
    '''
    idx_tmp = np.random.choice(datahandler.rx_signal.shape[0], batch, replace=False)
    batch_rx_signal = datahandler.rx_signal[idx_tmp].astype(np.float32)
    classid = datahandler.class_id[idx_tmp].astype(np.int64)
    fs_value = datahandler.fs_value[idx_tmp].astype(np.float32)
    bw_value = datahandler.bw_value[idx_tmp].astype(np.float32)
    #classid = torch.tensor([0])#for test
    return torch.from_numpy(batch_rx_signal), torch.from_numpy(classid), torch.from_numpy(fs_value), torch.from_numpy(bw_value)

def Train_one_epoch(Model, optimizer, scheduler, criterion, batch_size, train_datahandler, val_datahandler):
    '''
    训练1个epoch
    '''
    Model.train()
    ModelInput1, label, fs_value, bw_value = generator(batch_size, train_datahandler)
    ModelInput1, label, fs_value, bw_value = ModelInput1.cuda(), label.cuda(), fs_value.cuda(), bw_value.cuda()
    ModelOutput = Model(ModelInput1, fs_value, bw_value, label)
    #if label.shape[0] == 1:

    #分类问题
    label= label.squeeze(1)
    loss = criterion(ModelOutput, label.type(torch.long))
    # CrossEntropyLoss()=log_softmax() + NLLLoss() 
    # loss  = nn.NLLLoss()(F.log_softmax(ModelOutput, -1), label)
    predict = torch.argmax(ModelOutput,dim=1)
    score = torch.where(predict == label, 1.0, 0.0)
    acc = torch.mean(score)
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
    scheduler.step()#退化学习率                                        

    # Model Evaluating
    Model.eval()

    with torch.no_grad():
        val_ModelInput1,  val_label, fs_value, bw_value = generator(batch_size, val_datahandler)
        val_ModelInput1, val_label, fs_value, bw_value = val_ModelInput1.cuda(), val_label.cuda(), fs_value.cuda(), bw_value.cuda()
        val_ModelOutput = Model(val_ModelInput1, fs_value, bw_value)
        val_label = val_label.squeeze(1)
        val_loss = criterion(val_ModelOutput, val_label).item()
        # val_loss  = nn.NLLLoss()(F.log_softmax(val_ModelOutput, -1), val_label)
        val_predict = torch.argmax(val_ModelOutput,dim=1)
        val_score = torch.where(val_predict == val_label, 1.0, 0.0)
        val_acc = torch.mean(val_score)

    
    return loss, acc, val_loss, val_acc, val_label.cpu().numpy(), val_predict.cpu().numpy()

def Train_one_epoch_all(Model, optimizer, scheduler, criterion, train_loader, val_loader, epoch_params):
    '''
    训练1个epoch
    '''
    train_loss      = 0
    train_accuracy  = 0

    val_loss        = 0
    val_accuracy    = 0

    print('Start Train')
    pbar = tqdm(total=epoch_params.epoch_step,desc=f'Epoch {epoch_params.epoch + 1}/{epoch_params.total_epoch}',postfix=dict,mininterval=0.3)

    Model.train()
    for iteration, batch in enumerate(train_loader):
        ModelInput1, fs_value, bw_value, label = batch
        ModelInput1, label, fs_value, bw_value = ModelInput1.cuda(), label.cuda(), fs_value.cuda(), bw_value.cuda()
        ModelOutput = Model(ModelInput1, fs_value, bw_value, label)
        #if label.shape[0] == 1:

        #分类问题
        label= label.squeeze(1)
        loss = criterion(ModelOutput, label.type(torch.long))
        # CrossEntropyLoss()=log_softmax() + NLLLoss() 
        # loss  = nn.NLLLoss()(F.log_softmax(ModelOutput, -1), label)
        predict = torch.argmax(ModelOutput,dim=1)
        score = torch.where(predict == label, 1.0, 0.0)
        acc = torch.mean(score)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        scheduler.step()#退化学习率      

        train_loss += loss.item()
        train_accuracy += acc.item()

        mean_train_loss     =  train_loss / (iteration + 1)
        mean_train_accuracy =  train_accuracy / (iteration + 1)

        pbar.set_postfix(**{'train_loss': mean_train_loss, 
                                'train_accuracy'  : mean_train_accuracy, 
                                'lr'        : scheduler.get_last_lr()})
        pbar.update(1)                                  

    # Model Evaluating
    pbar.close()
    print('Finish Train')

    val_label_all=[]
    val_predict_all=[]
    print('Start Validation')
    pbar = tqdm(total=epoch_params.epoch_step_val, desc=f'Epoch {epoch_params.epoch + 1}/{epoch_params.total_epoch}',postfix=dict,mininterval=0.3)
    Model.eval()
    for iteration, batch in enumerate(val_loader):
        with torch.no_grad():
            val_ModelInput1, fs_value, bw_value, val_label = batch
            val_ModelInput1, val_label, fs_value, bw_value = val_ModelInput1.cuda(), val_label.cuda(), fs_value.cuda(), bw_value.cuda()
            val_ModelOutput = Model(val_ModelInput1, fs_value, bw_value)
            val_label = val_label.squeeze(1)
            loss_value = criterion(val_ModelOutput, val_label.type(torch.long))
            # val_loss  = nn.NLLLoss()(F.log_softmax(val_ModelOutput, -1), val_label)
            val_predict = torch.argmax(val_ModelOutput,dim=1)
            val_score = torch.where(val_predict == val_label, 1.0, 0.0)
            accuracy = torch.mean(val_score)

            val_label_all.extend(val_label.cpu().numpy())
            val_predict_all.extend(val_predict.cpu().numpy())

            val_loss    += loss_value.item()
            val_accuracy    += accuracy.item()
            mean_val_loss       =  val_loss / (iteration + 1)
            mean_val_accuracy   =  val_accuracy/(epoch_params.epoch_step_val+1)

            pbar.set_postfix(**{'val_loss': mean_val_loss,
                                'val_accuracy'  : mean_val_accuracy, 
                                'lr'        : scheduler.get_last_lr()})
            pbar.update(1)


    return mean_train_loss, mean_train_accuracy, mean_val_loss, mean_val_accuracy, val_label_all, val_predict_all
      