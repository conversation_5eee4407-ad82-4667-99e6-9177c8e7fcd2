function [ret, nb_sig_fd] = ExtractNarrowbandSignal(...
    wb_sig_fd, wb_fc, wb_bw, wb_fs, nb_len_set, nb_fc, nb_bw, nb_fs, rm_dc)
%% Extract each channel from wideband signal
% Author: Leyuan Pan
% Date: 2018/06/08
%
%  Params： wb_sig_fd   -- 宽带信号频域点
%           wb_fc       -- 宽带信号中心频率
%           wb_bw       -- 宽带信号带宽
%           wb_fs       -- 宽带信号采样率 
%           nb_len      -- 窄带信号点数 一般为 宽带数据点数*(nb_fs/wb_fs); %采样率换算计算得到窄带信号长度
%           nb_fc       -- 窄带信号中心频率
%           nb_bw       -- 窄带信号带宽 （实际没有用）
%           nb_fs       -- 窄带信号采样率 (决定实际截取长度)
%           rm_dc       -- 去直流

if nargin < 8
    rm_dc = 1; % 去直流
end

ret = IsValidNarrowBW(wb_fc, wb_bw, wb_fs, nb_fc, nb_bw, nb_fs);
nb_sig_fd = [];
if ret ~= 1
    return;
end
% 1. 信号带宽及长度折算
wb_len = length(wb_sig_fd); % 宽带信号的频域点数，wb_len/2为中心频率点 

nb_dc = round(wb_len * (nb_fc - wb_fc) / wb_fs + wb_len/2);% 窄带中心频率点位置，同时减去wb_fc也实现了下变频功能
nb_bw_len = floor(wb_len * nb_fs / wb_fs);                 % 窄带信号长度(折算到窄带长度),用 nb_fs 折算点数
nb_bw_len = min(nb_bw_len, nb_len_set);  %选择最少的频域点数

nb_len_pos = floor(nb_bw_len/2);
nb_len_neg = nb_bw_len - nb_len_pos;                         % 实质上为: 窄带长度/2

% 2. 信号低通滤波，仅仅取：窄带频率中心点位置 + 窄带频率长度/2，窄带频率中心点位置 - 窄带频率长度/2
% Low-pass filtering
% 设置 上半部分 [0-fs/2]+nb_dc
nb_sig_fd = zeros(nb_len_set, 1);    %频率范围为全带宽，下面仅仅把中心带宽设置为有效值，高频成分设置为0，实现低通                
if rm_dc
    len_wb_toend = length(wb_sig_fd(nb_dc+1:end)); %计算宽带频率点的最大长度
    len_tosel = min(nb_len_pos-1,len_wb_toend);    %取最小长度

    nb_sig_fd(2:2+len_tosel-1) = wb_sig_fd(nb_dc+1:nb_dc+1+len_tosel-1); %如果去直流，就把中心频率点去掉，取窄带频率中心点位置+1 + 窄带频率长度/2
else
    len_wb_toend = length(wb_sig_fd(nb_dc+1:end)); %计算宽带频率点的最大长度
    len_tosel = min(nb_len_pos-1, len_wb_toend);   %取最小长度

    nb_sig_fd(1:len_tosel) = wb_sig_fd(nb_dc:nb_dc+len_tosel-1);  %窄带频率中心点位置 + 窄带频率长度/2
end

% 下半部分 [-fs/2, 0]+nb_dc
if nb_dc>nb_len_neg %长度足够
    len_wb_toend = length(wb_sig_fd(nb_dc-nb_len_neg:end));
    len_tosel = min(nb_len_neg,len_wb_toend);

    nb_sig_fd(nb_len_set-len_tosel+1:nb_len_set) = wb_sig_fd(nb_dc-len_tosel:nb_dc-1); %窄带频率中心点位置 - 窄带频率长度/2
else
    nb_len_neg_reg = nb_dc-1;%修正长度后的
    nb_sig_fd(nb_len_set-nb_len_neg_reg+1:nb_len_set) = wb_sig_fd(1:nb_dc-1); %窄带频率中心点位置 - 窄带频率长度/2
end
ret = 1;
end

