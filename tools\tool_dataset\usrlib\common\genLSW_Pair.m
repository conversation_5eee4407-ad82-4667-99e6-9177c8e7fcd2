function []=genLSW_Pair(valfile_list,nStep)
%    function   : genLSW_Pair
%    Description: 规则数据，按照步长生成标记数据对 LSW(Labeled Signal Wild)
%    author     : 刘智国
%    Date       : 2024-12-31
lines = readlines(valfile_list);
fname_out = ".\output\lsw_pair.txt";
if exist(fname_out,"file")>0
    delete(fname_out);
end

nLen = length(lines);
%写入相同配对行
for idRow = 1:nStep:nLen-(nStep-1)
    strline = strcat(lines(idRow),";",lines(idRow+2));   % (1,3)
    writelines(strline, fname_out,'WriteMode','append');
    strline = strcat(lines(idRow+1),";",lines(idRow+3)); % (2,4)
    writelines(strline, fname_out,'WriteMode','append');
end
%写入不同配对行
for idRow = 1:nStep:nLen-(nStep-1)
    if idRow < nLen/2
        idsel = randi([floor(nLen/2+nStep),nLen],1);
    else
        idsel = randi([1, floor(nLen/2-nStep)],1);   
    end
    strline = strcat(lines(idRow+4),";",lines(idsel));   % ()
    writelines(strline, fname_out,'WriteMode','append');
end

end