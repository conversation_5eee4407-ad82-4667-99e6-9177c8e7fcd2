function [X_value] = CalcQpskTxValue(RxData)
%  Function    ：CalcQpskTxValue
%  Description : 计算qpsk发送值，并显示图表
%                主要目的为：用于估计信道H
%                原理：
%                     X = norm(RxData) %根据接收到的数据进行归一化
%
%  Parameter   : RxData    -- 复信号(频域)
%
%  output      : X_value    -- 发送X值
%
%  Author      : Liuzhiguo
%  Date        : 2024-07-11
%
len_rxdata = length(RxData);
mean_rxdata = mean(RxData);
% mean_I = abs(real(mean_rxdata))*(1/3);
% mean_Q = abs(imag(mean_rxdata))*(1/3);
mean_I = 0;
mean_Q = 0;

X_value = zeros(len_rxdata,1);
for i=1:len_rxdata
    value_I = real(RxData(i));
    value_Q = imag(RxData(i));
    if (value_I>mean_I) && (value_Q>mean_Q)
        X_value(i) = 1+1j;
    elseif (value_I>mean_I) && (value_Q<-mean_Q)
        X_value(i) = 1-1j;
    elseif (value_I<-mean_I) && (value_Q>mean_Q)
        X_value(i) = -1+1j;
    elseif (value_I<-mean_I) && (value_Q<-mean_Q)
        X_value(i) = -1-1j;
    else
        X_value(i) = sqrt(2);
    end
end
X_value = X_value/sqrt(2);

end