function load_waterfall(sig,wb_fc,wb_fs)

window = 4096;
noverlap = window/2;
nfft = window;
a_channel = sig;
figure(99)
[S,F,T,P]=spectrogram(a_channel,window,noverlap,nfft,wb_fs,'yaxis');
F = (-2048:1:2047)*0.015+wb_fc/1e6;
surf(T*1000,F,10*log10(circshift(P,2048)),'edgecolor','none'); axis tight;

view(0,90);
xlabel('Time (ms)'); ylabel('MHz');
title(['fc= ' num2str(wb_fc/1000000) 'Mhz' '  fs= ' num2str(wb_fs/1000000) 'Mhz']);

end

