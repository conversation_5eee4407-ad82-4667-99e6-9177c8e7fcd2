%  Function    ：WriteHdfsLTECH
%  Description : 写入lte channel训练数据
%  Parameter   : fname_rd       -- 写入文件名称
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13

% 1. 初始化命令
clc
clear
close all

addpath("lib");             %库函数路径
addpath("usrlib\common");   %用户自定义路径
addpath("usrlib\wb_evo2");  %用户自定义路径

% 2. 文件读入
% 2.1 文件目录读取
inpath = ".\outdataset\base\";
inname = "nb-dataset";
cls_size = 'S1';

fname_indslist = dir(strcat(inpath,inname,'-*','.hdf5'));

for i=1:length(fname_indslist)
    fname_indataset = strcat(fname_indslist(i).folder,'\',fname_indslist(i).name);
    disp(strcat("--------输入文件:",fname_indataset,"--------"));
    GenLteChdDS(fname_indataset,"EPA");
    GenLteChdDS(fname_indataset,"EVA");
    GenLteChdDS(fname_indataset,"ETU");
end

function [] = GenLteChdDS(fname_indataset, sProfile)
%LTE fading channel
model = struct(DelayProfile=sProfile,NRxAnts=1, ...
    DopplerFreq = 5,MIMOCorrelation="Low", ...
    Seed=1,InitPhase="Random",ModelType="GMEDS", ...
    NTerms=16,NormalizeTxAnts="On", ...
    NormalizePathGains="On");
% ModelType: To model Rayleigh fading by using the generalized method of
% exact Doppler spread (GMEDS) described in [4], set this field to "GMEDS".
%DelayProfile  "EPA", "EVA", "ETU", "Custom", "Off"

rmc = lteRMCDL("R.10",'TDD',1);
rmc.TotSubframes = 1;
subframeNumber = 0;
rmc.NSubframe = mod(subframeNumber,10);
model.InitTime = subframeNumber/1000;
if sProfile=="EPA"
    vrange = (0:2);         %  移动速度(m/s)
elseif sProfile=="EVA"
    vrange = (0:25);         %  移动速度(m/s)
elseif sProfile=="ETU"
    vrange = (0:10);         %  移动速度(m/s)
end
c = 3e8;                    % 光速

[waveform,txGrid,info] = lteRMCDLTool(rmc,[1]);%[1; 0; 1; 1]
rowpointer = 0;
for v = vrange   % max 90km/h
    fprintf("生成LTE(%s) 数据：v=%.2f\r\n", sProfile, v);

    %[rx,info] = lteFadingChannel(model,tx);
    [rd_sig,class_id,class_name, arrayfc, arrayfs] = RdTrainSig(fname_indataset);
    fname_outdataset = strrep(fname_indataset,'base','lteCH');
    substr = strcat('t-',sProfile,'-S');
    fname_outdataset = strrep(fname_outdataset,'t-S',substr);

    for iRow = 1 : length(class_id)
        cls_id = class_id(1,iRow);
        cls_name = class_name(1,iRow);
        fc = arrayfc(1,iRow);
        fs = arrayfs(1,iRow);
        model.SamplingRate = fs;
        fd = v*double(fc)/c;
        model.DopplerFreq = fd;

        signal = rd_sig(1,:,iRow)+1j*rd_sig(2,:, iRow);
        wb_rxSig_clip = lteFadingChannel(model,signal.');%添加多径信息
        wb_rxSig_clip = wb_rxSig_clip.';
        rowpointer = rowpointer+1;
        WrTrainSig(fname_outdataset, wb_rxSig_clip, cls_id,cls_name, fc, fs,rowpointer);  % 生成数据集，hdf文件格式
    end
end
%DispDatasetRecByChart(fname_dataset_ltech,1,"LTE信道");
%rmccfgout = lteRMCDL(rc,duplexmode,totsubframes)
%rmccfgout = lteRMCDL(rmccfg,ncodewords)
end

