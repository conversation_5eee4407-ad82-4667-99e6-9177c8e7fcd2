%% 信道
% COST207

function channel= cost207_channel()
    fs = 61.44e6;
    v = 50; % 移动速度(m/s)
    fc = 2.4e9; % 载波频率
    c = 3e8; % 光速
    fd = v*fc/c;
    delay = [0 0.1 0.2 0.3 0.4 0.5]*1e-6; % 时延 (s)
    gain = [0 -4 -8 -12 -16 -20]; % 路径增益
    channel = comm.RayleighChannel('PathDelays', delay, 'AveragePathGains', gain,...
    'NormalizePathGains',true,'MaximumDopplerShift',fd,'SampleRate',fs);
end