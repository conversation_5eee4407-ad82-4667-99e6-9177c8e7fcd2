function moddatasve_h5file(output_file,data,labels,snr_values,mod_type_cell,fs, fc, snr_levels)
%======================生成output_file.h5文件===================================

        % output_file：输出文件名
        % data: 当前信号数据，格式为(样本总数, 2, 信号截取长度)
        % labels: 当前信号标签
        % snr_values: 当前信号snr值
        % mod_type_cell: 所有调制类型
        % snr_levels: 信噪比范围. 例：0,2,4,6,8,10...20.

% 写入数据
h5create(output_file, '/signal_data', size(data), 'Datatype', 'single');
h5write(output_file, '/signal_data', single(data));

h5create(output_file, '/labels', size(labels), 'Datatype', 'uint8');
h5write(output_file, '/labels', uint8(labels));

h5create(output_file, '/SNRs', size(snr_values), 'Datatype', 'single');
h5write(output_file, '/SNRs', single(snr_values));

% 添加属性
h5writeatt(output_file, '/', 'modulations', mod_type_cell);
h5writeatt(output_file, '/', 'sample_rate', fs);
h5writeatt(output_file, '/', 'snr_range', snr_levels);
h5writeatt(output_file, '/', 'center_freq', fc);

% 添加全局标签说明
h5writeatt(output_file, '/', 'LabelMeaning', '0:BPSK, 1:QPSK, 2:16QAM, 3:GFSK, 4:LoRa');%, 4:LoRa'

disp('HDF5 dataset generation complete.');
end

