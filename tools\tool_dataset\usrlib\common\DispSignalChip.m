function [startPosX, endPosX] = DispSignalChip(figID, NoiseVar, window_size)
%  Function    ：DispSignalChip
%  Description : 显示信号切片
%  Parameter   : hfig         -- 要分析的图表chart
%                NoiseVar     -- 噪声方差
%                window_size  -- 分析滑动窗口大小
%
%  Author      : Liuzhiguo
%  Date        : 2024-07-18

fig=figure(figID);
ax=gca;%ax = fig.Children(3);  % 获取当前坐标轴(需要先选择当前的子图)
lines = ax.Children;  % 获取坐标轴中的所有子对象，包括线条
nLineCount = length(lines);
fprintf('the line count=%d\r\n', nLineCount);
if nLineCount<2
    warning('The wrong plot selected,pls click the subplot(1)');
    return;
end
curline = lines(1);
sigX = curline.XData;
sigY = curline.YData;
%x_range = get(ax, 'xlim'); %获取 x 轴的取值范围。
y_range = get(ax, 'ylim'); %获取 y 轴的取值范围。

startPosX = 0;
endPosX   = 0;
nStartpos = 1;
nEndpos  = length(sigY);
bInMid   = false;
alpha    = 2.5;
NoiseVar_std = alpha*NoiseVar;
i=nStartpos;
while i<nEndpos-window_size
    if bInMid==false && (var(sigY(i:i+window_size-1)) > NoiseVar_std)%信号功率大于噪声功率，开始位置
        startPosX = sigX(i);
        addlbltick(sigX,sigY,i,y_range(2)-0.1*(y_range(2)-y_range(1)));
        bInMid=true;
    elseif bInMid==true && (var(sigY(i:i+window_size-1)) < NoiseVar_std)%信号功率小于噪声功率，结束位置
        endPosX = sigX(i);
        addlbltick(sigX,sigY,i,y_range(2));
        bInMid=false;
        i=i+30*window_size;%向后移动，避免信号拖尾
    end
    i=i+1;
end

%%%------------------------------辅助函数----------------------------
function [] = addlbltick(sigX,sigY,i,y_maxlimit)
%txtsig = sprintf('|%.2f|:%.2f',startPosX-endPosX, sigX(i));
txtsig = sprintf('%.2f', sigX(i));

%text(sigX(i)+.02,sigY(i),txtsig);
x=[sigX(i), sigX(i)];
y=[y_maxlimit, sigY(i)];
t=annotation('textarrow', xy2norm('x', x), xy2norm('y', y), 'String', txtsig);
t.FontSize = 6;
t.TextColor = 'blue';

%%%------------------------------其他函数------------------------------
% https://ww2.mathworks.cn/matlabcentral/fileexchange/8269-transform-axes-units-to-normalized-units-for-annotation-objects
%  author: Girish Ratanpal, ECE, UVa.
%  transform axes units to normalized units for 2-D figures only.
%  works for linear,log scales and also reverse axes.
%  DATE: JAN. 6, 2006. previous version: aug 12, 2005.
%
%
%FUNCTION DESCRIPTION:
% function [y_norm] = y_to_norm_v2(y_begin,y_end)
% function returns a 1x2 array, y_norm, with normalized unitsy_begin and
% y_end for the annotation object.
%
% function arguments:
%   1. y_begin: enter the point where the annotation object begins on the axis
%         using axis units
%   2. y_end: end of annotation object, again in axis units.
%

%EXAMPLE: first plot the graph on the figure.
%  then use the following command for placing an arrow:
%  h_object =
%   annotation('arrow',x_to_norm_v2(x_begin,x_end),y_to_norm_v2(y_begin,y_end));
%  ******************************************
%   CODE FOR x_norm_v2() IS COMMENTED AT THE END.
%  ******************************************

function [norm] = xy2norm(xy, p)
if strcmp(xy, 'x')
    norm = x_to_norm_v2(p(1), p(2));
elseif strcmp(xy, 'y')
    norm = y_to_norm_v2(p(1), p(2));
end

function [y_norm] = y_to_norm_v2(y_begin, y_end)
if nargin ~= 2
    error('wrong number of input arguments! y_to_norm_v2(y_begin, y_end)');
end
h_axes = get(gcf, 'currentaxes'); % 获取 axes 句柄
axes_offsets = get(h_axes, 'position'); % 获取 axes 在 figure 上的位置
y_axis_limits = get(gca, 'ylim'); % 获取 y 轴的范围
y_dir = get(gca, 'ydir'); % 获取 y 轴的方向
y_scale = get(gca, 'yscale'); % 获取 y 轴的刻度类型

y_axis_length_lin = abs(y_axis_limits(2) - y_axis_limits(1)); % 获取线性刻度下 y 轴的长度

if strcmp(y_dir, 'normal') % 轴未反转
    if strcmp(y_scale, 'log') % 对数刻度
        y_axis_length_log = abs(log10(y_axis_limits(2)) - log10(y_axis_limits(1))); % 获取对数刻度下 y 轴的长度
        y_begin_norm = axes_offsets(2) + axes_offsets(4) * abs(log10(y_begin) - log10(y_axis_limits(1))) / (y_axis_length_log);
        y_end_norm = axes_offsets(2) + axes_offsets(4) * abs(log10(y_end) - log10(y_axis_limits(1))) / (y_axis_length_log);
    elseif strcmp(y_scale, 'linear') % 线性刻度
        y_begin_norm = axes_offsets(2) + axes_offsets(4) * abs((y_begin - y_axis_limits(1)) / y_axis_length_lin);
        y_end_norm = axes_offsets(2) + axes_offsets(4) * abs((y_end - y_axis_limits(1)) / y_axis_length_lin);
    else
        error('use only lin or log in quotes for scale');
    end
elseif strcmp(y_dir,'reverse') % 轴反转
    if strcmp(y_scale, 'log') % 对数刻度
        y_axis_length_log = abs(log10(y_axis_limits(2)) - log10(y_axis_limits(1))); % 获取对数刻度下 y 轴的长度
        y_begin_norm = axes_offsets(2) + axes_offsets(4) * (1 - abs(log10(y_begin) - log10(y_axis_limits(1))) / (y_axis_length_log));
        y_end_norm = axes_offsets(2) + axes_offsets(4) * (1 - abs(log10(y_end) - log10(y_axis_limits(1))) / (y_axis_length_log));
    elseif strcmp(y_scale, 'linear') % 线性刻度
        y_begin_norm = axes_offsets(2) + axes_offsets(4) * (1 - abs((y_begin - y_axis_limits(1)) / y_axis_length_lin));
        y_end_norm = axes_offsets(2) + axes_offsets(4) * (1 - abs((y_end - y_axis_limits(1)) / y_axis_length_lin));
    else
        error('use only lin or log in quotes for scale');
    end
end
y_norm = [y_begin_norm, y_end_norm];

%********************************************************
function [x_norm] = x_to_norm_v2(x_begin,x_end)

if nargin ~= 2
    error('Wrong number of input arguments.')
end

h_axes = get(gcf,'CurrentAxes');    %get axes handle

axesoffsets = get(h_axes,'Position');
x_axislimits = get(gca, 'xlim');     %get axes extremeties.
x_dir = get(gca,'xdir');
x_scale = get(gca,'xscale');

%get axes length
x_axislength_lin = abs(x_axislimits(2) - x_axislimits(1));


if strcmp(x_dir,'normal')
    if strcmp(x_scale,'log')
        x_axislength_log = abs(log10(x_axislimits(2)) - log10(x_axislimits(1)));
        x_begin_norm = axesoffsets(1)+axesoffsets(3)*abs(log10(x_begin)-log10(x_axislimits(1)))/(x_axislength_log);
        x_end_norm = axesoffsets(1)+axesoffsets(3)*abs(log10(x_end)-log10(x_axislimits(1)))/(x_axislength_log);

        x_norm = [x_begin_norm x_end_norm];
    elseif strcmp(x_scale,'linear')
        x_begin_norm = axesoffsets(1)+axesoffsets(3)*abs((x_begin-x_axislimits(1))/x_axislength_lin);
        x_end_norm = axesoffsets(1)+axesoffsets(3)*abs((x_end-x_axislimits(1))/x_axislength_lin);

        x_norm = [x_begin_norm x_end_norm];
    else
        error('use only lin or log in quotes for scale')
    end

elseif strcmp(x_dir,'reverse')
    if strcmp(x_scale,'log')
        x_axislength_log = abs(log10(x_axislimits(2)) - log10(x_axislimits(1)));
        x_begin_norm = axesoffsets(1)+axesoffsets(3)*abs(log10(x_axislimits(2))-log10(x_begin))/(x_axislength_log);
        x_end_norm = axesoffsets(1)+axesoffsets(3)*abs(log10(x_axislimits(2))-log10(x_end))/(x_axislength_log);

        x_norm = [x_begin_norm x_end_norm];
    elseif strcmp(x_scale,'linear')
        x_begin_norm = axesoffsets(1)+axesoffsets(3)*abs((x_axislimits(2)-x_begin)/x_axislength_lin);
        x_end_norm = axesoffsets(1)+axesoffsets(3)*abs((x_axislimits(2)-x_end)/x_axislength_lin);

        x_norm = [x_begin_norm x_end_norm];
    else
        error('use only lin or log in quotes for scale')
    end
else
    error('use only r or nr in quotes for reverse')
end