%  Function    ：DivOriginalData
%  Description : 将原始数据划分为基本数据，用于噪声增强的基本数据，及用于其他增强的基本数据
%  Parameter   : fname_label       -- 类别文件路径
%                output_dir        -- 输出文件路径
%  Date        : 2024-05-06

IncludedPaths; %统一引入路径
InitMyParams; %初始化路径参数
fname_label= myconfig.fpath_classdef; %原始数据类别文件路径及文件名
gennoised_datanum=myconfig.gennoised_datanum;
genaugment_datanum=myconfig.genaugment_datanum;
[output_dir, ~, ~] = fileparts(myconfig.fpath_classdef) ;%原始数据输出划分文件txt路径(与原始数据类别路径相同)

% ================== 初始化部分 ==================
[signalDB_dir, ~, ~] = fileparts(fname_label);
signalDB_dir = fullfile(signalDB_dir,'');
fname_class = fname_label;

% ================== 读取类别定义 ==================
if ~exist(fname_class, "file")
    error("类别文件不存在: %s", fname_class);
end
Table_cls = readtable(fname_class, 'Format', '%d %s %d %f');
Table_cls.Properties.VariableNames = ["clsid", "clsname", "bw", "Tms"];
sep = filesep; %系统分割符
% ================== 处理每个类别 ==================
for iRow = 1:height(Table_cls)
    clsname = char(Table_cls.clsname(iRow));
    fname_txt = fullfile(signalDB_dir, clsname, 'Train_records.txt');

    % 跳过不存在的文件
    if ~exist(fname_txt, "file")
        fprintf("!! 警告: 文件不存在 [%s]\n", fname_txt);
        continue;
    end

    % 读取当前类别数据
    curflines = readlines(fname_txt);
    curflines = replace(curflines,'\',sep);
    curflines = replace(curflines,'/',sep);

    fprintf("处理类别 ID=%d: %s (总样本=%d)\n", iRow, clsname, length(curflines));

    % 噪声增强的数据为前gennoised_datanum条
    noised_lines = curflines(1:gennoised_datanum);

    % 除去前gennoised_datanum条后的数据
    remaining_Lines = curflines(gennoised_datanum+1:end);
    nRemaining = length(remaining_Lines);

    % --- 按前缀分组 ---
    prefixGroups = containers.Map();
    for j = gennoised_datanum+1:length(curflines)
        line = curflines{j};
        match = regexp(line, '(CJ[0-9]_[0-9]+[MK])_', 'tokens');
        if ~isempty(match)
            prefix = match{1}{1};
            if ~isKey(prefixGroups, prefix)
                prefixGroups(prefix) = [];
            end
            prefixGroups(prefix) = [prefixGroups(prefix); j];
        else %手动添加的记录
            sExtra = "CJ_BYHAND";
            if ~isKey(prefixGroups, sExtra)
                prefixGroups(sExtra) = [];
            end
            prefixGroups(sExtra) = [prefixGroups(sExtra); j];
        end
    end
    prefixes = keys(prefixGroups);
    fprintf("检测到前缀: %s\n", strjoin(prefixes, ', '));

    % 用于其他增强的数据索引
    aug_Indices = [];
    % 基本数据索引
    base_Indices = [];

    % 按前缀每类抽取genaugment_datanum条
    for k = 1:length(prefixes)
        prefix = prefixes{k};
        groupIndices = prefixGroups(prefix);
        if length(groupIndices) >= genaugment_datanum
            % 按距离抽取genaugment_datanum条
            step = ceil(length(groupIndices) / genaugment_datanum);
            selectedIndices = 1:step:length(groupIndices);
            selectedIndices = selectedIndices(1:min(3, length(selectedIndices)));
            aug_Indices = [aug_Indices; groupIndices(selectedIndices)];
            % 剩余的作为基本集
            remainingIndices = setdiff(1:length(groupIndices), selectedIndices);
            base_Indices = [base_Indices; groupIndices(remainingIndices)];
        else
            % 如果不足genaugment_datanum条，全部作为基本集
            base_Indices = [base_Indices; groupIndices];
        end
    end

    % 获取相应的行数据
    aug_lines = curflines(aug_Indices);
    base_lines = curflines(base_Indices);

    % 定义输出文件路径
    res_output_dir = fullfile(output_dir, clsname);
    out_base = fullfile(res_output_dir, 'base.txt');    % 基本数据
    out_noised = fullfile(res_output_dir, 'noised.txt');   % 用于噪声增强
    out_aug = fullfile(res_output_dir, 'augment.txt');     % 用于其他增强


    % 确保输出目录存在
    if ~exist(res_output_dir, 'dir')
        mkdir(res_output_dir);
    end

    % 写入基本数据
    writelines(base_lines, out_base, 'WriteMode', 'overwrite');
    % 写入噪声增强
    writelines(noised_lines, out_noised, 'WriteMode', 'overwrite');
    % 写入其他增强
    writelines(aug_lines, out_aug, 'WriteMode', 'overwrite');



    fprintf('类别 %s 处理完成，已保存到相应文件\n', clsname);
end

fprintf('\n===== 所有类别处理完成 =====\n');
