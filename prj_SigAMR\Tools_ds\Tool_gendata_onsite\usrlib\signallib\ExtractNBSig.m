function [sync_sig_td] = ExtractNBSig(sig,wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs, nb_len_set)
%    function:    ExtractNBSig
%    Description: 从宽窄带信号中提取窄带信号（时域）
%
%    Params：
%             sig,      -- I/Q信号
%             wb_fc,    -- 宽带信号中心频率
%             wb_bw,    -- 宽带信号带宽
%             wb_fs,    -- 宽带信号采样率
%             nb_fc,    -- 窄带信号中心频率
%             nb_bw,    -- 窄带信号带宽
%             nb_fs,    -- 窄带信号采样率
%
wb_n_sig = length(sig);
wb_sig_fd = fft(sig);
wb_sig_fd = circshift(wb_sig_fd, floor(length(wb_sig_fd)/2));   %宽频域带信号
if exist('nb_len_set','var')>0
    nb_n_sig = nb_len_set;
else
    nb_n_sig = floor(wb_n_sig*(nb_fs/wb_fs));          %采样率换算，计算窄带信号长度
    nb_fs = 2*nb_bw;  % 重新计算采样率
end
%nb_n_sig = 60000;%固定取10ms,61.44e6*0.001=60000

try
    [ret, nb_sig_fd] = ExtractNarrowbandSignal(...
        wb_sig_fd, wb_fc, wb_bw, wb_fs, nb_n_sig, nb_fc, nb_bw, nb_fs, 1); %提取窄带信号
catch ME
    rethrow(ME);
end

if ret ~= 1 || isempty(nb_sig_fd)%无效的窄带信号
    sync_sig_td = [];
    return
end

nb_sig_td = ifft(nb_sig_fd) * nb_n_sig;     % 将信号转换到时域
pow_norm_factor = 1/ ((wb_n_sig));              % 1/折算到宽带信号采样点数
nb_sig_td = nb_sig_td * pow_norm_factor;    % 将信号功率归一化
sync_sig_td = nb_sig_td;   % 截取信号


end