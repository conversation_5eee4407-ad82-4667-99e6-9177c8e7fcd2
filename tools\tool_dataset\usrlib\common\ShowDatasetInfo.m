function ShowDatasetInfo(fname_dataset)
%  Function    ：ShowDatasetInfo
%  Description : 显示数据库文件内容
%  Parameter   : fname_dataset       -- 数据文件名称
%  Return      :
%                数据表内容显示
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-27

[rd_sig,class_ids,class_names, arrayfc, arrayfs, arraybw] = RdTrainSig(fname_dataset);
rowcount = length(class_ids);
fprintf("数据集名称: %s 记录个数:%d\n", fname_dataset, rowcount);
for rowid = 1:rowcount
    fs = arrayfs(rowid);
    fc = arrayfc(rowid);
    bw = arraybw(rowid);
    signal_in = rd_sig(1,:,rowid)+1j*rd_sig(2,:,rowid);
    len_clip = length(signal_in);
    fs = double(fs);fc = double(fc);bw = double(bw);
    sfilename = class_names(rowid);
    fprintf("序号:%d \t 采集文件:%s \t fc=%.2f MHz \t fs=%.2f M \t BW=%.2f K \t 分类ID:%d \t 数据点数:%d \t 持续时间为%.2f ms\n",rowid,sfilename,fc/1e6,fs/1e6, bw/1e3, class_ids(rowid), len_clip, len_clip*1e3/fs);
end
end

