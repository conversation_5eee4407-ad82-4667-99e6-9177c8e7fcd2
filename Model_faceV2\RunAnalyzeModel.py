
from usrlib.usrlib import read_path_config
from utils.eval.ConfusionMatrix import Analyzer
import os
import numpy as np
from usrlib.usrlib import *
from utils.dataloader import SigNbDataset, LSWDataset, SigNbDataset_collate
from torch.utils.data import DataLoader

def main():
    clsdef_dir, _, _, annotation_path_test, _, _, _ = read_path_config()
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    # 模型路径
    model_path = './logs/history-0609/Mtype0-ep099-loss0.007-val_loss0.006.pth'
    # 结果保存目录
    save_dir = "./confusion_analysis_results/"
    print(f"模型路径: {model_path}")
    print(f"测试数据: {annotation_path_test}")
    print(f"分析结果保存至: {save_dir}")
    print("="*60)
    # note：分类模型输出混淆矩阵时设置为modeltype=0；分类模型获得PCA的时候设置modeltype=1(可以这样设置的原因是分类和匹配的主干网络维度全部一致）
    # 1. 混淆矩阵任务
    analyzer_consufiosn = Analyzer(model_path=model_path, clsdef_file=clsdef_file, modeltype=0,task='CM',file_test=annotation_path_test)
    analyzer_consufiosn.run_comfusionmatrix_analysis(save_dir)
    # 2. PCA任务和DBI计算
    analyzer_pca = Analyzer(model_path=model_path, clsdef_file=os.path.join(clsdef_dir, 'class_def.txt'), modeltype=1, task='PCA',file_test=annotation_path_test)
    analyzer_pca.run_plotpca(save_dir)

if __name__ == "__main__":
    main()