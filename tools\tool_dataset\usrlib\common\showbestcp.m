function [vmaxcp, pos_cp] = showbestcp(signal_td, N_fft, cp_range, bShowfigure, cur_SymID)
%  Function    ：showbestcp
%  Description : 计算相关值，并显示图表
%                主要目的为：假定cp后，利用相关曲线，如CP达不到正确长度，会出现相关峰平台，
%                           如CP超过正确长度，会出现尖峰值
%                原理：
%                     采用滑动窗口取法，依次判别相关峰值
%                     如果cp取足够长如100，就会出现多个相关峰情况，峰间隔为fft+cp
%
%  Parameter   : signal_td    -- 复信号(时域)
%                    N_fft    -- FFT长度
%                  cp_range   -- cp范围
%
%  output      : vmaxcp       -- 最大cp值
%                pos_cp       -- 最大cp值对应位置
%
%  Author      : Liuzhiguo
%  Date        : 2024-07-01
%

vals_sel = zeros(1,length(cp_range));
k=1;
for ncp_len=cp_range
    [corr_param] = getcpcorrvals(signal_td, N_fft, ncp_len); % cp相关值（前后点移动）
    [v1, pos]     = max(corr_param);                         % 求最大相关值
    vals_sel(k)  = v1;
    k = k+1;
end
[vmaxcp, pos_cp] = maxk(vals_sel, 10);
pos_cp = pos_cp + cp_range(1)-1;% 未考虑绝对位置，需要换算

if bShowfigure==true

    figure;
    plot(cp_range, vals_sel);
    xlabel('cp值');
    ylabel('对应的相关值');
    stitle = sprintf('第%d符号-不同cp的最佳相关值',cur_SymID);
    title(stitle);
end

end

