# =======================================================================================================================
#   Function    ：PredictClass.py
#   Description : 模型预测推理代码
#                 传入hdf5文件路径，并对modelTrain.py调用
# 
#   Parameter   : ../data/predict/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2024-08-27
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from net.modelDesign import *
from usrlib.usrlib import *
from usrlib.dataloader import *
import sys
print(sys.argv)
import getopt

modeltype = 0 #模型类别 0:分类 1:角向量
clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件
fname_model = "DroneSigCls.pth"
dataset_file = "" # 数据文件路径

if __name__ == "__main__":
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)


    # 命令行方式获取测试文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['modelname=','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modelname'):
            fname_model = opt_value
            print("The modelname is ", fname_model)
        elif opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something


    #1. 路径及模型参数
    model_dir = './PretrainedModel/'

    testcase1 = 2
    if dataset_file=="":
        if testcase1==0:
            dataset_name ='nb-gendataset-S1.hdf5'
            datasettype = 'test' # train, test
            subdir = 'gendataset' # base,noised,multipath
            predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)
        elif testcase1==1:
            dataset_name ='nb-test433-odoor-S1.hdf5'
            datasettype = 'test' # train, test
            subdir = 'sam' # base,noised,multipath
            predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)    
        elif testcase1==2:
            dataset_name ='fchanscan-S1.hdf5'
            datasettype = 'test' # train, test
            subdir = 'sam' # base,noised,multipath
            predict_dataset_dir = 'E:\\project\\tool_dataset\\outdataset\\{0}\\{1}'.format(datasettype, subdir)       
        dataset_file = os.path.join(predict_dataset_dir, dataset_name)


    model_saved = os.path.join(model_dir, fname_model)

    batch_size = 16 #256  

    #2. 读取数据文件
    t0 = time.time()
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    rx_signal,classid_gt,class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file)

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  

    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    #3.读取模型文件
    Model = DroneSig_classifier(nc=cls_count, ModelType=modeltype)
    Init_model(Model, True, model_saved)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))
    
    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y, afs_value, abw_value)
            output_all.append(modelOutput.softmax(dim=1))
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    print("True Labels:\n{0}".format(classid_gt.reshape(1,-1)))

    val_predict = torch.argmax(output_all,dim=1)
    val_predict1 = torch.argsort(output_all,dim=1,descending=True)
    print("The sorted predictions:\n{0}".format(val_predict.reshape(1,-1).cpu().numpy()))
    val_label = torch.Tensor(classid_gt).cuda()
    val_label=val_label.squeeze()
    val_score = torch.where(val_predict == val_label, 1.0, 0.0)
    val_acc = torch.mean(val_score)
    val_acc = val_acc.cpu().numpy()
    t4 = time.time()
    print("count score：{:.2f}s".format(t4 - t3))
    print('predict_result: acc= {:.2f}%'.format(val_acc*100))
    nCount = val_predict.shape[0]
    if nCount>3:
        nCount=3

    print('list the top predict results:')
    for i in np.arange(nCount):
        print("predict val:{0} {1}".format(val_predict[i].item(),cls_names[val_predict[i]]))

    total_params_deep = "{:,}".format(sum(p.numel() for p in Model.parameters()))
    print(f"Model parameters: {total_params_deep}")