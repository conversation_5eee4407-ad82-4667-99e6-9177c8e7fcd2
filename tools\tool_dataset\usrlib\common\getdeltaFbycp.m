function [array_df, cfo] = getdeltafbycp(signal_seq, N_fft, cp_len, fs)
%  Function    ：getdeltaFbycp
%  Description : 根据CP估算频偏df的值
%  Parameter   : signal_seq   -- 输入数据   (I/Q序列)
%                cp_len       -- CP长度
%                fs           -- 采样率 
%
%  Author      : Liuzhiguo
%  Date        : 2024-07-01

array_corr = zeros(cp_len,1);
Ns =  N_fft;                      % cp序列间隔
Ts =  1/fs;                       % 采样时间
for i=1:cp_len                    % cp长度个相同序列
    rn       = signal_seq(i);
    rm       = signal_seq(i+Ns);
    corr_rnm = rm*conj(rn);       %点相关
    array_corr(i) = corr_rnm;     %记录点 
end

array_df = phase(array_corr)/(2*pi*Ns*Ts);%求得各个点df值

cp = signal_seq(1:cp_len);
copy_cp = signal_seq(N_fft+1 : N_fft+cp_len);
cfo = angle(dot(cp, copy_cp)) / N_fft*fs/2/pi; 

end

