
# =======================================================================================================================
#   Function    ：MyHelper.py
#   Description : 预测结果显示及保存帮助类
#                 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-05-08
# =======================================================================================================================
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import matplotlib
import os
import shutil
from datetime import datetime
import numpy as np
import re
import soundfile as sf
from mutagen.wave import WAVE
from datetime import datetime
from mutagen.id3 import ID3, TIT2, TPE1, COMM, TALB, TDRC, TENC
import h5py

def SetStaticsPlot(cls_count, cls_names, all_predicts, num_samp, num_modelcheck, ax, fig, sExtra):

    # 生成图例标签
    legend_labels = [f"{i}: {cls_names[i]}" for i in range(cls_count)]

    # 选择颜色映射
    colormap = cm.get_cmap('tab20', cls_count)

    # 生成每个柱子对应的颜色
    colors = [colormap(i) for i in range(cls_count)]

    # 绘制柱状图，设置不同颜色
    bars = ax.bar(range(cls_count), all_predicts, color=colors)

    # 清除可能存在的旧文本标签
    for text in ax.texts:
        text.remove()

    # 在每个柱子中心添加数值标签
    for bar in bars:
        height = bar.get_height()
        if height > 0:
            ax.text(bar.get_x() + bar.get_width() / 2, height / 2,
                    f'{height}', ha='center', va='center')

    # 添加图例
    ax.legend(bars, legend_labels, loc='upper right', bbox_to_anchor=(1, 1))

    # 设置图表标题和坐标轴标签
    ax.set_title('{0} 预测结果:采集次数={1},检测次数={2},发现次数={3}'.format(sExtra, num_samp, num_modelcheck, sum(all_predicts)))
    ax.set_xlabel('类别')
    ax.set_ylabel('预测次数')

    # 设置 x 轴刻度为类别数字
    ax.set_xticks(range(cls_count))

    try :
        fig.canvas.draw_idle()
    except Exception as e:
        print(f"fig.canvas.draw_idle: {e}")



# 显示统计结果
def ShowStaticsPlot(cls_count, cls_names, all_predicts, num_samp):

    # 生成图例标签
    legend_labels = [f"{i}: {cls_names[i]}" for i in range(cls_count)]

    # 选择颜色映射
    colormap = cm.get_cmap('tab20', cls_count)

    # 生成每个柱子对应的颜色
    colors = [colormap(i) for i in range(cls_count)]

    # 绘制柱状图，设置不同颜色
    plt.figure(figsize=(10, 6))
    bars = plt.bar(range(cls_count), all_predicts, color=colors)

    # 添加图例
    plt.legend(bars, legend_labels, loc='upper right', bbox_to_anchor=(1.2, 1))

    # 设置图表标题和坐标轴标签
    plt.title('预测结果统计,采集次数={0},发现次数={1}'.format(num_samp, sum(all_predicts)))
    plt.xlabel('类别')
    plt.ylabel('预测次数')

    # 设置 x 轴刻度为类别数字
    plt.xticks(range(cls_count))

    # 显示图表
    plt.tight_layout()
    plt.show()

# 拷贝hdfs文件到指定文件夹
def copyhdfs2folder(dataset_name, predict_cls):
    # 定义文件夹和文件路径
    folder_path = 'saved_hdfs'
    file_to_copy = dataset_name

    # 判断文件夹是否存在，不存在则创建
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    # 获取当前时间并格式化 %f：代表微秒数，范围从 000000 到 999999。
    now = datetime.now()
    current_time = "{:02d}-".format(predict_cls)+now.strftime("%Y%m%d-%H%M%S-") + str(now.microsecond)[:3] #毫秒部分，可以截取微秒字符串的前三位
    new_file_name = f"{current_time}.hdf5"
    new_file_path = os.path.join(folder_path, new_file_name)

    # 复制文件到指定文件夹并修改文件名
    try:
        shutil.copy2(file_to_copy, new_file_path)
        print(f"将 {file_to_copy} 复制到 {new_file_path}")
    except FileNotFoundError:
        print(f"错误: 未找到 {file_to_copy} 文件。")
    except Exception as e:
        print(f"错误: 发生了一个未知错误: {e}")
    
    return new_file_path

def copy_source_file_to_datafiles(source_file_path, class_name):
    """
    将源数据文件复制到config/DataFiles/类名/文件夹中

    Args:
        source_file_path (str): 源文件的完整路径
        class_name (str): 类名，用作子文件夹名称

    Returns:
        tuple: (success, copied_file_path, error_msg)
            - success (bool): 是否复制成功
            - copied_file_path (str): 复制后的文件路径，失败时为None
            - error_msg (str): 错误信息，成功时为空字符串
    """
    try:
        # 检查源文件是否存在
        if not source_file_path or not os.path.exists(source_file_path):
            return False, None, f"源文件不存在: {source_file_path}"

        # 获取config目录路径
        from usrlib.usrlib import get_config_path
        config_file_path = get_config_path()
        config_dir = os.path.dirname(config_file_path)

        # 构建目标目录路径：config/DataFiles/类名/
        datafiles_dir = os.path.join(config_dir, "DataFiles")
        class_dir = os.path.join(datafiles_dir, class_name)

        # 创建目录结构（如果不存在）
        os.makedirs(class_dir, exist_ok=True)
        print(f"确保目录存在: {class_dir}")

        # 获取源文件名和扩展名
        source_filename = os.path.basename(source_file_path)
        name_part, ext_part = os.path.splitext(source_filename)

        # 构建目标文件路径
        target_file_path = os.path.join(class_dir, source_filename)

        # 处理文件名冲突
        counter = 1
        while os.path.exists(target_file_path):
            # 如果文件已存在，添加时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"{name_part}_{timestamp}{ext_part}"
            target_file_path = os.path.join(class_dir, new_filename)

            # 如果时间戳文件名还冲突，添加序号
            if os.path.exists(target_file_path):
                new_filename = f"{name_part}_{timestamp}_{counter:03d}{ext_part}"
                target_file_path = os.path.join(class_dir, new_filename)
                counter += 1

                # 防止无限循环
                if counter > 999:
                    return False, None, f"无法生成唯一文件名: {source_filename}"

        # 复制文件（保持元数据）
        shutil.copy2(source_file_path, target_file_path)

        # 验证复制是否成功
        if os.path.exists(target_file_path):
            # 检查文件大小是否一致
            source_size = os.path.getsize(source_file_path)
            target_size = os.path.getsize(target_file_path)

            if source_size == target_size:
                print(f"成功复制源文件: {source_file_path} -> {target_file_path}")
                return True, target_file_path, ""
            else:
                # 文件大小不一致，删除目标文件
                os.remove(target_file_path)
                return False, None, f"文件复制不完整，大小不匹配: 源文件{source_size}字节 vs 目标文件{target_size}字节"
        else:
            return False, None, f"文件复制失败，目标文件不存在: {target_file_path}"

    except PermissionError as e:
        return False, None, f"权限错误: {str(e)}"
    except OSError as e:
        return False, None, f"文件系统错误: {str(e)}"
    except Exception as e:
        return False, None, f"复制文件时发生未知错误: {str(e)}"

def save_array_to_wavfile(complex_data, sample_rate=30720000, output_file='output.wav',
                         title='NB433', artist='4', comment='bw=256e3,fc=433e6'):
    """
    将复数数组写入WAV文件，并添加元数据信息
    
    参数:
    complex_data (array-like): 输入的复数数组
    sample_rate (int): 采样率，默认为30.72MHz
    output_file (str): 输出文件名，默认为'output.wav'
    title (str): 音频标题元数据
    artist (str): 艺术家元数据
    comment (str): 注释元数据
    """
    # 确保输入是numpy数组
    complex_data = np.asarray(complex_data)
    
    # 将复数拆分为实部和虚部，分别作为左右声道
    left_channel = complex_data.real
    right_channel = complex_data.imag
    
    # 归一化到[-1, 1]范围
    max_val = max(np.max(np.abs(left_channel)), np.max(np.abs(right_channel)))
    if max_val > 0:
        left_channel = left_channel / max_val
        right_channel = right_channel / max_val
    
    # 组合为立体声信号
    stereo_data = np.column_stack((left_channel, right_channel))
    
    # 写入WAV文件（不包含元数据）
    try:
        sf.write(output_file, stereo_data, sample_rate, subtype='FLOAT')
        print(f"成功写入WAV文件: {output_file}")
        # print(f"采样率: {sample_rate} Hz")
        # print(f"数据长度: {len(complex_data)} 样本")
        # print(f"文件格式: 32位浮点型，立体声")
    except Exception as e:
        print(f"写入文件时出错: {e}")
        return
    
    # # 使用mutagen添加元数据
    # try:
    #     audio = WAVE(output_file)
    #     audio.add_tags()
    #     tags = audio.tags
    #     tags['TITLE'] = title
    #     tags['ARTIST'] = artist
    #     tags['COMMENT'] = comment
    #     tags['ALBUM'] = 'Complex Data Series'
    #     tags['DATE'] = datetime.now().strftime('%Y-%m-%d')
    #     tags['ENCODER'] = f'Python soundfile {sf.__version__}'
    #     tags['BITSPERSAMPLE'] = '32'
    #     audio.save()
    #     print(f"元数据已添加: Title='{title}', Artist='{artist}', Comment='{comment}'")
    # except Exception as e:
    #     print(f"添加元数据时出错: {e}")
    #     print("提示: 某些播放器可能不支持WAV文件的元数据显示") #TypeError: '123' not a Frame instance

         # 使用mutagen添加元数据
    try:
        # 创建ID3对象并添加适当的帧
        audio = ID3()
        audio.add(TIT2(encoding=3, text=title))  # 标题
        audio.add(TPE1(encoding=3, text=artist))  # 艺术家
        audio.add(TALB(encoding=3, text='Complex Data Series'))  # 专辑
        audio.add(TDRC(encoding=3, text=datetime.now().strftime('%Y-%m-%d')))  # 日期
        audio.add(TENC(encoding=3, text=f'Python soundfile {sf.__version__}'))  # 编码器
        audio.add(COMM(encoding=3, lang='eng', desc='', text=comment))  # 注释
        
        # 将ID3标签保存到WAV文件
        audio.save(output_file)
        #print(f"元数据已添加: Title='{title}', Artist='{artist}', Comment='{comment}'")
    except Exception as e:
        print(f"添加元数据时出错: {e}")
        print("提示: 某些播放器可能不支持WAV文件的元数据显示")

def save_signal_to_hdf5(filepath: str, signal: np.ndarray, fc: float, fs: float, bw: float) -> None:
    """
    将信号数据保存到 HDF5 文件
    
    参数:
        filepath: 保存文件的路径
        signal: 要保存的信号数据，应为 numpy 数组
        fc: 中心频率
        fs: 采样率
    
    返回:
        None
    """
    try:
        timestamp = datetime.now().isoformat()
        with h5py.File(filepath, "w") as hf:
            hf.create_dataset("iq_samples", data=signal.astype(np.complex64))
            hf.attrs.update({
                "fc": fc,
                "fs": fs,
                "bw": bw,
                "timestamp": timestamp
            })
        print(f"信号数据已成功保存到 {filepath}")
    except Exception as e:
        print(f"保存文件时出错: {e}")

def read_signal_from_hdf5(filepath: str) -> tuple[np.ndarray, dict[str, any]]:
    """
    从 HDF5 文件读取信号数据和元信息
    
    参数:
        filepath: 要读取的文件路径
    
    返回:
        tuple: 包含两个元素的元组
            - 第一个元素是信号数据的 numpy 数组
            - 第二个元素是包含元信息的字典

    example:  loaded_signal, metadata = read_signal_from_hdf5("signal_data.h5")

    """
    try:
        with h5py.File(filepath, "r") as hf:
            # 读取信号数据
            signal = hf["iq_samples"][:]
            
            # 读取元信息
            metadata = dict(hf.attrs.items())
            
            # 尝试将 timestamp 转换为 datetime 对象
            if "timestamp" in metadata:
                try:
                    metadata["timestamp"] = datetime.fromisoformat(metadata["timestamp"])
                except ValueError:
                    print("警告: 无法将时间戳转换为 datetime 对象")
        
        print(f"成功从 {filepath} 读取信号数据")
        return signal, metadata
    except FileNotFoundError:
        print(f"错误: 文件 {filepath} 不存在")
        return np.array([]), {}
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return np.array([]), {}


def clean_database_path_string(path_str):
    """
    清理从数据库读取的路径字符串，移除字节字符串标记和多余的引号

    Args:
        path_str: 可能包含b'...'格式的路径字符串或numpy数组

    Returns:
        str: 清理后的纯字符串路径
    """
    if path_str is None:
        return ""

    # 处理numpy数组格式
    if hasattr(path_str, 'shape') and len(path_str.shape) > 0:
        if path_str.shape[0] > 0:
            path_str = path_str[0]
        else:
            return ""
    elif isinstance(path_str, (list, tuple)) and len(path_str) > 0:
        path_str = path_str[0]

    # 转换为字符串
    if isinstance(path_str, bytes):
        path_str = path_str.decode('utf-8')
    else:
        path_str = str(path_str)

    # 移除字节字符串标记 b'...' 或 b"..."
    path_str = re.sub(r"^b['\"](.*)['\"]\s*$", r'\1', path_str)

    # 移除多余的引号
    path_str = path_str.strip('\'"')

    # 标准化路径分隔符
    path_str = path_str.replace('\\', '/')

    return path_str


def clean_database_class_name(class_name):
    """
    清理从数据库读取的类名字符串，移除字节字符串标记和多余的引号

    Args:
        class_name: 可能包含b'...'格式的类名字符串或numpy数组

    Returns:
        str: 清理后的纯字符串类名
    """
    if class_name is None:
        return ""

    # 处理numpy数组格式
    if hasattr(class_name, 'shape') and len(class_name.shape) > 0:
        if class_name.shape[0] > 0:
            class_name = class_name[0]
        else:
            return ""
    elif isinstance(class_name, (list, tuple)) and len(class_name) > 0:
        class_name = class_name[0]

    # 转换为字符串
    if isinstance(class_name, bytes):
        class_name = class_name.decode('utf-8')
    else:
        class_name = str(class_name)

    # 移除字节字符串标记 b'...' 或 b"..."
    class_name = re.sub(r"^b['\"](.*)['\"]\s*$", r'\1', class_name)

    # 移除多余的引号
    class_name = class_name.strip('\'"')

    return class_name


def check_file_dependencies(file_path):
    """
    检查指定文件在数据库中的依赖关系

    Args:
        file_path (str): 要检查的文件路径

    Returns:
        tuple: (dependency_count, dependent_records, error_msg)
            - dependency_count (int): 依赖此文件的记录数量
            - dependent_records (list): 依赖记录的详细信息列表
            - error_msg (str): 错误信息，成功时为空字符串
    """
    try:
        from usrlib.usrlib import GetArcVectorDB

        # 获取数据库内容
        db_result = GetArcVectorDB()
        if db_result is None:
            return 0, [], "无法读取数据库内容"

        # 兼容新旧格式的数据库
        if len(db_result) == 9:  # 最新版本数据库（包含record_id）
            vectors, record_ids, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
        elif len(db_result) == 8:  # 中间版本数据库（包含样本字段但无record_id）
            vectors, clsids, clsnames, filepaths, curfs, curbw, start_samples, end_samples = db_result
        else:  # 旧版本数据库
            vectors, clsids, clsnames, filepaths, curfs, curbw = db_result

        if filepaths is None or len(filepaths) == 0:
            return 0, [], ""

        # 标准化文件路径以便比较
        normalized_target = os.path.normpath(file_path).replace('\\', '/')

        dependent_records = []
        dependency_count = 0

        # 遍历所有记录检查文件路径
        for i, filepath_entry in enumerate(filepaths):
            # 处理不同的数据格式
            if hasattr(filepath_entry, 'shape') and len(filepath_entry.shape) > 0:
                # numpy数组格式
                if filepath_entry.shape[0] > 0:
                    current_path = filepath_entry[0]
                else:
                    continue
            elif isinstance(filepath_entry, (list, tuple)) and len(filepath_entry) > 0:
                current_path = filepath_entry[0]
            else:
                current_path = filepath_entry

            # 清理路径字符串
            current_path = clean_database_path_string(current_path)

            # 标准化当前路径
            normalized_current = os.path.normpath(current_path).replace('\\', '/')

            # 检查路径是否匹配
            if normalized_current == normalized_target:
                dependency_count += 1

                # 获取记录详细信息
                class_id = clsids[i][0] if hasattr(clsids[i], '__len__') else clsids[i]
                class_name_raw = clsnames[i][0] if hasattr(clsnames[i], '__len__') else clsnames[i]
                class_name = clean_database_class_name(class_name_raw)

                fs_val = curfs[i][0] if hasattr(curfs[i], '__len__') else curfs[i]
                bw_val = curbw[i][0] if hasattr(curbw[i], '__len__') else curbw[i]

                record_info = {
                    'index': i,
                    'class_id': int(class_id),
                    'class_name': str(class_name),
                    'file_path': current_path,
                    'sample_rate': float(fs_val) if fs_val is not None else 0.0,
                    'bandwidth': float(bw_val) if bw_val is not None else 0.0
                }
                dependent_records.append(record_info)

        print(f"文件 {file_path} 被 {dependency_count} 条记录依赖")

        return dependency_count, dependent_records, ""

    except Exception as e:
        import traceback
        error_msg = f"检查文件依赖时发生错误: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return 0, [], error_msg


def delete_source_file_safely(file_path, force_delete=False):
    """
    安全删除源文件，包括依赖检查和文件夹清理

    Args:
        file_path (str): 要删除的文件路径
        force_delete (bool): 是否强制删除（忽略依赖检查）

    Returns:
        tuple: (success, deleted_items, error_msg)
            - success (bool): 是否删除成功
            - deleted_items (list): 删除的项目列表（文件和文件夹）
            - error_msg (str): 错误信息，成功时为空字符串
    """
    try:
        deleted_items = []

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return False, [], f"文件不存在: {file_path}"

        # 如果不是强制删除，先检查依赖关系
        if not force_delete:
            dependency_count, dependent_records, dep_error = check_file_dependencies(file_path)

            if dep_error:
                return False, [], f"检查文件依赖失败: {dep_error}"

            if dependency_count > 0:
                record_info = []
                for record in dependent_records:
                    record_info.append(f"记录{record['index']}: class_id={record['class_id']}, class_name={record['class_name']}")

                error_msg = f"文件被 {dependency_count} 条记录依赖，无法删除:\n" + "\n".join(record_info)
                return False, [], error_msg

        # 删除文件
        try:
            os.remove(file_path)
            deleted_items.append(f"文件: {file_path}")
            print(f"已删除文件: {file_path}")
        except PermissionError:
            return False, [], f"权限不足，无法删除文件: {file_path}"
        except Exception as e:
            return False, [], f"删除文件失败: {str(e)}"

        # 检查并清理空文件夹
        parent_dir = os.path.dirname(file_path)

        # 从当前文件夹开始，向上检查并删除空文件夹
        while parent_dir and parent_dir != os.path.dirname(parent_dir):  # 防止删除根目录
            try:
                # 检查文件夹是否为空
                if os.path.exists(parent_dir) and not os.listdir(parent_dir):
                    # 检查是否是DataFiles下的类名文件夹
                    if 'DataFiles' in parent_dir:
                        os.rmdir(parent_dir)
                        deleted_items.append(f"空文件夹: {parent_dir}")
                        print(f"已删除空文件夹: {parent_dir}")

                        # 继续检查上级目录
                        parent_dir = os.path.dirname(parent_dir)
                    else:
                        # 不是DataFiles下的文件夹，停止清理
                        break
                else:
                    # 文件夹不为空或不存在，停止清理
                    break
            except OSError as e:
                # 无法删除文件夹（可能有权限问题），停止清理
                print(f"无法删除文件夹 {parent_dir}: {e}")
                break

        return True, deleted_items, ""

    except Exception as e:
        import traceback
        error_msg = f"删除文件时发生错误: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return False, [], error_msg


def batch_check_file_dependencies(file_paths):
    """
    批量检查多个文件的依赖关系

    Args:
        file_paths (list): 要检查的文件路径列表

    Returns:
        tuple: (success, dependency_summary, error_msg)
            - success (bool): 是否检查成功
            - dependency_summary (dict): 依赖关系摘要
            - error_msg (str): 错误信息，成功时为空字符串
    """
    try:
        dependency_summary = {
            'total_files': len(file_paths),
            'files_with_dependencies': 0,
            'files_without_dependencies': 0,
            'total_dependencies': 0,
            'file_details': {}
        }

        for file_path in file_paths:
            dependency_count, dependent_records, error = check_file_dependencies(file_path)

            if error:
                return False, {}, f"检查文件 {file_path} 的依赖关系失败: {error}"

            dependency_summary['file_details'][file_path] = {
                'dependency_count': dependency_count,
                'dependent_records': dependent_records
            }

            if dependency_count > 0:
                dependency_summary['files_with_dependencies'] += 1
                dependency_summary['total_dependencies'] += dependency_count
            else:
                dependency_summary['files_without_dependencies'] += 1

        print(f"批量依赖检查完成:")
        print(f"  总文件数: {dependency_summary['total_files']}")
        print(f"  有依赖的文件: {dependency_summary['files_with_dependencies']}")
        print(f"  无依赖的文件: {dependency_summary['files_without_dependencies']}")
        print(f"  总依赖数: {dependency_summary['total_dependencies']}")

        return True, dependency_summary, ""

    except Exception as e:
        import traceback
        error_msg = f"批量检查文件依赖时发生错误: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return False, {}, error_msg
