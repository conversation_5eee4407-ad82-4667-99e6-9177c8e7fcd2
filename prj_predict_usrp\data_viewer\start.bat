@echo off
chcp 65001
echo =========================================
echo    数据文件可视化查看工具 (含模型推理)
echo =========================================
echo.
echo 正在启动程序...
echo.

REM 配置conda环境名（如需修改请编辑下一行）
set CONDA_ENV=py39

echo 正在激活conda环境: %CONDA_ENV%
call conda activate %CONDA_ENV%

if %errorlevel% neq 0 (
    echo.
    echo 警告: conda环境激活失败，使用系统Python
    echo 请确认conda环境名是否正确: %CONDA_ENV%
    echo.
) else (
    echo conda环境激活成功
    echo.
)

python data_viewer.py

if %errorlevel% neq 0 (
    echo.
    echo 程序运行出现错误！
    echo 请检查以下问题：
    echo 1. Python 环境是否正确安装
    echo 2. 必需的库是否已安装：torch, numpy, scipy, matplotlib
    echo 3. data_viewer.py 文件是否存在
    echo.
    pause
) else (
    echo.
    echo 程序已正常退出。
)

pause 