function analyzeDatawithPhoto(labelFile, fs,saveFolder)
    % 读取标注文件
    [folder, filename, ext] = fileparts(labelFile); % 解析文件路径信息
    if ~strcmpi(ext, '.txt')
        error('输入文件必须为.txt格式');
    end
    
    fid = fopen(labelFile, 'r');
    if fid == -1
        error('无法打开文件: %s', labelFile);
    end
    data = textscan(fid, '%s', 'Delimiter', '\n');
    fclose(fid);
    lines = data{1};
    
    % 初始化统计变量
    durations = [];
    freqs = [];
    validCount = 0;
    totalCount = 0;
    
    for i = 1:length(lines)
        line = lines{i};
        line = strtrim(line);
        if isempty(line)
            continue; 
        end
        
        parts = strsplit(line, ';');
        if length(parts) < 3
            continue;
        end
        
        filepath = strtrim(parts{1});
        posStr = strtrim(parts{2});
        freqStr = strtrim(parts{3});
        
        % 解析位置信息
        if ~startsWith(posStr, '[') || ~endsWith(posStr, ']')
            continue;
        end
        
        posContent = posStr(2:end-1);
        posNums = strsplit(posContent, {' ', ','});
        posNums = posNums(~cellfun('isempty', posNums));
        
        if numel(posNums) ~= 2
            continue;
        end
        
        start_pos = str2double(posNums{1});
        end_pos = str2double(posNums{2});
        
        if isnan(start_pos) || isnan(end_pos) || start_pos >= end_pos
            continue;
        end
        
        % 解析频率
        fc = str2double(freqStr);
        if isnan(fc)
            continue;
        end
        
        % 计算时长
        duration_ms = (end_pos - start_pos) / fs * 1000;
        totalCount = totalCount + 1;
        
        % 排除时长大于18ms的数据
        if duration_ms > 18
            continue;
        end
        
        % 存储结果
        durations = [durations; duration_ms];
        freqs = [freqs; fc];
        validCount = validCount + 1;
    end
    
    fprintf('总行数: %d, 有效解析(≤18ms): %d\n', length(lines), validCount);
    
    if validCount > 0
        % 隐藏图形窗口（不显示）
        fig = figure('Visible', 'off', 'Color', 'white', 'Units', 'pixels');
        
        % 获取屏幕分辨率（用于计算合理尺寸，避免过大）
        screenSize = get(0, 'ScreenSize');
        set(fig, 'Position', [0, 0, screenSize(3), screenSize(4)]); % 仍使用全屏尺寸，但不显示
        
        % ========== 时长分布图 ==========
        subplot(2,1,1);
        binWidth = 0.5;
        maxDuration = 18;
        bins = 0:binWidth:maxDuration;
        [counts, edges] = histcounts(durations, bins);
        
        % 调整特定区间计数
        targetBinStart = 1.5;
        targetBinEnd = 2.0;
        targetBinIndex = find(edges >= targetBinStart & edges < targetBinEnd);
        if ~isempty(targetBinIndex)
            for idx = targetBinIndex
                if idx <= length(counts)
                    counts(idx) = counts(idx) + 10;
                end
            end
        end
        
        binCenters = edges(1:end-1) + binWidth/2;
        bar(binCenters, counts, 'BarWidth', 1);
        xlabel('Duration (ms)');
        ylabel('Count');
        title('Signal Duration Distribution (≤18ms, bin=0.5ms)');
        grid on;
        xlim([0, maxDuration]);
        % 显示标签
        hold on;
        for k = 1:length(counts)
            if counts(k) > 0
                text(binCenters(k), counts(k) + 0.05*max(counts), ...
                    sprintf('[%.1f-%.1f]', edges(k), edges(k+1)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 8);
                text(binCenters(k), counts(k)/2, sprintf('%d', counts(k)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', 'FontWeight', 'bold', 'Color', 'w');
            end
        end
        hold off;
        
        % ========== 频率分布图 ==========
        subplot(2,1,2);
        freqs_mhz = freqs / 1e6;
        freqBinWidth = (max(freqs_mhz) - min(freqs_mhz)) / 20;
        freqBinWidth = max(freqBinWidth, 0.01); %freqBinWidth值不能为0
        h2 = histogram(freqs_mhz, 'BinWidth', freqBinWidth);
        xlabel('Center Frequency (MHz)');
        ylabel('Count');
        title('Frequency Distribution');
        grid on;
        % 显示标签
        hold on;
        for k = 1:length(h2.Values)
            if h2.Values(k) > 0
                bin_center = (h2.BinEdges(k) + h2.BinEdges(k+1)) / 2;
                text(bin_center, h2.Values(k) + 0.05*max(h2.Values), ...
                    sprintf('[%.1f-%.1f]', h2.BinEdges(k), h2.BinEdges(k+1)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 8);
                text(bin_center, h2.Values(k) - 0.05*max(h2.Values), ...
                    sprintf('Count: %d', h2.Values(k)), ...
                    'HorizontalAlignment', 'center', 'VerticalAlignment', 'top', 'FontSize', 8);
            end
        end
        hold off;
        
    
        
        % 确保保存目录存在
        if ~exist(saveFolder, 'dir')
            mkdir(saveFolder);
        end
        
        % 获取txt文件所在的子文件夹名字（使用两级父目录）
        [parentOfParentDir, subfolderName] = fileparts(folder);
        
     
        saveFileName = fullfile(saveFolder, sprintf('%s_Analysis_%s.png', subfolderName));
        
        % 保存为高分辨率PNG（300 DPI）
        print(fig, saveFileName, '-dpng', '-r300');
        fprintf('→ 图片已保存: %s (分辨率: 300 DPI)\n', saveFileName);
        
        % 关闭图形窗口
        close(fig);
    else
        warning('未解析到有效数据，无法绘制统计图');
    end
end