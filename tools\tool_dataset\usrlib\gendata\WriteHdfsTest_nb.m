%  Function    ：WriteHdfsbase
%  Description : 写入基础训练数据(采集得到)
%  Parameter   : fname_rd       -- 写入文件名称
%  Return      :
%                rd_sig         -- 信号数据
%                class_id       -- 分类ID
%                class_name     -- 分类名称
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-13

% 1. 初始化命令
clc
clear
close all

addpath("lib");             %库函数路径
addpath("usrlib\common");   %用户自定义路径
addpath("usrlib\wb_evo2");  %用户自定义路径

% 2. 文件读入
% 2.1 文件目录读取
testcase = 1;%0: 数据方式 1:生成方式
if testcase==0
    testDB_dir = '.\datafiles\gendataset\';%信号路径
    fname_test = [testDB_dir,'test_gen.txt']; % 训练数据文件
    fname_class = [testDB_dir,'class_def.txt'];% 类别文件
    outpath = ".\outdataset\test\";
    outname = "nb-gen433";
elseif testcase==1
    testDB_dir = 'E:\ftproot\signalDB\';%信号路径
    fname_test = [testDB_dir,'test_case1.txt']; % 训练数据文件
    fname_class = [testDB_dir,'class_def.txt'];% 类别文件
    outpath = ".\outdataset\test\";
    outname = "nb-test433";
end

cls_size = 'S1';
fname_dataset = strcat(outpath,outname,'-',cls_size,'.hdf5');

if exist(fname_test,"file")>0
    Table_test = readtable(fname_test,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_test.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_test.lenPoints = Table_test(:,3).endpos-Table_test(:,2).startpos+1;
    Table_test = sortrows(Table_test,'lenPoints');
else
    error("文件：%s 不存在！", fname_test);
end

if exist(fname_class,"file")>0
    Table_cls = readtable(fname_class,'Format','%d %s');
    Table_cls.Properties.VariableNames = ["clsid","clsname"];
else
    sprintf("文件：%s 不存在！", fname_class);
end
indexPointer = 0;
cls_size = '';

nb_bw   = 4e6;      % 窄带信号的带宽
nb_fs   = 1.2*nb_bw;   % 窄带信号的采样率


%2.2 训练数据文件读取
for iRow = 1 : height(Table_test)
    sigfsubname          = char(Table_test(iRow,1).filename);%文件名称
    clsname          = split(sigfsubname,'\',1);       %文件类别名名称 

    sigfname         = [testDB_dir, sigfsubname];    %文件路径
    nStartpos_t      = Table_test(iRow,2).startpos;      %起始点
    nEndpos_t        = Table_test(iRow,3).endpos;      %结束点
    fc               = Table_test(iRow,4).fc;      %中心频率
    lenPoints        = Table_test(iRow,5).lenPoints;%长度
    
    if sigfname(end-2:end)=="mat"
        fs = 61.44e6;
        load(sigfname);
        wb_rxSig_clip = rx_data(nStartpos_t:nEndpos_t); %数据切片
    else
	    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
    	%showWBSig(wb_signal, wb_fc, wb_fs);
    	wb_rxSig_clip = wb_signal(nStartpos_t:nEndpos_t); %数据切片
    end
    if mod(length(wb_rxSig_clip),2)>0
        wb_rxSig_clip = wb_rxSig_clip(1:end-1);
    end
	
    nb_fc = fc;
    [nb_signal] = ExtractNBSig(wb_rxSig_clip, wb_fc, wb_bw, wb_fs,nb_fc, nb_bw, nb_fs);%信道化后数据
    %showNBSig(testcase, nb_sig_td, nb_fc, nb_fs); %显示窄带信号
    len_clip_nb = length(nb_signal);
    fs = nb_fs;     % 信号采样率
    fprintf("文件:%s 截取窄带化后信号: 数据点数为%d，持续时间为%.2f ms\r\n",sigfsubname,len_clip_nb,len_clip_nb*1e3/fs);

    cls_id = Table_cls(strcmp(Table_cls.clsname,clsname(1)),:).clsid;% 读取信号所属类别

    [cls_size, indexPointer] = getFileSizeClass(len_clip_nb, cls_size,indexPointer);
    fname_dataset = strcat(outpath,outname,'-',cls_size,'.hdf5');
    WrTrainSig(fname_dataset, nb_signal, cls_id, sigfsubname, fc, fs, indexPointer);  % 生成数据集，hdf文件格式
end
% varPoints = Table_train(:,3).endpos-Table_train(:,2).startpos;
% varNames  = Table_train(:,1).filename;
% selpart = {varNames; varPoints};
%3 读取hdf文件信息（测试用）
DispDatasetRecByChart(fname_dataset,1,"测试");

function [clsName,indexout]=getFileSizeClass(len,prev_clsName,index)
%
%
%分割成5个区间:[0,5e4],[5e4,10e4],[10e4,20e4],[20e4,40e4],[40e4,60e4],[60e4, -]
%             对应:S1-S5,S6
%
    if len < 5e4
        clsName = 'S1';
    elseif len < 10e4
        clsName = 'S2';
    elseif len < 20e4
        clsName = 'S3';
    elseif len < 40e4
        clsName = 'S4'; 
    elseif len < 60e4
        clsName = 'S5'; 
    else 
        clsName = 'S6'; 
    end
    %新文件，更换index
    if index==0%第1个，没有prev_clsName
        prev_clsName = clsName;
    end
    if prev_clsName==clsName
        indexout = index+1;
    else
        indexout = 1;
    end
end

