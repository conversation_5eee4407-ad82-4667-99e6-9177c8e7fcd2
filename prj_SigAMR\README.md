# prj_SigAMR - 信号自动调制识别系统

## 项目概述

prj_SigAMR (Signal Automatic Modulation Recognition) 是一个基于深度学习的信号调制方式自动识别系统，专门用于分析和识别无线通信信号的调制类型。该系统采用先进的深度学习算法，能够准确识别多种调制方式，如BPSK、QPSK、16QAM、GFSK和LoRa等。

## 系统特点

- **高精度识别**：采用深度学习模型实现高精度的调制方式识别
- **多种调制支持**：支持BPSK、QPSK、16QAM、GFSK、LoRa等多种调制方式
- **模型分析工具**：提供混淆矩阵和PCA特征可视化等模型分析功能
- **灵活的网络架构**：支持多种骨干网络，包括SigAMRNet、MobileFaceNet等
- **注意力机制**：集成CBAM、SE等注意力模块提升性能
- **跨平台支持**：支持Windows和Linux系统

## 目录结构

```
prj_SigAMR/
├── Model_V0/                  # 主要模型目录
│   ├── nets/                  # 网络模型定义
│   │   ├── SigAMRNet.py       # 信号调制识别网络
│   │   ├── modelInterface.py  # 模型接口
│   │   ├── Module_attentions.py # 注意力机制模块
│   │   └── ...                # 其他网络模型
│   ├── utils/                 # 工具函数
│   │   ├── dataloader.py      # 数据加载
│   │   ├── eval/              # 评估工具
│   │   │   ├── ConfusionMatrix.py # 混淆矩阵分析
│   │   │   └── tsnePlot.py    # 特征可视化
│   │   └── ...                # 其他工具函数
│   ├── usrlib/                # 用户库函数
│   ├── ModType_def.txt        # 调制类型定义
│   ├── pathsetting.json       # 路径配置
│   ├── predictSigModType.py   # 调制方式预测脚本
│   ├── RunAnalyzeModel.py     # 模型分析脚本
│   ├── TestModelNet.py        # 网络测试脚本
│   └── train.py               # 模型训练脚本
└── ...
```

## 核心模块

### 1. 网络模型 (SigAMRNet)

SigAMRNet是专为信号调制识别设计的深度学习网络，具有以下特点：

- **多层卷积结构**：采用多层卷积提取信号特征
- **注意力机制**：集成CBAM注意力模块增强特征表示
- **C2f模块**：使用CSP Bottleneck结构提高特征提取能力
- **时序特征提取**：使用GRU和自注意力机制提取时序特征
- **自适应池化**：使用自适应平均池化压缩特征

```python
# 网络结构示例
self.blocks = nn.Sequential()
conv1 = Conv(c1=self.in_ch, c2=self.out_ch, k=[1, 8])
AttenBlock = CBAM(self.out_ch)
# 多层C2f模块
block = C2f(c1=16, c2=16, shortcut=True)
# 时序特征提取
self.GRUT = nn.GRU(input_size=25, hidden_size=25, num_layers=1, batch_first=True)
self.attention = SelfAttention(hidden_dim=25)
```

### 2. 数据加载 (dataloader.py)

支持多种数据格式和加载方式：

- **HDF5数据加载**：支持从HDF5文件加载信号数据
- **分层数据划分**：按调制类型和SNR进行分层数据集划分
- **数据预处理**：支持数据归一化和下采样
- **批处理支持**：提供批处理数据加载器

```python
def read_hdf5_dataset(file_path):
    """读取HDF5数据集并进行预处理"""
    with h5py.File(file_path, 'r') as f:
        X = f['signal_data'][:]  # [len, 2, 样本数]
        Y = f['labels'][:]       # 标签 [1, 样本数]
        Z = f['SNRs'][:]         # SNR值 [1, 样本数]
        
        # 转换数据形状并下采样
        X = np.transpose(X, (2, 1, 0))
        X = X[:, :, np.newaxis, :]
        nstep = int(61.44e6//15.36e6)
        X = X[:,:,:,::nstep]
        
    return X, Y, Z, modulations
```

### 3. 模型分析工具 (RunAnalyzeModel.py)

提供全面的模型分析功能：

- **混淆矩阵分析**：生成原始和归一化混淆矩阵
- **PCA特征可视化**：使用PCA降维可视化特征分布
- **DBI指标计算**：计算Davies-Bouldin指数评估聚类质量
- **类别统计分析**：提供各类别样本数量和方差统计

```python
# 混淆矩阵分析
analyzer_consufiosn = Analyzer(model_path=model_path, clsdef_file=clsdef_file, task='CM', file_test=path_test_file)
analyzer_consufiosn.run_comfusionmatrix_analysis(save_dir)

# PCA分析和DBI计算
analyzer_pca = Analyzer(model_path=model_path, clsdef_file=clsdef_file, task='PCA', file_test=path_test_file)
analyzer_pca.run_plotpca(save_dir)
```

### 4. 模型训练 (train.py)

提供完整的模型训练流程：

- **多种优化器**：支持Adam和SGD优化器
- **学习率调度**：支持余弦和阶梯式学习率调度
- **混合精度训练**：支持FP16混合精度训练
- **分布式训练**：支持单机多卡分布式训练
- **训练监控**：提供损失历史记录和模型保存

```python
# 优化器选择
optimizer = {
    'adam': optim.Adam(model.parameters(), Init_lr_fit, betas=(momentum, 0.999), weight_decay=weight_decay),
    'sgd': optim.SGD(model.parameters(), Init_lr_fit, momentum=momentum, nesterov=True, weight_decay=weight_decay)
}[optimizer_type]

# 学习率调度
lr_scheduler_func = get_lr_scheduler(lr_decay_type, Init_lr_fit, Min_lr_fit, Epoch)
```

## 工作流程

### 1. 数据准备流程

```mermaid
graph TD
    A[原始信号数据] --> B[HDF5格式转换]
    B --> C[数据预处理]
    C --> D[分层数据划分]
    D --> E1[训练集]
    D --> E2[验证集]
    D --> E3[测试集]
```

### 2. 模型训练流程

```mermaid
graph TD
    A[数据加载] --> B[模型初始化]
    B --> C[训练循环]
    C --> D[前向传播]
    D --> E[损失计算]
    E --> F[反向传播]
    F --> G[参数更新]
    G --> H{达到终止条件?}
    H -- 否 --> C
    H -- 是 --> I[保存模型]
```

### 3. 模型评估流程

```mermaid
graph TD
    A[加载测试数据] --> B[加载预训练模型]
    B --> C[模型预测]
    C --> D1[混淆矩阵分析]
    C --> D2[PCA特征可视化]
    D1 --> E1[生成混淆矩阵图]
    D2 --> E2[计算DBI指标]
    E1 --> F[评估模型性能]
    E2 --> F
```

## 使用说明

### 1. 环境配置

- Python 3.6+
- PyTorch 1.7+
- NumPy, Matplotlib, Seaborn
- h5py, scikit-learn

### 2. 路径配置

编辑`pathsetting.json`文件，设置数据路径：

```json
{
    "ModeType_dir": "E:\\project\\prj_SigAMR\\Model_V0",
    "path_train_file": "E:\\lz_signaldB\\datafiles\\10-ModDataSet\\2025-07-16\\Train.h5",
    "path_val_file": "E:\\lz_signaldB\\datafiles\\10-ModDataSet\\2025-07-16\\Valid.h5",
    "path_test_file": "E:\\lz_signaldB\\datafiles\\10-ModDataSet\\2025-07-16\\Test.h5"
}
```

### 3. 模型训练

```bash
# 单卡训练
python train.py

# 多卡训练 (Linux)
CUDA_VISIBLE_DEVICES=0,1 python -m torch.distributed.launch --nproc_per_node=2 train.py
```

### 4. 模型预测

```bash
# 使用默认模型和测试集
python predictSigModType.py

# 指定模型和数据集
python predictSigModType.py -m ./logs/ModelAMR-ep100.pth -p ./data/test.h5
```

### 5. 模型分析

```bash
# 使用默认模型和测试集
python RunAnalyzeModel.py

# 指定模型和数据集
python RunAnalyzeModel.py -m ./logs/ModelAMR-ep100.pth -p ./data/test.h5
```

## 性能指标

- **分类准确率**：在测试集上达到95%+的调制方式识别准确率
- **特征聚类**：通过PCA分析显示良好的类别分离性
- **DBI指标**：Davies-Bouldin指数低于0.5，表明良好的聚类效果

## 注意事项

1. 确保数据集格式正确，包含`signal_data`、`labels`和`SNRs`字段
2. 训练前检查`ModType_def.txt`文件，确保调制类型定义正确
3. 大数据集训练时建议使用多卡分布式训练提高效率
4. 模型分析结果保存在`./confusion_analysis_results/`目录下

## 未来工作

- 支持更多调制方式的识别
- 增强对低信噪比条件下的识别能力
- 添加更多特征可视化和分析工具
- 优化网络结构，减少模型参数量
- 支持模型量化和部署到边缘设备

---

*作者: Liuzhiguo*  
*更新日期: 2025-07-17*