function [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(filename)
%    function: LoadWBSig
%    Params： file       ,    -- 信号文件路径
%    return:
%             wb_signal, -- I/Q信号
%             wb_fc,    -- 宽带信号中心频率
%             wb_bw,    -- 宽带信号带宽
%             wb_fs,    -- 宽带信号采样率

if exist(filename,"file")<=0
    error('文件不存在:%s\n',filename);
end
packet = load_sigcap(filename);                   % 加载信号（注意：此时加载的信号为宽带信号）
wb_signal = packet.data(:, 1);%原始信号
%参数信息
wb_fc = packet.header.center_freq;    % 信号中心频率
wb_bw = packet.header.bandwidth;       % 信号带宽
wb_fs = packet.header.samp_rate;     % 信号采样率
end