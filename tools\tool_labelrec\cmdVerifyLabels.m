%  Function    ：cmdVerifyLabels
%  Description : 验证标注文件
%
%  Author      : Liuzhiguo
%  Date        : 2024-10-22

% 1. 初始化命令
clc
clear
close all

IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数

%验证所标注文件
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\Train_ds.txt',85);
%showWavById('E:\ftproot\signalDB\','E:\ftproot\signalDB\Train_ds.txt',61);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_433M\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_433M\Train_records_1ms.txt',14);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Anxiangdongli\Train_records.txt',12);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_rc_Microzone_6cmini\Train_records.txt',7);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Glider\Train_records.txt',1);

%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Jouav\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Keweitai_x6lm\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_rc_Graupner\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_rc_Microzone_6cmini\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_rc_Upair\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_yaboot\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_FF-433\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Skydroid\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_FLYSKY_FS16X_2439\Train_records.txt',20);

%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\Train_records.txt',34);
%showWavById('E:\ftproot\signalDB\','E:\ftproot\signalDB\Train_records.txt',32);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_flyskyfsst8\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_frskyx9d\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_crossfire_gfsk\Train_records.txt',12);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_crossfire_gfsk\Train_records_added.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_FUTABA_T14SG_2430\Train_records.txt',1);
verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_FUTABA_T14SG_2430\Train_records_added.txt',1);

%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_microhard_840\Train_records.txt',9);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Frsky_x9dse\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_RFD900X\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_crossfire_lora\Train_records.txt',14);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_crossfire_lora\Train_records_added.txt',1);

%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_DEVO10_2427\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Frsky_x9dse_black\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_HITEC_FLAH8_2430\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_RadioLink_AT9S_2443\Train_records.txt',4);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Spectrum_DX6e\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Radiolink_T8F8\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_TFMODEL_TF8G\Train_records.txt',16);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_TFMODEL_TF8G\Train_records.txt',16);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_WFLY_ET07\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_WFT_09Sll\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_heqi_swan-K1\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_xiaomi\Train_records.txt',18);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_HOTRC_HT-8A\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_syma_x56w\Train_records.txt',1);%数据长度应大于0.5ms
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Hikvision_6150A\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Feima_D2000\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_xingqi\Train_records.txt',12);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_XK_Detect_X380\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_blackHnornet\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_Ehang\Train_records.txt',19);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_siyiD23K\Train_records.txt',1);
%verifylabelsById('E:\ftproot\signalDB\','E:\ftproot\signalDB\nb_crossfire_gfsk\Train_records.txt',1);
%verifylabelsById('E:\software\nxtool\packages\samDb\','E:\software\nxtool\packages\samDb\nb_crossfire_lora\Train_records.txt',1);
%verifylabelsById('E:\software\nxtool\packages\samDb\','E:\software\nxtool\packages\samDb\nb_crossfire_gfsk\Train_records.txt',1);
%verifylabelsById('E:\software\nxtool\packages\samDb\','E:\software\nxtool\packages\samDb\nb_frskyx9d2\Train_records.txt',1);
%verifylabelsById('E:\software\nxtool\packages\samDb\','E:\software\nxtool\packages\samDb\nb_HITEC_FLAH8_2430\Train_records.txt',1);
%verifylabelsById('E:\software\nxtool\packages\samDb\','E:\software\nxtool\packages\samDb\nb_RFD900X\Train_records.txt',1);
%verifylabelsById('E:\software\nxtool\packages\samDb\','E:\software\nxtool\packages\samDb\nb_FUTABA_T14SG_2430\Train_records.txt',1);
