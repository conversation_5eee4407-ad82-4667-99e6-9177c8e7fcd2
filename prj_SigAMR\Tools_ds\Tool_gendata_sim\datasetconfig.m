% function myconfig = datasetconfig()

% 信号仿真参数设置
myconfig.num_samples = 200;                     % 每种调制类型每种SNR的样本数
myconfig.snr_levels = 0:2:20;                   % 信噪比范围(dB)
myconfig.bw = [120e3 384e3 1200e3];             % 带宽
myconfig.fs = 15.36e6;                          % 采样率
myconfig.Rb = floor(myconfig.bw / 1.2);                      % 码元速率
myconfig.sps = floor(myconfig.fs ./ myconfig.Rb);      % BPSK、QPSK、GFSK和16QAM 每符号采样数
myconfig.spf = 100;                                    % 初始信号长度
myconfig.fc = 915e6;                            % 单载波频率
myconfig.sf = 6;                                % 扩频因子
myconfig.channelization = true;                % 是否进行信道化
myconfig.wb_bw = 12e6;                          % 宽带带宽
myconfig.nb_bw = 2e6;                           % 窄带带宽
myconfig.nb_fc = 915e6;                         % 窄带载波频率
myconfig.nb_fs = 3.84e6;                        % 窄带采样频率
downsample_rate = myconfig.fs/myconfig.nb_fs;   % 下抽样率
myconfig.crop_length_out = 192; % 输出信号长度
myconfig.crop_length_in  = floor(myconfig.crop_length_out*downsample_rate);  % 随机截取的原始信号长度

%调制类型定义
myconfig.modulationTypes = categorical(["BPSK", "QPSK",  "16QAM", "GFSK", 'LoRa']);%'LoRa',
myconfig.mod_type_cell = { 'BPSK', 'QPSK', '16QAM', 'GFSK', 'LoRa'}; % 5种调制类型, 'LoRa'

% 训练集验证集测试集比例
myconfig.train_ratio = 0.8;
myconfig.val_ratio = 0.1;
myconfig.test_ratio = 0.1;

%数据集输出路径
myconfig.output_file = '.\ds_gen'; % 存储路径设置

% end