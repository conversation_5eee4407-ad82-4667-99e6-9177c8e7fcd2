function [out_bin] = demodfsk_signal(xdata, nSamp)
%  Function    ：demodfsk_signal
%  Description : 利用反正切方式依次计算频率变化率
%                参考：https://blog.csdn.net/qq_41332806/article/details/111311196
%  Parameter   : xdata    -- 输入数据    (I/Q)
%                out_bin  -- 输出数据    (Bin)
%
%  Author      : Liuzhiguo
%  Date        : 2024-06-21

nLen = length(xdata);
out_data = zeros(nLen-1, 1);
for i=2:length(xdata)
    out_data(i-1)=real(xdata(i-1))*imag(xdata(i))-real(xdata(i))*imag(xdata(i-1));
    norm_value = abs(xdata(i));
    out_data(i-1) = out_data(i-1)/norm_value;
end

% figure(123);
% plot(out_data);


%判断采样序列
pos = 1;
checksync = zeros(nSamp, 1);
mypreamble = zeros([32, 1]);
mypreamble(1:2:end)=1;

for pos =1:nSamp %判断preamble序列
    binSync = out_data(pos:nSamp:pos+nSamp*32-1)>0;
    binSync = 1 - binSync;
    if isequal(mypreamble, binSync)==true
        checksync(pos) = 1;
    end
end

startpos=0;
endpos =nSamp;
nRange = [];
for pos =2:nSamp %寻找最佳采样点
    if startpos==0
        if (checksync(pos)==1) && (checksync(pos-1)==0)
            startpos = pos;
        end
    elseif pos == nSamp
        if (checksync(pos)==1) %已经开始，直到末尾都是1
            endpos = pos;
            nRange = [nRange;[startpos endpos endpos-startpos+1]];
        end
    else
        if (checksync(pos)==0) && (checksync(pos-1)==1)
            endpos = pos-1;
            nRange = [nRange;[startpos endpos endpos-startpos+1]];
            startpos = 0;%设置重新查找
        end

    end
end

[v,i] = max(nRange(:,3));
startpos=nRange(i,1);
endpos=nRange(i,2);
bestpos = startpos + floor((endpos-startpos+1)/2);

out_bin=out_data(bestpos:nSamp:end)>0;
out_bin=1-out_bin;%反向

end

