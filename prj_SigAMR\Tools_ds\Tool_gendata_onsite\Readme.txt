		数据生成工具说明
1.cmdAutolabelsByMeta.m-----------------------------------根据meta文件生成标注文件，输入：包含meta文件的类别目录 输出：标注后的txt文件，Train_records.txt 在自动标注的过程中会过滤无效的信号，如起始位置错误，相关中心频率无信号、低信噪比等情况。


2.DivTrainTestVal.m----------------------------------根据mdAutolabelsByMeta.m输出的txt文件按照比例划分为训练、测试、验证数据集，并输出相应单独的txt文件。


3.generateMultiClassHDF5_fixed_labels-----------------------根据生成的按比例划分好的train.txt、test.txt、val.txt生成相应的hdf5文件格式的训练数据集。输入：数据集的根目录，生成h5文件的输出路径 输出：hdf5文件格式的训练数据集

