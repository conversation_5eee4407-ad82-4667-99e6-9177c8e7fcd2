%求最终的起始位置
function [start_position] = findTheStartPos(wave_data, clip_start_pos, clip_end_pos)
%% 参数设置
threshold_ratio = 0.43;    % 阈值比例 (RMS的10%)


%% 计算幅度阈值
wav_clip = wave_data(clip_start_pos:clip_end_pos);
rms_value = sqrt(mean(abs(wav_clip).^2));
threshold = rms_value * threshold_ratio;

%% 滑动窗口检测
window_size = 100; %round(window_length * fs);  % 窗口采样点数
clip_start_pos_new = clip_start_pos + floor((clip_end_pos-clip_start_pos)/10);
num_windows = floor(length(wave_data(1:clip_start_pos_new)) / window_size)-2;%留有余量
sigwins_count = 0;
start_position = -1;

for i = 1:num_windows
    start_idx = clip_start_pos_new-i * window_size+1;%向前滑动
    end_idx = clip_start_pos_new-(i-1) * window_size;
    window = wave_data(start_idx:end_idx);
    
    % 计算窗口RMS
    window_rms = sqrt(mean(abs(window).^2));
    
    % 判断是否为信号窗口
    if window_rms > threshold
        sigwins_count = sigwins_count + 1;
        
    else
        start_position = start_idx;%已发现上升沿
        break;
    end
end

%% 输出结果
if start_position <0 %找到头位置也未发现上升沿
    start_position = 0;
    %start_position = clip_start_pos;
end

end