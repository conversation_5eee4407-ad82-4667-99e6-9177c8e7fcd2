# =======================================================================================================================
#   Function    ：predictSigSimility.py
#   Description : 模型预测推理：信号向量相似度
#                 传入hdf5文件路径，并对modelTrain.py调用
#                 调用方式: 
#   Parameter   : ../data/predict/*.hdf5
#   Author      : Liuzhiguo
#   Date        : 2024-08-27
# =======================================================================================================================
import os
import numpy as np
import h5py
import time
from usrlib.VectorToVectorSim import *
from usrlib.usrlib import *
from usrlib.dataloader import *
import sys
print(sys.argv)
import getopt
from nets.arcface import Arcface

if __name__ == "__main__":
    modeltype = 1 #模型类别 0:分类 1:角向量
    clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件
    dataset_file = ""

    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:',cls_count)

    # 命令行方式获取测试文件 dataset_file
    opts,args = getopt.getopt(sys.argv[1:],'-m:-p:',['modelname=','datafilepath='])
    for opt_name,opt_value in opts:
        if opt_name in ('-m','--modelname'):
            fname_model = opt_value
            print("The modelname is ", fname_model)
        elif opt_name in ('-p','--datafilepath'):
            dataset_file = opt_value
            print("The datafilepath is ", dataset_file)
            # do something


    #1. 路径及模型参数
    dataset_name ='nb-dataset-val-S1.hdf5'
    #model_path = "logs/Mtype1-ep095-loss0.000-val_loss2.050.pth" #192
    #model_path = "logs/Mtype1-ep081-loss0.007-val_loss1.631.pth" #96 acc= 47.50%
    #model_path = "logs/Mtype1-ep046-loss0.061-val_loss1.434.pth" #96 acc= 47.50%
    #model_path = "logs/Mtype1-ep098-loss0.000-val_loss2.219.pth"  #96 acc= 34.17%
    #model_path = "logs/Mtype1-ep095-loss0.000-val_loss1.817.pth"  #64 acc= 55%
    #model_path = "logs/Mtype1-ep063-loss0.016-val_loss1.441.pth"  #64 acc= 44.17%
    model_path = "logs/Mtype1-ep100-loss0.000-val_loss1.867.pth"  #64 acc= 54.17%

    if dataset_file=="":
        predict_dataset_dir = dataset_dir[0]
        dataset_file = os.path.join(predict_dataset_dir, dataset_name)


    batch_size = 32 #256  

    #2. 读取数据文件
    t0 = time.time()
    print('=====================load predict datafile: {0} ==============='.format(dataset_file))
    rx_signal,classid_gt,class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file)

    if batch_size > classid_gt.size: #如果数据量不足，重新确定适合的batchsize
            batch_size = classid_gt.size  

    t1 = time.time()
    print('load data：{:.2f}s'.format(t1 - t0))

    #3.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()
    t2 = time.time()
    print("load model：{:.2f}s".format(t2 - t1))

    test_dataset = DatasetFolder_eval(rx_signal, fs_value, bw_value)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False) #, num_workers=8, pin_memory=True)

    output_all = []
    with torch.no_grad():
        for idx, (data, afs_value, abw_value) in enumerate(test_loader):
            y = data
            y, afs_value, abw_value = y.cuda(), afs_value.cuda(), abw_value.cuda()
            modelOutput = Model(y)
            output_all.append(modelOutput.softmax(1))
    output_all = torch.cat(output_all, dim=0)

    t3 = time.time()
    print("model eval：{:.2f}s".format(t3 - t2))

    (vectors, clsids, clsnames, filepaths, curfs, curbw) = GetArcVectorDB()

    VectorSim = VectorSimilarity()
    if len(output_all.shape) == 1:
        avec = output_all.unsqueeze(0)
    else:
        avec = output_all

    vectors_tensor = torch.from_numpy(vectors)
    vectors_tensor = vectors_tensor.cuda()
    predicts = VectorSim.matching(avec, vectors_tensor) 
    #print(predicts)

    print("DB Labels List:\n{0}".format(clsids.reshape(1,-1)))
    #信号实际值
    print("True Labels:\n{0}".format(classid_gt.reshape(1,-1)))

    # val_label = torch.Tensor(clsids).cuda()
    # val_label=val_label.squeeze()
    for i in range(3):
        Indexs = predicts['pred_label'][:,i]
        probs = predicts['pred_score'][:,i]
        probs = probs.cpu().numpy()
        Indexs = Indexs.cpu().numpy()
        print("Predict Top {0} Labels:\n{1} \n Probs:\n{2}".format( i, clsids[Indexs].reshape(1,-1), probs)) #预测标签
        if i == 0:
            val_predict = clsids[Indexs]


    val_score = np.where(val_predict == classid_gt, 1.0, 0.0)
    val_acc = np.mean(val_score)
    print('predict_result: acc= {:.2f}%'.format(val_acc*100))

    t4 = time.time()
    print("count score：{:.2f}s".format(t4 - t3))

