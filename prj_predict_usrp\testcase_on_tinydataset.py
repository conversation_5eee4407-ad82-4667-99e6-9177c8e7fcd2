# =======================================================================================================================
#   File        : testcase_on_tinydataset.py
#   Description : 信号分类预测测试脚本
#   Features    : 1. 支持多种文件格式（.dat、.bvsp、.hdfv）的读取和处理
#                 2. 提供三种测试模式：清洗模式(BVSP)、预测模式(DAT/HDFV)
#                 3. 支持单文件测试和批量测试
#                 4. 自动生成测试报告和问题文件列表
#   Author      : Caonairui
#   Date        : 2025-06-17
# =======================================================================================================================

import numpy as np
from chanlib.func_scansig import proc_wbsig
from usrlib.usrlib import Read_sigfile
from usrlib.predict_proc import predict_classify_proc_all, load_classify_model, predict_match_proc_all, load_match_model
from usrlib.MyHelper import read_signal_from_hdf5
import os
import csv
import torch
from datetime import datetime

# ===================== 运行类型枚举 =====================
class RunType:
    TEST_MODE = 0  # 测试模式（对应DAT文件和BVSP文件）
    CLEAN_MODE = 1 # 清洗模式（对应BVSP文件）
    HDFV_MODE = 2  # HDFV模式（对应HDFV文件）

# ===================== 全局配置参数 =====================
# 数据集配置
use_run_type = RunType.HDFV_MODE  # 默认使用测试模式
test_single_ds = False         # 是否测试单个数据集
dataset_name = 'fchanscan-S1.hdf5' 
dataset_path = R"C:\Users\<USER>\Documents\Project\datafiles\02-LabeledData\testdata-2025-07-17"
dataset_path_single = R"C:\Users\<USER>\Documents\Project\datafiles\02-LabeledData\tinydata-2025-07-02\nb_crossfire_gfsk\CJ0_1M_2046.bvsp"

# 输出目录配置
output_dir = "output"  # 主输出目录
clean_results_dir = os.path.join(output_dir, "dataset_clean_results")  # 清洗模式输出目录
predict_results_dir = os.path.join(output_dir, "predict_results")  # 预测模式输出目录

# 创建输出目录
for dir_path in [output_dir, clean_results_dir, predict_results_dir]:
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)

# 模型配置
modeltype = 1  # 模型类型：0-分类模型，1-匹配模型
if modeltype == 0:
    model_path = R"C:\Users\<USER>\Documents\Project\datafiles\02-LabeledData\tinydata-2025-07-02\Mtype0-ep100-loss0.007-val_loss0.007.pth"  # 分类模型路径
    
    Model, cls_count, cls_ids, cls_names = load_classify_model(model_path)  # 加载分类模型
else:
    model_path = R"C:\Users\<USER>\Documents\Project\datafiles\02-LabeledData\tinydata-2025-07-02\Mtype1-ep100-loss0.000-val_loss0.000.pth"  # 匹配模型路径
    Model, cls_count, cls_ids, cls_names = load_match_model(model_path)  # 加载匹配模型

# 预测阈值配置
all_predicts = np.zeros(cls_count, dtype=int)                # 预测发现无人机次数统计
threshold_classify = np.ones(cls_count, dtype=float)*0.7     # 分类模型每类阈值
threshold_match = np.ones(cls_count, dtype=float)*0.8        # 匹配模型每类阈值

# USRP参数配置
rate = 15.36e6
freq = 915e6
bandwidth = 12e6

def Test_savedhdfs(fname_hdfraw = R"default.hdfv", csv_filename = "predict_results.csv", check_filename = None, list_filename = None):
    """
    测试保存的HDF5文件并进行预测
    
    Args:
        fname_hdfraw (str): 输入的原始数据文件路径，支持.dat、.bvsp或.hdfv格式
        csv_filename (str): 预测结果保存的CSV文件名
        check_filename (str): 清洗模式日志文件名
        list_filename (str): 问题文件列表文件名
    
    Returns:
        None
    """
    # 确保csv_filename在正确的输出目录下
    if use_run_type == RunType.CLEAN_MODE:
        if not csv_filename.startswith(clean_results_dir):
            csv_filename = os.path.join(clean_results_dir, os.path.basename(csv_filename))
    else:
        if not csv_filename.startswith(predict_results_dir):
            csv_filename = os.path.join(predict_results_dir, os.path.basename(csv_filename))
    
    # 1. 信号文件读取和处理
    chan_id = 0
    print("解析测试文件:{0}\n".format(fname_hdfraw))
    name, ext = os.path.splitext(fname_hdfraw)
    
    # 根据文件类型选择不同的读取方式
    if ext == ".dat" or ext == ".bvsp": 
        # 读取.dat或.bvsp格式文件
        (rxDatas, rate, freq, bandwidth) = Read_sigfile(fname_hdfraw, [0, -1])
        samples = rxDatas[chan_id,:,0]+1j*rxDatas[chan_id,:,1]  # 转换为复数形式
    elif ext == ".hdfv":
        # 读取.hdfv格式文件
        samples, metadata = read_signal_from_hdf5(fname_hdfraw)
        rate = metadata['fs']
        freq  = metadata['fc']
        bandwidth = metadata['bw']

    # 2. 信号处理
    proc_wbsig(samples, rate, freq, bandwidth)

    # 3. 保存预测结果
    dataset_name = R'fchanscan-S1.hdf5'
    SavePredict2CSV(dataset_name, csv_filename, fname_hdfraw)
    
    # 4. 如果是BVSP文件且处于清洗模式，检查预测结果
    if use_run_type == RunType.CLEAN_MODE and ext == ".bvsp":
        # 获取真实类别名
        true_classname = os.path.basename(os.path.dirname(fname_hdfraw))
        
        # 确保CSV文件存在
        if not os.path.exists(csv_filename):
            print(f"警告: CSV文件 {csv_filename} 不存在，跳过预测结果检查")
            return
            
        # 读取最新的预测结果
        with open(csv_filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            last_row = None
            for row in reader:
                last_row = row
            
            if last_row and last_row['True_ClassName'] != last_row['Predicted_ClassName']:
                # 如果文件不存在，创建文件
                if check_filename and not os.path.exists(check_filename):
                    with open(check_filename, 'w', encoding='utf-8') as f:
                        f.write(f"数据集清洗报告 - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("="*80 + "\n\n")
                
                if list_filename and not os.path.exists(list_filename):
                    open(list_filename, 'w', encoding='utf-8').close()
                
                # 写入详细日志
                if check_filename:
                    with open(check_filename, 'a', encoding='utf-8') as f:
                        f.write(f"文件: {fname_hdfraw}\n")
                        f.write(f"真实类别: {last_row['True_ClassName']}\n")
                        f.write(f"预测类别: {last_row['Predicted_ClassName']}\n")
                        f.write(f"预测概率: {last_row['Predicted_Props']}\n")
                        f.write("-"*50 + "\n")
                
                # 写入问题文件列表（只写入文件路径）
                if list_filename:
                    with open(list_filename, 'a', encoding='utf-8') as f:
                        f.write(f"{fname_hdfraw}\n")
                
                print(f"预测错误已记录到: {check_filename}")
                print(f"问题文件已添加到列表: {list_filename}")

def list_hdfv_files(basepath):
    """
    列举指定目录下所有.hdfv扩展名的文件
    
    Args:
        basepath (str): 要搜索的根目录路径
    
    Returns:
        list: 包含.hdfv文件路径的列表
    """
    hdfv_files = []
    
    # 递归遍历目录
    for root, dirs, files in os.walk(basepath):
        for file in files:
            if file.lower().endswith('.hdfv'):
                hdfv_files.append(os.path.join(root, file))
    
    return hdfv_files

def list_bvsp_files(basepath, clsnames):
    """
    列举指定目录下所有.bvsp扩展名的文件
    
    Args:
        basepath (str): 要搜索的根目录路径
        clsnames (list): 类别名称列表
    
    Returns:
        list: 包含.bvsp文件路径的列表
    """
    bvsp_files = []
    
    # 递归遍历目录
    for root, dirs, files in os.walk(basepath):
        # 排除后缀为_out的文件夹
        dirs[:] = [d for d in dirs if not d.endswith('_out')]
        
        # 检查当前目录是否在类别列表中
        current_dir = os.path.basename(root)
        if current_dir not in clsnames:
            continue
            
        # 扫描目录中的文件
        for file in files:
            if file.lower().endswith('.bvsp'):
                bvsp_files.append(os.path.join(root, file))
    
    return bvsp_files

def list_dat_files(basepath, clsnames):
    """
    列举指定目录下所有.dat扩展名的文件
    
    Args:
        basepath (str): 要搜索的根目录路径
        clsnames (list): 类别名称列表
    
    Returns:
        list: 包含.dat文件路径的列表
    """
    dat_files = []
    
    # 递归遍历目录
    for root, dirs, files in os.walk(basepath):
        # 排除后缀为_out的文件夹
        dirs[:] = [d for d in dirs if not d.endswith('_out')]
        
        # 检查当前目录是否在类别列表中
        current_dir = os.path.basename(root)
        if current_dir not in clsnames:
            continue
            
        # 扫描目录中的文件
        for file in files:
            if file.lower().endswith('.dat'):
                dat_files.append(os.path.join(root, file))
    
    return dat_files

def Testpreceison_byfolders(sFolderName = dataset_path, file_type = RunType.TEST_MODE):
    """
    对指定文件夹中的所有数据文件进行批量测试
    
    Args:
        sFolderName (str): 包含测试数据文件的根目录路径，默认为dataset_path
        file_type (int): 文件类型枚举值，可选值：
            - RunType.TEST_MODE: 处理.dat文件
            - RunType.CLEAN_MODE: 处理.bvsp文件（清洗模式）
            - RunType.HDFV_MODE: 处理.hdfv文件
    
    Returns:
        None
    """
    # 1. 获取所有数据文件列表
    data_files = []
    if file_type == RunType.HDFV_MODE:
        data_files = list_hdfv_files(sFolderName)
    elif file_type == RunType.TEST_MODE:
        # TEST_MODE同时处理DAT和BVSP文件
        dat_files = list_dat_files(sFolderName, cls_names)
        bvsp_files = list_bvsp_files(sFolderName, cls_names)
        data_files = dat_files + bvsp_files
    elif file_type == RunType.CLEAN_MODE:
        data_files = list_bvsp_files(sFolderName, cls_names)
    
    # 2. 生成带时间戳的文件名
    current_time = datetime.now().strftime("%m%d_%H%M")
    if file_type == RunType.CLEAN_MODE:
        csv_filename = os.path.join(clean_results_dir, f"dataset_clean_results_{current_time}.csv")
        check_filename = os.path.join(clean_results_dir, f"dataset_clean_results_{current_time}.log")
        list_filename = os.path.join(clean_results_dir, f"dataset_clean_results_list_{current_time}.txt")
    else:
        csv_filename = os.path.join(predict_results_dir, f"predict_results_{current_time}.csv")
        check_filename = None
        list_filename = None
    
    # 3. 如果是BVSP模式（清洗模式），创建待检查文件
    if file_type == RunType.CLEAN_MODE:
        with open(check_filename, 'w', encoding='utf-8') as f:
            f.write(f"数据集清洗报告 - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("="*80 + "\n\n")
        
        # 创建问题文件列表文件（空文件）
        open(list_filename, 'w', encoding='utf-8').close()
    
    # 4. 逐个处理数据文件
    for i in range(len(data_files)):
        fname_sample = data_files[i]
        print("解析采样文件:{0}\n".format(fname_sample))
        
        # 获取真实类别名
        true_classname = os.path.basename(os.path.dirname(fname_sample))
        
        # 进行预测
        Test_savedhdfs(fname_sample, csv_filename, check_filename, list_filename)

def Test_mainproc():
    """
    主测试处理函数
    
    Returns:
        None
    
    Note:
        该函数会：
        1. 调用Testpreceison_byfolders进行批量测试
    """
    if test_single_ds:
        # 生成带时间戳的CSV文件名
        current_time = datetime.now().strftime("%m%d_%H%M")
        if use_run_type == RunType.CLEAN_MODE:
            csv_filename = os.path.join(clean_results_dir, f"dataset_clean_results_{current_time}.csv")
        else:
            csv_filename = os.path.join(predict_results_dir, f"predict_results_{current_time}.csv")
        Test_savedhdfs(fname_hdfraw = dataset_path_single, csv_filename = csv_filename)
    else:
        # 根据use_run_type选择对应的处理模式
        Testpreceison_byfolders(sFolderName = dataset_path, file_type = use_run_type)


def Anylize_predict_result(cur_predicts, full_filename, sample_data=None):
    """
    分析预测结果并生成详细报告
    
    Args:
        cur_predicts (torch.Tensor): 模型预测的概率分布
        full_filename (str): 原始数据文件的完整路径
        sample_data (dict): 包含样本数据的字典，包含fs_value, bw_value, fc_values, start_poses, end_poses
    
    Returns:
        list: 包含每个样本预测结果的字典列表
    """
    path_separator = os.sep
    # 1. 提取文件信息
    datfile_name = full_filename.split(path_separator)[-1]  # 获取文件名
    true_classname = full_filename.split(path_separator)[-2]  # 获取真实类别名
    true_label = cls_names.index(true_classname)  # 获取真实标签索引

    # 2. 获取预测结果
    val_predict = torch.argmax(cur_predicts, dim=1)  # 获取每个样本的预测类别（取概率最大的类别）
    val_label = torch.tensor(true_label)  # 将真实标签转换为张量
    
    # 3. 限制处理样本数量
    nCount = val_predict.shape[0]
    if nCount>10:
        nCount=10
        
    # 4. 分析每个样本的预测结果
    results = []
    valid_predictions = 0  # 有效预测计数器
    correct_predictions = 0  # 正确预测计数器
    
    for i in np.arange(nCount):
        id_cls = val_predict[i]
        props = cur_predicts[i, id_cls]
        
        # 4.1 检查预测概率是否有效
        if torch.isnan(props) or torch.isinf(props):
            print(f"警告：样本 {i} 的预测概率值无效 (NaN/Inf)，已跳过")
            continue
            
        # 4.2 检查是否超过阈值
        if props <= threshold_classify[id_cls]:
            print(f"警告：样本 {i} 的预测概率 {props.item():.4f} 未超过阈值 {threshold_classify[id_cls]:.4f}，已跳过")
            continue
        
        # 4.3 更新统计信息
        pred_label = val_predict[i].cpu()
        valid_predictions += 1
        if pred_label == true_label:
            correct_predictions += 1
        
        # 4.4 记录预测结果
        result = {
            'File_Name': datfile_name,
            'True_ClassName': true_classname,
            'True_Label': true_label,
            'Predicted_Label': pred_label.item(),
            'Predicted_ClassName': cls_names[val_predict[i]],
            'Predicted_Props': f"{props.item() * 100:.1f}%",
            'Prediction_Result': 'Correct' if pred_label == true_label else 'Incorrect'
        }
        
        # 4.5 添加样本数据信息（如果提供了）
        if sample_data is not None:
            if i < len(sample_data.get('fs_values', [])):
                fs_val = sample_data['fs_values'][i]
                # 如果是数组，取第一个元素；如果是标量，直接使用
                result['FS_Value'] = fs_val[0] if hasattr(fs_val, '__len__') and len(fs_val) > 0 else fs_val
            if i < len(sample_data.get('bw_values', [])):
                bw_val = sample_data['bw_values'][i]
                result['BW_Value'] = bw_val[0] if hasattr(bw_val, '__len__') and len(bw_val) > 0 else bw_val
            if i < len(sample_data.get('fc_values', [])):
                fc_val = sample_data['fc_values'][i]
                # 将fc从Hz转换为MHz并保留两位小数
                fc_hz = fc_val[0] if hasattr(fc_val, '__len__') and len(fc_val) > 0 else fc_val
                result['FC_Value'] = round(fc_hz / 1e6, 2)  # 转换为MHz并保留两位小数
            if i < len(sample_data.get('start_poses', [])):
                start_val = sample_data['start_poses'][i]
                result['Start_Pos'] = start_val[0] if hasattr(start_val, '__len__') and len(start_val) > 0 else start_val
            if i < len(sample_data.get('end_poses', [])):
                end_val = sample_data['end_poses'][i]
                result['End_Pos'] = end_val[0] if hasattr(end_val, '__len__') and len(end_val) > 0 else end_val
            if i < len(sample_data.get('snr_values', [])):
                end_val = sample_data['snr_values'][i]
                result['SNR'] = round(end_val[0],2) if hasattr(end_val, '__len__') and len(end_val) > 0 else end_val    
            if i < len(sample_data.get('duration_values', [])):
                end_val = sample_data['duration_values'][i]
                result['Duration'] = round(end_val[0],2) if hasattr(end_val, '__len__') and len(end_val) > 0 else end_val    

        results.append(result)
    
    # 5. 计算准确率
    actual_accuracy = correct_predictions / valid_predictions if valid_predictions > 0 else 0.0
    
    # 6. 添加准确率到结果中
    for result in results:
        result['Predicted_Accuracy'] = actual_accuracy
        
    return results

def SavePredict2CSV(dataset_name = R'default.hdfv', csv_filename = "predict_results.csv", full_filename = "default.dat"):
    """
    将模型预测结果保存到CSV文件
    
    Args:
        dataset_name (str): 数据集文件名，默认为'default.hdfv'
        csv_filename (str): 输出CSV文件名，默认为'predict_results.csv'
        full_filename (str): 原始数据文件名，默认为'default.dat'
    
    Returns:
        None
    
    Note:
        该函数会：
        1. 根据模型类型（分类/匹配）进行预测
        2. 分析预测结果
        3. 将结果保存到CSV文件
    """
    # 确保csv_filename在正确的输出目录下
    if use_run_type == RunType.CLEAN_MODE:
        if not csv_filename.startswith(clean_results_dir):
            csv_filename = os.path.join(clean_results_dir, os.path.basename(csv_filename))
    else:
        if not csv_filename.startswith(predict_results_dir):
            csv_filename = os.path.join(predict_results_dir, os.path.basename(csv_filename))
    
    # 1. 准备CSV文件的表头
    headers = ['File_Name', 'True_ClassName', 'True_Label', 'Predicted_Label', 
              'Predicted_ClassName', 'Predicted_Props', 'Predicted_Accuracy', 'Prediction_Result',
              'FS_Value', 'BW_Value', 'FC_Value', 'Start_Pos', 'End_Pos', 'SNR', 'Duration']
    
    # 2. 尝试从数据集文件中加载额外的样本数据
    sample_data = None
    try:
        if os.path.exists(dataset_name):
            from utils.dataloader import LoadHdfsDataset
            rx_signal, classid_gt, class_name, fs_values, bw_values, fc_values, start_poses, end_poses, snr_values, duration_values = LoadHdfsDataset(dataset_name)
            sample_data = {
                'fs_values': fs_values,
                'bw_values': bw_values,
                'fc_values': fc_values,
                'start_poses': start_poses,
                'end_poses': end_poses,
                'snr_values': snr_values,
                'duration_values': duration_values
            }
    except Exception as e:
        print(f"警告: 无法从数据集文件 {dataset_name} 加载额外数据: {e}")
        print("将使用默认值填充额外数据列")
    
    # 3. 根据模型类型进行预测
    if modeltype == 0:
        # 分类模型预测
        cur_predicts = predict_classify_proc_all(Model, cls_count, cls_names, dataset_name, threshold_classify)
        # 检查cur_predicts是否为默认值
        if isinstance(cur_predicts, np.ndarray) and np.array_equal(cur_predicts, np.zeros(cls_count, dtype=int)):
            print(f"警告: 数据集 {full_filename} 返回了默认值，可能存在以下问题:")
            print("1. 数据集为空或损坏或质量不满足要求")
            print("2. 信号处理过程中出现问题")
            print("3. 模型预测失败")
            print("建议: 检查数据集完整性或考虑删除该数据集")
            # 创建log文件记录待检查的数据集
            log_filename = os.path.join(output_dir, "datasets_to_check.log")
            with open(log_filename, 'a', encoding='utf-8') as log_file:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                log_file.write(f"[{timestamp}] {full_filename}\n")
            print(f"已将数据集 {full_filename} 添加到待检查列表: {log_filename}")
            return
    else:
        # 匹配模型预测
        cur_predicts = predict_match_proc_all(Model, cls_count, cls_names, dataset_name, threshold_match)
    
    # 4. 分析预测结果
    results = Anylize_predict_result(cur_predicts, full_filename, sample_data)
    
    # 5. 检查文件是否存在，决定是否需要写入表头
    file_exists = os.path.isfile(csv_filename)
    
    # 6. 写入CSV文件
    with open(csv_filename, 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        if not file_exists:
            writer.writeheader()  # 如果文件不存在，写入表头
        writer.writerows(results)  # 写入预测结果

    print(f"预测结果已保存到文件: {csv_filename}")

def main():
    """
    主函数入口
    
    Returns:
        None
    
    Note:
        该函数会：
        1. 调用Test_mainproc()进行模型测试
        2. 执行批量测试过程，便于定位误判问题
    """
    Test_mainproc()  # 多文件测试过程，方便定位误判的问题

if __name__ == "__main__":
    main()  