%% 读入标注文件
function verifylabelsById(signalDB_dir, lblfile, startID)
%
% function: verifylabelsById
%
% params: signalDB_dir -- 信号文件路径
%         lblfile      -- 标注文件路径
%         startID      -- 起始ID
%
%signalDB_dir = 'E:\ftproot\signalDB\';%信号路径
%lblfile = [signalDB_dir,'Train_ds.txt']; % 训练数据文件
if exist(lblfile,"file")>0
    Table_train = readtable(lblfile,'Format','%s [%d,%d] %f','Delimiter',';');
    Table_train.Properties.VariableNames = ["filename","startpos","endpos","fc"];
    Table_train.lenPoints = Table_train(:,3).endpos-Table_train(:,2).startpos+1;
    %Table_train = sortrows(Table_train,'lenPoints');
else
    error("文件：%s 不存在！", lblfile);
end

%% 逐条验证
for iRow = startID : height(Table_train)
    sigfsubname          = char(Table_train(iRow,1).filename);%文件名称
    %clsname          = split(sigfsubname,'\',1);       %文件类别名名称

    sigfname         = [signalDB_dir, sigfsubname];    %文件路径
    nStartpos_t      = Table_train(iRow,2).startpos;      %起始点
    nEndpos_t        = Table_train(iRow,3).endpos;      %结束点
    fc               = Table_train(iRow,4).fc;      %中心频率
    lenPoints        = Table_train(iRow,5).lenPoints;%长度

    
    Stitle = sprintf('记录号:%d,文件:%s,数据点:[%d,%d],中心频率=%.3f MHz\n',iRow, sigfsubname,nStartpos_t,nEndpos_t,fc/1e6);
    disp(Stitle);
    if fc < 100e6
        error("[错误]中心频率应该>100MHz");
    elseif fc > 10e9
        error("[错误]中心频率应该<10GHz");
    end
    if lenPoints < 61.44e6*0.0005
        error("数据长度应大于0.5ms");
    end

    [wb_signal, wb_fc, wb_bw, wb_fs] = LoadWBSig(sigfname);
    showWBSig(wb_signal(nStartpos_t:nEndpos_t),wb_fc,wb_fs,Stitle);
    %pause();
    x = input('继续下一个，按回车，退出，按2\n');
    if x==2
        break;
    end
end
end