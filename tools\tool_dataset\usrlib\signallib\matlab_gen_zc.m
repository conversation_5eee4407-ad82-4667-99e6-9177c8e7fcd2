
function zcseq_td = matlab_gen_zc(u,N,fftlen)
    seq = zeros(1,N);
    for n = 0:N-1
       seq(n+1) = exp(-1j*pi*u*n*(n+1)/N);
    end
    if fftlen==1024
        zcseq_fft = fftshift([zeros(1,212) seq  zeros(1,211)]);
    elseif fftlen==2048
        zcseq_fft = fftshift([zeros(1,424) seq  zeros(1,423)]);
    else
        zcseq_fft = fftshift([zeros(1,round((fftlen-N)/2)) seq  zeros(1,round((fftlen-N)/2)-1)]);
     end
    zcseq_td = ifft(zcseq_fft,fftlen);
    
%     zcseq_td = resample(zcseq_td,3,1);
%     subplot(211)
%     plot(abs(xcorr(zcseq_td)./length(zcseq_td)))
%     subplot(212)
%     plot(real(zcseq_td))
end