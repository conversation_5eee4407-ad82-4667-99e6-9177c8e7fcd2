# =======================================================================================================================
#   Function    ：TestOnnxModel.py
#   Description : 测试TensorRT模型脚本
#                   利用onnxruntime加载onnx模型，并测试
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-2-19
# =======================================================================================================================
import numpy as np
from usrlib.usrlib import compute_stft
import torch
import time
from usrlib.dataloader import *
from usrlib.usrlib import  read_dataset_lines
from usrlib.usrlib import *
from utils.dataloader import SigNbDataset, LSWDataset, SigNbDataset_collate
from torch.utils.data import DataLoader
from utils.utils import (seed_everything, show_config, worker_init_fn)
import torch.nn as nn
import torch.nn.functional as F
import onnxruntime as ort
import onnx

PRECISION = np.float32

clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件
clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
cls_ids, cls_names, cls_count = get_classes(clsdef_file)
print('无人机类别个数:', cls_count)
annotation_path_train = dataset_dir[4]
annotation_path_val = dataset_dir[5]
LenNorm         = 512*46# 应该<=24000 ;6ms for 4M sps


model_name = "Mtype0-ep095-loss0.084-val_loss0.145" 
onnx_fname = 'model_data/{0}.onnx'.format(model_name)
onnx_fname_modified = 'model_data/{0}_modified.onnx'.format(model_name) #tensor rt filename

# 如果你的模型实际上支持批量输入，只是在定义输入时固定了批量大小为 1，你可以使用 ONNX 工具来修改模型的输入维度。
# 加载 ONNX 模型
model = onnx.load(onnx_fname)
# 获取输入张量
input_tensor = model.graph.input[0]
# 修改第 0 维的维度为动态（使用 None 表示）
input_tensor.type.tensor_type.shape.dim[0].dim_param = 'batch_size'
# 保存修改后的模型
onnx.save(model, onnx_fname_modified)

#
# https://zhuanlan.zhihu.com/p/351426774
#
#  测试可成功运行
#
ort_session = ort.InferenceSession(onnx_fname_modified)
#2. 读取数据文件

# print('=====================load predict datafile: {0} ==============='.format(dataset_file))
# rx_signal,classid_gt,class_name, fs_value, bw_value = LoadHdfsDataset(dataset_file)

input_shape     = (32, LenNorm, 2) 
#dummy_input     = np.zeros(input_shape, dtype = PRECISION)
dummy_input     = torch.randn(input_shape)
dummy_input     = compute_stft(dummy_input)
dummy_input     = dummy_input.numpy()
#
# 必须为numpy变量，所以转换为numpy
#
t0 = time.time()
#predictions = trt_model.predict(dummy_input)
outputs = ort_session.run(None, {ort_session.get_inputs()[0].name: dummy_input})
t1 = time.time()
print('Predict time：{:.2f}s'.format(t1 - t0))

#
#   实际数据测试
#
lines_val   = read_dataset_lines(annotation_path_val, ['base'])
val_dataset = SigNbDataset(lines_val,LenNorm=LenNorm)
seed        = 11
ds_val      = DataLoader(val_dataset, shuffle=True, batch_size=32, num_workers=0, pin_memory=True,
                    drop_last=True, collate_fn=SigNbDataset_collate, sampler=None, 
                    worker_init_fn=partial(worker_init_fn, rank=0, seed=seed))

for iteration, batch in enumerate(ds_val):
    signals, labels = batch
    signals = compute_stft(signals)#预先计算stft
    signals     = signals.numpy()
    #outputs     = trt_model.predict(signals)
    outputs = ort_session.run(None, {ort_session.get_inputs()[0].name: signals})
    outputs     = torch.from_numpy(outputs[0])
    loss        = nn.NLLLoss()(F.log_softmax(outputs, -1), labels)
    accuracy    = torch.mean((torch.argmax(F.softmax(outputs, dim=-1), dim=-1) == labels).type(torch.FloatTensor))
    print("accuracy:{0} Loss:{1}\n".format(accuracy.item(), loss.item()))
            
# bug issue
# https://forums.developer.nvidia.com/t/how-to-correctly-set-up-bindings-for-execute-async-v3/289924
# 