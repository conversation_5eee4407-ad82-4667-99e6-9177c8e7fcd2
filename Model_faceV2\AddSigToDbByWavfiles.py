# =======================================================================================================================
#   Function    ：AddSigToDbByWavfiles.py
#   Description : 添加wav文件信号到信号库中，config/sig-vectordB.cfg
#                 可在命令行中输入: python AddSigToDbByWavfiles.py
#                                 
#
#   Author      : Liuzhiguo
#   Date        : 2025-05-06
# =======================================================================================================================
from usrlib.usrlib import *
import torch 
from nets.arcface import Arcface
from utils.dataloader import *
import sys
#import getopt
#print(sys.argv)
import soundfile

# 读取wav文件数据，按照数据行及索引值
def readwavdata(lines, index):
    LenNorm = getLenNorm()
    annotation_path = lines[index].split(';')[1].split()[0]
    label               = int(lines[index].split(';')[0])
    
    rx_signals, fs_value = soundfile.read(annotation_path, dtype='float32')#(60000, 2)
    N_row =  rx_signals.shape[0]
    if N_row<LenNorm:
        rx_signals = np.pad(rx_signals,((0,LenNorm-N_row),(0,0)), 'constant')
    else:
        rx_signals = np.resize(rx_signals,(LenNorm, 2))

    if rx_signals.shape[0]!=LenNorm or rx_signals.shape[1]!=2 or len(rx_signals.shape)!=2: #(1, 59392, 2)
            print(fs_value)

    return rx_signals, label, annotation_path, fs_value


if __name__ == "__main__":
    #文件方式读入，加入
    #clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    modeltype = 1 #模型类别 0:分类 1:角向量
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('total classes count:',cls_count)

    lines_test   = read_dataset_lines(annotation_path_test, ['base'])
    
    if sys.platform.startswith('win'):
        print("当前系统是Windows系统")
    if windows_path!=windows_path_local: # 判断windows路径和本地windows路径是否相同
        windows_to_local_path(windows_path, windows_path_local, lines_test)

    elif sys.platform.startswith('linux'):
        print("当前系统是Linux系统") #路径转换
        windows_to_linux_path(windows_path, linux_path, lines_test)
    else:
        print("当前系统不是Windows也不是Linux系统")    
            

    N_rec = cls_count
    #每类只取第一个记录
    unique_rows = [] #第1个记录行
    for aid in  cls_ids:
        for strRow in lines_test:
            if aid==strRow.split(";")[0]:
                unique_rows.append(strRow)
                break


    #1. 路径及模型参数
    model_path = "logs/Mtype1-ep096-loss0.000-val_loss0.000-0521.pth" #64 acc= 16.67%
    model_path = "logs/Mtype1-ep087-loss0.000-val_loss0.391.pth" #05-27 匹配模型

    #2.读取模型文件
    Model = Arcface(num_classes=cls_count, backbone="DroneSigNet", mode="predict", ModelType=modeltype)
    Init_model(Model, True, model_path)

    Model.eval()

    fname = "./config/sig-vectordB.cfg"
    ViewArcVectorDB(fname) #查看向量库
    if os.path.exists(fname):
        os.remove(fname)

    N_Row = min(N_rec, len(unique_rows))
    #依次读取wav文件，并加入数据库
    for i in range(N_Row):
        sigdata, class_id, file_subpath, fs_value = readwavdata(unique_rows, i)

        clsname = file_subpath.split('\\')[-2] 
        print('要解析信号的结构：{0} {1}'.format(file_subpath, sigdata.shape))
        sigdata = torch.from_numpy(sigdata)
        sigdata = sigdata.cuda()
        bw_value = 400e3
        
        if len(sigdata.shape)==2:
            sigdata = sigdata.unsqueeze(0)
        #生成向量,加库
        vec1 = Model(sigdata)
        vec1 = vec1.squeeze()

        AddArcVector(vec1.detach().cpu().numpy(), class_id, clsname, file_subpath, fs_value, bw_value)

    ViewArcVectorDB() 