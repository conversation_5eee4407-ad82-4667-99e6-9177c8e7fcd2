%    function   : cmdMergeLabledfiles
%    Description: 合并生成标注文件工具
%    author     : 刘智国
%    主要步骤：按照class_def文件逐一目录合并生成txt文件

close all;

IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数

%1 基本数据集
%生成标注文件到output目录，请将其copy到signalDB目录
MergeDs_TV('E:\ftproot\signalDB\class_def.txt',20,'E:\ftproot\signalDB\');

%2 扩展数据集
%生成标注文件到output目录，请将其copy到samDb目录
MergeDs_TV('E:\software\nxtool\packages\samDb\class_def_test.txt',80,'E:\software\nxtool\packages\samDb\');

%3 其它测试 
%MergeDs_Single('E:\ftproot\signalDB\class_def.txt');
%[Table_train]=getTrainTable('.\Train_ds_all.txt');
%[Table_train]=getTrainTable('E:\ftproot\signalDB\nb_433M\Train_records.txt');