# =======================================================================================================================
#   Function    ：findAccuractPos.py
#   Description : 根据输入信号确定合适的有用信号起始位置
#
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-04-11
# =======================================================================================================================
import numpy as np

def findAccuractPos(x, startpos, offset):
    """
    Function    ：findAccuractPos
    Description : 根据输入信号确定合适的有用信号起始位置
    Parameter   : x        -- 输入信号
                  startpos -- 原起始位置
                  offset   -- 可能的偏移量
    Return      :
                  startpos_new   -- 新的信号起始位置
    Author      : Liuzhiguo
    Date        : 2025-02-06
    """
    # 先通过谱熵/能量确定大概范围 startpos_new+-frame_step
    startpos_p1, frame_step1 = findAccuractPosBySpectralEntropy(x, startpos, offset)
    startpos_p2, frame_step2 = findAccuractPosBySpectralEntropy(x, startpos_p1, frame_step1 * 2)  # 二次搜索
    # 能量法
    # startpos_range, frame_step = findAccuractPosBySTEnergy(x, startpos, offset)
    # 再通过阈值法确定更精确的位置
    startpos_new = findAccuractPosByThreshold(x, startpos_p2, frame_step2 * 2)
    return startpos_new


def findAccuractPosBySpectralEntropy(x, startpos, offset):
    """
    通过时域方法判断起始位置(谱熵范围法)
    """
    # 确定新的起始位置，防止越界
    if startpos - offset > 0:
        startpos_new = startpos - offset
    else:
        startpos_new = 0
    # 截取指定范围的数据
    x_clip = x[startpos_new: startpos + offset]
    # 计算帧长，为截取数据长度的1/4
    frame_length = int(len(x_clip) / 4)
    # 计算帧移，为截取数据长度的1/8
    frame_step = int(len(x_clip) / 8)
    # 计算帧数
    num_frames = int((len(x_clip) - frame_length) / frame_step) + 1
    # 初始化谱熵数组
    spectral_entropy = np.zeros(num_frames)

    # 分帧计算谱熵
    for i in range(num_frames):
        # 计算当前帧的起始和结束索引
        start_index = i * frame_step
        end_index = start_index + frame_length
        # 提取当前帧的数据
        frame = x_clip[start_index:end_index]
        # 计算当前帧的频谱
        spectrum = np.abs(np.fft.fft(frame))
        # 对频谱进行归一化处理
        spectrum = spectrum / np.sum(spectrum)
        # 计算当前帧的谱熵
        spectral_entropy[i] = -np.sum(spectrum * np.log2(spectrum + np.finfo(float).eps))

    # 求阶梯差最大值对应的索引
    index = np.argmax(np.abs(np.diff(spectral_entropy)))
    # 从第一个信号开始，差值从第2个信号开始，所以补偿1
    start_frame = index + 1

    # 计算开始位置，偏差为frame_step
    start_index = start_frame * frame_step
    # 计算最终的开始位置
    startpos_new = startpos_new + start_index
    return startpos_new, frame_step

def findAccuractPosBySTEnergy(x, startpos, offset):
    """
    通过时域方法判断起始位置(短时能量法)
    """
    if startpos - offset > 0:
        startpos_new = startpos - offset
    else:
        startpos_new = 0
    x_clip = x[startpos_new: startpos + offset]
    frame_length = int(len(x_clip) / 4)
    frame_step = int(len(x_clip) / 8)
    num_frames = int((len(x_clip) - frame_length) / frame_step) + 1
    st_energy = np.zeros(num_frames)

    # 分帧计算短时能量
    for i in range(num_frames):
        start_index = i * frame_step
        end_index = start_index + frame_length
        frame = x_clip[start_index:end_index]
        st_energy[i] = np.sum(np.abs(frame) ** 2) / frame_length

    # 求阶梯差最大值
    index = np.argmax(np.abs(np.diff(st_energy)))
    start_frame = index + 1  # 从第一个信号开始，差值从第2个信号开始，所以补偿1

    # 计算开始位置，偏差为frame_step
    start_index = start_frame * frame_step
    # 计算开始位置
    startpos_new = startpos_new + start_index
    return startpos_new, frame_step


def findAccuractPosByThreshold(x, startpos, offset):
    """
    通过时域方法判断起始位置（阈值）
    """
    if startpos - offset > 0:
        startpos_new = startpos - offset
    else:
        startpos_new = 0
    x_abs = np.abs(x[startpos_new: startpos_new + offset])
    index = np.argmax(np.abs(np.diff(x_abs)))
    index = index + 1  # 从第一个信号开始，差值从第2个信号开始，所以补偿1
    # 检查是否找到开始点和结束点
    if index.size == 0:
        index = 0
    startpos_new = startpos_new + index
    return startpos_new