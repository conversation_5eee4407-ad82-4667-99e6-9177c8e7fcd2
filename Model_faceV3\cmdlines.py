import os
#dataset_file = "E:\\project\\tool_dataset\\outdataset\\train\\base\\nb-dataset-S1.hdf5"   #已训练过的数据集
#dataset_file = "E:\\project\\tool_dataset\\outdataset\\test\\sam\\nb-test433-S1.hdf5"      #采集数据集
dataset_file = "E:\\project\\tool_dataset\\outdataset\\test\\sam\\nb-flyfrsky-S1.hdf5"      #采集数据集
#dataset_file = "E:\\project\\tool_dataset\\outdataset\\test\\gendataset\\nb-gendataset-S1.hdf5" #matlab生成数据集
os.system('python predictSigClass.py -p {0} -m {1}'.format(dataset_file, 0)) #python 训练文件调用 模型类别 0:分类 1:角向量

#信号分类
#python predictSigClass.py -p E:\project\tool_dataset\outdataset\test\sam\nb-test433-odoor-S1.hdf5
#python predictSigClass.py -p E:\project\tool_dataset\outdataset\test\sam\fchanscan-S1.hdf5
#python predictSigClass.py -p E:\project\tool_dataset\outdataset\train\base\nb-dataset-val-S1.hdf5 
#python predictSigClass.py -p E:\project\tool_dataset\outdataset\train\augment\nb-dataset-val-dbw-S1.hdf5 
#python predictSigClass.py -p E:\project\tool_dataset\outdataset\train\augment\nb-dataset-val-dfc-S1.hdf5 
#python predictSigClass.py -p E:\project\tool_dataset\outdataset\test\sam\nb-test433-1126-S1.hdf5 

#预测指纹相似度
#python predictSigSimility.py -p E:\project\tool_dataset\outdataset\train\base\nb-dataset-val-S1.hdf5  
#python predictSigSimility.py -p E:\\project\\tool_dataset\\outdataset\\test\\sam\\nb-test433-odoor-S1.hdf5  
#python predictSigSimility.py -p E:\project\tool_dataset\outdataset\test\sam\fchanscan-S1.hdf5  

#生成无人机指纹数据库
# 

#训练模型
#CUDA_VISIBLE_DEVICES=0 python train.py

#查看数据库结构
#python AddSigToDbByHdfs.py
#python summary.py

#多GPU分布训练
#python -m torch.distributed.launch --nproc_per_node 2 main.py
