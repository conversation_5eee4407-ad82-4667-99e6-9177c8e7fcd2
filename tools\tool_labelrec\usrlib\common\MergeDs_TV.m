function []=MergeDs_TV(fname_class, nRecCount, signalDB_dir)
%  Function    ：MergeDs_TV
%  Description : 将signalDB下各个子文件夹中标注文件合并成
%                并生成一个数据集，一个验证集文件
%
%  Parameter   : fname_class       -- 类别文件名称
%                nRecCount         -- 记录数     （可选）
%                signalDB_dir      -- 信号库路径 （可选）
%
%  Author      : Liuzhiguo
%  Date        : 2024-08-27

if exist("nRecCount","var")<=0
    nRecCount = 20;
end

if exist(signalDB_dir,"var")>0
    if exist(signalDB_dir,"dir")<=0
        error("%s 不存在\n", signalDB_dir);
    end
else
    [signalDB_dir,~,~]=fileparts(fname_class);
    signalDB_dir = strcat(signalDB_dir,'\');
end

if exist(fname_class,"file")>0
    Table_cls = readtable(fname_class,'Format','%d %s %d %f');
    Table_cls.Properties.VariableNames = ["clsid","clsname","bw","Tms"];
else
    error("文件：%s 不存在！", fname_class);
end

sOutfname_train = '.\output\Train_ds_gen.txt';
if exist(sOutfname_train,"file")>0
    delete(sOutfname_train);
end
sOutfname_val = '.\output\Val_ds_gen.txt';
if exist(sOutfname_val,"file")>0
    delete(sOutfname_val);
end
nTrainSet = floor(nRecCount*3/4);
nValSet = nRecCount-nTrainSet;
fprintf('Write file %s 每类训练集个数: %d\n',sOutfname_train,nTrainSet);
fprintf('Write file %s 每类验证集个数：%d\n',sOutfname_val,nValSet);
for iRow = 1 : height(Table_cls)
    foldername = char(Table_cls(iRow,2).clsname);
    fname_txt = strcat(signalDB_dir,foldername,'\Train_records.txt');% 类别文件
    if exist(fname_txt,"file")>0
        curflines = readlines(fname_txt);
        fprintf("Append file ID=%d, name: %s, nrows=%d\n",iRow, fname_txt, length(curflines));
        RowIndexs = randperm(nRecCount);%原则上不需要混淆处理,但记录会出自同一个文件
        %RowIndexs = (1:nSelCount);
        RowIDs_train = RowIndexs(1:nTrainSet);
        RowIDs_val = RowIndexs(nTrainSet+1:end);
        if iRow==1
            writelines(curflines(RowIDs_train),sOutfname_train,'WriteMode','overwrite');
            writelines(curflines(RowIDs_val),sOutfname_val,'WriteMode','overwrite');
        else
            writelines(curflines(RowIDs_train),sOutfname_train,'WriteMode','append');
            writelines(curflines(RowIDs_val),sOutfname_val,'WriteMode','append');
        end
    else
        fprintf("文件：%s 不存在！\n", fname_txt);
    end
end

end