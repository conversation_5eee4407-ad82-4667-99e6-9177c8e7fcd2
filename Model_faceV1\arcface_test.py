import matplotlib.pyplot as plt
import numpy as np
import torch
import torch.backends.cudnn as cudnn

from nets.arcface import Arcface as arcface
from utils.utils import preprocess_input, resize_image, show_config
from usrlib.usrlib import *
import soundfile


class Arcface_test(object):
    _defaults = {
        #--------------------------------------------------------------------------#
        #   使用自己训练好的模型进行预测要修改model_path，指向logs文件夹下的权值文件
        #   训练好后logs文件夹下存在多个权值文件，选择验证集损失较低的即可。
        #   验证集损失较低不代表准确度较高，仅代表该权值在验证集上泛化性能较好。
        #--------------------------------------------------------------------------#
        #"model_path"        : "model_data/mobilenet_v1_backbone_weights.pth",
        #"model_path"        : "model_data/arcface_mobilefacenet.pth",
        "model_path"        : "logs/Mtype1-ep095-loss0.000-val_loss2.050.pth",

        #-------------------------------------------#
        #   输入图片的大小。
        #-------------------------------------------#
        "input_shape"       : [512, 117],
        #-------------------------------------------#
        #   所使用到的主干特征提取网络，与训练的相同
        #   mobilefacenet
        #   mobilenetv1
        #   iresnet18
        #   iresnet34
        #   iresnet50
        #   iresnet100
        #   iresnet200
        #-------------------------------------------#
        #"backbone"          : "mobilenetv1",
        #"backbone"          : "mobilefacenet",
        #"backbone"          : "iresnet50",
        "backbone"          : "DroneSigNet",
        
        #-------------------------------------------#
        #   是否进行不失真的resize
        #-------------------------------------------#
        "letterbox_image"   : True,
        #-------------------------------------------#
        #   是否使用Cuda
        #   没有GPU可以设置成False
        #-------------------------------------------#
        "cuda"              : True,
    }

    @classmethod
    def get_defaults(cls, n):
        if n in cls._defaults:
            return cls._defaults[n]
        else:
            return "Unrecognized attribute name '" + n + "'"

    #---------------------------------------------------#
    #   初始化Arcface
    #---------------------------------------------------#
    def __init__(self, **kwargs):
        self.__dict__.update(self._defaults)
        for name, value in kwargs.items():
            setattr(self, name, value)

        self.generate()
        
        show_config(**self._defaults)

        dataset_file = "" # 数据文件路径
        modeltype =  1 #模型类别 0:分类模型 1:指纹特征模型
        clsdef_dir, dataset_dir  =  read_path_config() #读取路径配置文件      
        clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
        cls_ids, cls_names, cls_count = get_classes(clsdef_file)
        print('无人机类别个数:',cls_count)  
        #---------------------------------------------------#
        #   载入模型与权值
        #---------------------------------------------------#
        print('Loading weights into state dict...')
        device      = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.net    = arcface(num_classes=cls_count, backbone=self.backbone, mode="predict",ModelType=modeltype).eval()
        self.net.load_state_dict(torch.load(self.model_path, map_location=device), strict=False)
        print('{} model loaded.'.format(self.model_path))

        if self.cuda:
            self.net = torch.nn.DataParallel(self.net)
            cudnn.benchmark = True
            self.net = self.net.cuda()
    
    #---------------------------------------------------#
    #   检测图片
    #---------------------------------------------------#
    def detect_image(self, path_1, path_2):
        #---------------------------------------------------#
        #   图片预处理，归一化
        #---------------------------------------------------#
        with torch.no_grad():
            rx_signal1, fs_value = soundfile.read(path_1, dtype='float32')
            rx_signal2, fs_value = soundfile.read(path_2, dtype='float32')
            LenNorm = 1024*58+512
            rx_signal1 = rx_signal1[0:LenNorm,:]
            rx_signal2 = rx_signal2[0:LenNorm,:]
            
            #(59392, 2)
            photo_1 = torch.from_numpy(np.array(rx_signal1, np.float32))
            photo_2 = torch.from_numpy(np.array(rx_signal2, np.float32))
            
            if self.cuda:
                photo_1 = photo_1.cuda()
                photo_2 = photo_2.cuda()
                
            #---------------------------------------------------#
            #   图片传入网络进行预测
            #---------------------------------------------------#
            output1 = self.net(photo_1).cpu().numpy()
            output2 = self.net(photo_2).cpu().numpy()
            
            #---------------------------------------------------#
            #   计算二者之间的距离
            #---------------------------------------------------#
            l1 = np.linalg.norm(output1 - output2, axis=1)
        
        # plt.subplot(1, 2, 1)
        # plt.imshow(np.array(image_1))

        # plt.subplot(1, 2, 2)
        # plt.imshow(np.array(image_2))
        # plt.text(-12, -12, 'Distance:%.3f' % l1, ha='center', va= 'bottom',fontsize=11)
        # plt.show()
        print('Distance:{0:0.5f}'.format(l1))
        return l1

    def get_FPS(self, image, test_interval):
        #---------------------------------------------------#
        #   对图片进行不失真的resize
        #---------------------------------------------------#
        image_data  = resize_image(image, [self.input_shape[1], self.input_shape[0]], self.letterbox_image)
        #---------------------------------------------------------#
        #   归一化+添加上batch_size维度
        #---------------------------------------------------------#
        image_data  = torch.from_numpy(np.expand_dims(np.transpose(preprocess_input(np.array(image_data, np.float32)), (2, 0, 1)), 0))
        with torch.no_grad():
            #---------------------------------------------------#
            #   图片传入网络进行预测
            #---------------------------------------------------#
            preds = self.net(image_data).cpu().numpy()
            
        import time
        t1 = time.time()
        for _ in range(test_interval):
            with torch.no_grad():
                #---------------------------------------------------#
                #   图片传入网络进行预测
                #---------------------------------------------------#
                preds = self.net(image_data).cpu().numpy()
        t2 = time.time()
        tact_time = (t2 - t1) / test_interval
        return tact_time
