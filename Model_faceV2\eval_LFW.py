# =======================================================================================================================
#   Function    ：eval_LFW.py
#   Description : 估算匹配模型匹配的正确率
#                 
# 
#   Parameter   : 
#   Author      : Liuzhiguo
#   Date        : 2025-05-07
# =======================================================================================================================
import torch
import torch.backends.cudnn as cudnn
import sys

from nets.arcface import Arcface
from utils.dataloader import LSWDataset
from utils.utils_metrics import test
from usrlib.usrlib import  read_dataset_lines
from usrlib.usrlib import *

if __name__ == "__main__":
    #--------------------------------------#
    #   是否使用Cuda
    #   没有GPU可以设置成False
    #--------------------------------------#
    cuda            = True
    #--------------------------------------#
    #   主干特征提取网络的选择
    #   mobilefacenet
    #   mobilenetv1
    #   iresnet18
    #   iresnet34
    #   iresnet50
    #   iresnet100
    #   iresnet200
    #--------------------------------------#
    backbone        = "DroneSigNet"
    #--------------------------------------#
    #   训练好的权值文件
    #--------------------------------------#
    model_path      = "logs/Mtype1-ep100-loss0.000-val_loss0.000.pth"
    #--------------------------------------------------------#
    #   指向根目录下的分类文件，读取信号路径与标签
    #--------------------------------------------------------#
    clsdef_dir, annotation_path_train, annotation_path_val, annotation_path_test, windows_path, linux_path, windows_path_local  =  read_path_config() #读取路径配置文件
    clsdef_file = os.path.join(clsdef_dir, 'class_def.txt')
    cls_ids, cls_names, cls_count = get_classes(clsdef_file)
    print('无人机类别个数:', cls_count)
    #--------------------------------------#
    #   LSW评估数据集的文件路径
    #   以及对应的txt文件
    #--------------------------------------#
    lsw_pairs_path  = "model_data/lsw_pair.txt"
    #--------------------------------------#
    #   评估的批次大小和记录间隔
    #--------------------------------------#
    batch_size      = 64
    log_interval    = 1
    #--------------------------------------#
    #   ROC图的保存路径
    #--------------------------------------#
    png_save_path   = "model_data/roc_test.png"

    lines_lsw   = read_dataset_lines(lsw_pairs_path, ['base'])


    if sys.platform.startswith('win'):
        print("当前系统是Windows系统")
        if windows_path!=windows_path_local: # 判断windows路径和本地windows路径是否相同
            windows_to_local_path(windows_path, windows_path_local, lines_lsw)
    elif sys.platform.startswith('linux'):
        print("当前系统是Linux系统") #路径转换
        windows_to_linux_path(windows_path, linux_path, lines_lsw)
    else:
        print("当前系统不是Windows也不是Linux系统") 

    #---------------------------------#
    #   LSW估计
    #---------------------------------#
    LSW_loader = torch.utils.data.DataLoader(
        LSWDataset(lines_lsw=lines_lsw), batch_size=batch_size, shuffle=False)
    
    model = Arcface(num_classes=cls_count, backbone=backbone, mode="predict")

    print('Loading weights into state dict...')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.load_state_dict(torch.load(model_path, map_location=device), strict=False)
    model  = model.eval()

    if cuda:
        model = torch.nn.DataParallel(model)
        cudnn.benchmark = True
        model = model.cuda()

    test(LSW_loader, model, png_save_path, log_interval, batch_size, cuda)
