%  Function    ：cmdAutolabelsByMeta
%  Description : 自动根据meta生成标注文件
%
%  Author      : Liuzhiguo
%  Date        : 2024-11-29

% 1. 初始化命令
clc
clear
close all

IncludedPaths; % 引入路径
InitMyParams;  % 初始化路径等参数
%AutogenLblsByMeta('E:\software\nxtool\packages',"ardupilot_general","nb_433M");
%AutogenLblsByMeta('E:\ftproot\sampleData\nb_433M',"ardupilot_general","nb_433M");
%AutogenLblsByMeta('E:\ftproot\signalDB\nb_frskyx9d2\',"frsky_x9d2_dr_rc","nb_frskyx9d2", 4.2);
%AutogenLblsByMeta('E:\ftproot\signalDB\nb_Feima_D2000\',"feima_v100_d200","nb_Feima_D2000", 2,25200);
%AutogenLblsByMeta('E:\ftproot\signalDB\nb_Hikvision_6150A\',"hikvision_6150a","nb_Hikvision_6150A", 1.6);
%AutogenLblsByMeta('E:\ftproot\signalDB\nb_syma_x56w\',"syma_x56w","nb_syma_x56w", 0.41);%待验证
%AutogenLblsByMeta('E:\ftproot\signalDB\nb_xiaomi\',"xm_1080p","nb_xiaomi", 3.5);%待验证，最后一个参数为截取时间，ms
%AutogenLblsByMeta('E:\software\nxtool\packages\wfly_1km',"wfly_et12","nb_WFLY_ET07", 0.74);
%AutogenLblsByMeta('E:\software\nxtool\packages\wfly_2km',"wfly_et12","nb_WFLY_ET07", 0.74);

%AutogenLblsByMeta('E:\ftproot\signalDB\nb_crossfire_gfsk\sam',"tbs_crossfire_fsk","nb_crossfire_gfsk", 3);
%AutogenLblsByMeta('E:\software\nxtool\packages\samDb\nb_crossfire_lora',"tbs_crossfire_lora","nb_crossfire_lora", 6);
%AutogenLblsByMeta('E:\lz_signaldB\datafiles\LabeledData\nb_crossfire_gfsk',"tbs_crossfire_fsk","nb_crossfire_gfsk", 2.04);
%AutogenLblsByMeta('E:\lz_signaldB\datafiles\LabeledData\nb_frsky_x9d2',"frsky_x9d2_dr_rc","nb_frsky_x9d2", 3);
%AutogenLblsByMeta('E:\lz_signaldB\datafiles\LabeledData\nb_HITEC_FLAH8_2430',"hitec_flash8","nb_HITEC_FLAH8_2430", 1.9);
%AutogenLblsByMeta('E:\lz_signaldB\datafiles\LabeledData\nb_RFD900X',"ardupilot_general","nb_RFD900X", 3.34,-55800);
%AutogenLblsByMeta('E:\lz_signaldB\datafiles\02-LabeledData\nb_433M',"ardupilot_general","nb_433M", 1.73, 0);
% AutogenLblsByMeta('E:\lz_signaldB\datafiles\02-LabeledData\2025-05-23\nb_frsky_tw_fsk',"frsky_tw_fsk","nb_frsky_tw_fsk", 1.7, -3450);
%AutogenLblsByMeta('E:\lz_signaldB\datafiles\02-LabeledData\LabelTest-2025-05-26\nb_433M\for-debug',"ardupilot_general","nb_433M", 1.73, 0);
%AutogenLblsByMeta('E:\software\nxtool\packages\samDb\nb_FUTABA_T14SG_2430',"futaba_sfhss","nb_FUTABA_T14SG_2430", 1.45);
%AutogenLblsByMeta('E:\ftproot\signalDB\nb_flysky_ant',"flysky_ant","nb_flysky_ant",4.1);

if exist(myconfig.fpath_labeldef,"file")>0
    fprintf("[启动标签创建] 输出路径为:%s\n", myconfig.folder_labeleddata);
else
    error("文件：%s 不存在！", myconfig.fpath_labeldef);
end

Table_lbldef = readtable(myconfig.fpath_labeldef,'Format','%s %s %s %f %d','Delimiter',';');
Table_lbldef.Properties.VariableNames = ["foldername","meta_subtype","clsname","duration_ms","forwardoffset"];


% 获取CPU核心数并限制工作进程数量
maxWorkers = feature('numCores');
workerCount = max(1, floor(maxWorkers * 0.7));  % 使用70%的核心
fprintf('启动并行池，使用 %d 个工作进程（总核心数: %d）\n', workerCount, maxWorkers);

% 启动并行池
if ~isempty(gcp('nocreate'))
    delete(gcp('nocreate'));
end
parpool('local', workerCount);
nRows = height(Table_lbldef);

%优化为了parfor快速执行
foldernames = Table_lbldef(:,1).foldername;
meta_subtypes = Table_lbldef(:,2).meta_subtype;
clsnames = Table_lbldef(:,3).clsname;
duration_mss = Table_lbldef(:,4).duration_ms;
forwardoffsets = Table_lbldef(:,5).forwardoffset;
% 启动计时器
tic;
parfor iRow = 1 : nRows
    srcFolder = fullfile(myconfig.folder_labeleddata, foldernames(iRow));
    meta_subtype = strjoin(meta_subtypes(iRow));
    clsname = strjoin(clsnames(iRow));
    duration_ms = duration_mss(iRow);
    forwardoffset = forwardoffsets(iRow);
    AutogenLblsByMeta(srcFolder,meta_subtype,clsname,duration_ms,forwardoffset);
end


% 关闭并行池
delete(gcp);

% 停止计时器并获取经过的时间
elapsedTime = toc;
disp(['程序执行时间: ', num2str(round(elapsedTime/60,2),'%.2f'), '分']);